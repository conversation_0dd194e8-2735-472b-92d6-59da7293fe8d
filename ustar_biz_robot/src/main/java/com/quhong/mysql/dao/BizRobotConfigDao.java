package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quhong.constant.RobotConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.BizRobotConfigData;
import com.quhong.mysql.mapper.ustar.BizRobotConfigMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BizRobotConfigDao extends ServiceImpl<BizRobotConfigMapper, BizRobotConfigData> {

    private static final Logger logger = LoggerFactory.getLogger(BizRobotConfigDao.class);


    public List<BizRobotConfigData> getConfigList() {
        return lambdaQuery()
                .in(BizRobotConfigData::getStatus, RobotConstant.RUNNING, RobotConstant.STOPPING)
                .list();
    }

    public List<BizRobotConfigData> getRunningConfigList() {
        return lambdaQuery()
                .eq(BizRobotConfigData::getStatus, RobotConstant.RUNNING)
                .list();
    }

    public void updateStatus(int id, int status) {
        logger.info("update biz robot config status, id={} status={}", id, status);
        LambdaUpdateWrapper<BizRobotConfigData> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(BizRobotConfigData::getId, id)
                .set(BizRobotConfigData::getStatus, status)
                .set(BizRobotConfigData::getMtime, DateHelper.getNowSeconds());
        update(lambdaUpdateWrapper);
    }

}
