package com.quhong.enums;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public interface Cmd {
    // 密码房全房间需要过滤的消息
    Set<Integer> PWD_FILTER_SET = new HashSet<>(Arrays.asList(2100, 2106, 2074, 2102, 2134,
            2108, 2170, 2172, 2174, 2096, 2178, 2194, 2116, 2210, 2204, 2250));
    // 密码房触发的房间房间广播，只在触发的房间展示，其他房间不展示。广播类型：大礼物广播、PK广播
    Set<Integer> PWD_ROOM_SET = new HashSet<>(Arrays.asList(2194, 2116, 2210, 2204, 2174, 2134, 2250));
    // mars心跳
    int MARS_HEART = 6;

    // 握手消息New
    int HAND_SHAKE = 1;
    // 握手消息
    int LOGIN = 1001;
    // 登录回包
    int LOGIN_PUSH = 1002;
    // 心跳
    int HEART_BEAT = 1003;
    // 心跳回包
    int HEART_BEAT_ACK = 1004;
    // 客户端收到消息确认
    int CLIENT_RECV_ACK = 1005;
    // 场景切换
    int CHANGE_SCENE = 1007;
    // 强制下线
    int KICK_OUT = 1016;
    // 功能场景
    int FUNC_SCENE = 1023;

    int ROOM_START = 2000;
    int ROOM_END = 3000;
    // 进入房间
    int ENTER_ROOM_REQ = 2001;
    int ENTER_ROOM_PUSH = 2002;
    // 离开房间
    int LEAVE_ROOM_REQ = 2003;
    // 房间消息
    int SEND_TEXT_MSG = 2005;
    int SEND_TEXT_MSG_PUSH = 2006;
    // 礼物消息
    int ROOM_GIFT_MSG_PUSH = 2008;
    // 幸运转盘推送
    int LUCKY_WHEEL_ROOM_PUSH = 2010;
    // 房间幸运宝箱推送
    int LUCKY_BOX_ROOM_PUSH = 2012;
    // 房间幸运数字推送
    int LUCKY_NUM_ROOM_PUSH = 2014;
    // 房间骰子推送
    int DICE_ROOM_PUSH = 2016;
    // 解散房间推送
    int DISMISS_ROOM_PUSH = 2018;
    // 用户进出房间通知
    int ROOM_USER_CHANGE_PUSH = 2020;
    // 关注房主
    int FOLLOW_ROOM_OWNER_PUSH = 2022;
    // 授予播放音乐权限
    int OPEN_MUSIC_LIVE_PUSH = 2024;
    // 授予摄像头权限
    int OPEN_CAM_LIVE_PUSH = 2026;
    // 房主切换房间类型
    int CHANGE_ROOM_TYPE_PUSH = 2028;
    // 成为房间会员
    int MEMBER_PUSH = 2030;
    // 麦位管理
    int MUTE_MIC_PUSH = 2032;
    // 锁卖解锁
    int LOCK_MIC_PUSH = 2034;
    // 禁言，解除禁言
    int OPT_BAN_TXT_PUSH = 2036;
    // 创建猜拳游戏
    int CREATE_FINGER_GUESS_PUSH = 2038;
    // 邀请上麦
    int INVITE_MIC_PUSH = 2040;
    // 踢下麦
    int REMOVE_SEAT_PUSH = 2042;
    // 房间公告
    int ROOM_ANNOUNCE_PUSH = 2044;
    // 关注房间
    int FOLLOW_ROOM_PUSH = 2046;
    // 打开，关闭房间公屏消息
    int OPT_ROOM_CHAT_PUSH = 2048;
    // 打开，关闭房间发图片功能
    int OPT_SEND_PIC_PUSH = 2050;
    // 房间发文字消息等级限制
    int TXT_LIMIT_PUSH = 2052;
    // 打开关闭房间PK 功能
    int OPT_PK_PUSH = 2054;
    // 操作所有麦位
    int OPT_ALL_MIC_PUSH = 2056;
    // emoji表情
    int ROOM_EMOJI_PUSH = 2058;
    // 设置房间锁
    int ROOM_CHANGE_PWD_PUSH = 2060;
    // 发图片
    int SEND_PIC_PUSH = 2062;
    // 真心话大冒险
    int TRUTH_DARE_GAME_PUSH = 2064;
    // mars房间开关
    int MARS_SWITCH_PUSH = 2066;
    // 麦位变化
    int ROOM_MIC_CHANGE = 2070;
    // 全房间礼物单词推送
    int ROOM_EVERY_ONE_GIFT_SINGLE_PUSH = 2084;
    // 踢人消息
    int KICK_FROM_ROOM = 2090;
    // 礼物红包
    int GIFT_BOX_PUSH = 2092;
    // 排行榜推送
    int RANK_NOTIFICATION_PUSH = 2094;
    // 创建房间通知
    int CREATE_ROOM_NOTIFY_MESSAGE = 2098;
    // 房间抽奖中大奖广播通知（全房间通知）
    int ROOM_LOTTERY_NOTIFY_MESSAGE = 2100;
    // 猜拳结果
    int ROOM_ROSHAMBO_RESULT_MESSAGE = 2102;
    // 转盘入口数量发生变化
    int ROOM_ROSHAMBO_ENTRANCE_CHANGE_MESSAGE = 2104;
    // 房间幸运礼物中奖通知
    int ROOM_LUCKY_GIFT_WIN_LOTTERY_MESSAGE = 2106;
    // 房间幸运礼物中奖奖励
    int ROOM_LUCK_GIFT_REWARD_MESSAGE = 2108;
    // 排行榜广播推送
    int ACTIVITY_BROADCAST_MSG = 2096;
    // 房间PK
    int ROOM_ROOM_PK_MSG = 2114;
    // 房间PK通知我的好友来助威
    int ROOM_ROOM_PK_NOTIFY_FRIENDS_MSG = 2116;
    // 房间PK开始通知
    int ROOM_ROOM_PK_START_NOTIFY_MSG = 2118;
    // 房间PK邀请通知
    int ROOM_PK_INVITE_MSG = 2120;
    // 麦位PK
    int ROOM_MIC_PK_MSG = 2122;
    // 麦位PK邀请
    int ROOM_MIC_PK_INVITE_MSG = 2124;
    // 房间征服活动
    int ROOM_CONQUER_MESSAGE = 2128;
    // 房间公屏通知消息下发
    int ROOM_NOTIFICATION_PUSH = 2134;
    // 房间Lodo列表变更
    int ROOM_LUDO_CHANGE_MSG = 2138;
    // 房间视频信息推送
    int ROOM_VIDEO_INFO_MSG = 2142;
    // 切换视频开关
    int VIDEO_SWITCH_PUSH = 2144;
    // ludo游戏状态更新推送
    int ROOM_LUDO_STATUS_CHANGE_MSG = 2146;
    // ludo游戏踢人推送
    int ROOM_LUDO_KIT_OUT_MSG = 2148;
    // 房间视频推荐
    int ROOM_SUGGEST_VIDEO = 2150;
    // 房间设置开关
    int ROOM_GREET_SWITCH_CHANGE = 2152;
    // 新用户进房间推荐关注用户 2154
    int ROOM_RECOMMEND_FOLLOW_MSG = 2154;
    // 新用户被关注 2158
    int ROOM_FOLLOW_ME_MSG = 2158;
    // 新用户收到加好友请求 2160
    int ROOM_ADD_FRIENDS_MSG = 2160;
    // 关注房间提示消息 2162
    int ROOM_FOLLOW_ROOM_TIPS_MSG = 2162;
    // 关注房间提示消息
    int ROOM_NOVICE_FINISH = 2164;
    // 房间内麦位主题更改mars消息
    int ROOM_MIC_THEME_CHANGE = 2166;
    // 房间信息更改通知 2168
    int ROOM_INFO_CHANGE = 2168;
    // 发送平台礼物  代表平台礼物横幅消息
    int SEND_PLATFORM_GIFT = 2170;
    // 房间排名更改消息
    int ROOM_RANK_CHANGE_MSG = 2172;
    // 平台礼物 代表平台礼物消息（其他房间需要播放该礼物消息）
    int ROOM_PLATFORM_GIFT_MSG = 2174;
    // 开通女性VIP广播通知
    int ROOM_QUEEN_VIP_ACTIVE_MSG = 2178;
    // 水果游戏状态改变推送
    int FRUIT_PARTY_STATUS_CHANGE_MSG = 2180;
    // 新用户引导回应消息
    int NEW_USER_ONBOARDING_RESPONSE_MSG = 2182;
    // 房间内即构游戏操作消息
    int ROOM_SUD_GAME_OPERATE_MSG = 2186;
    // 房间内即构游戏超时结束消息
    int ROOM_SUD_GAME_TIME_OUT_MSG = 2188;
    // sud小游戏组队页面变更消息
    int SUD_GAME_CHANGE_MSG = 2190;
    // sud小游戏游戏结果消息
    int SUD_GAME_RESULT_MSG = 2192;
    // 新版大礼物
    int BIG_GIFT_MSG = 2194;
    // 房间操作消息
    int ROOM_OPT_MSG = 2196;
    // 房间幸运数字修改推送
    int LUCKY_NUM_ROOM_CHANGE_MSG = 2198;
    // 房间火箭发射消息
    int ROOM_ROCKET_LAUNCH_MSG = 2202;
    // 房间火箭发射平台消息
    int ROCKET_LAUNCH_PLATFORM_MSG = 2204;
    // 获得房间火箭奖励消息
    int GET_ROOM_ROCKET_REWARD_MSG = 2206;
    // 房间火箭能量进度改变消息
    int ROCKET_PROGRESS_CHANGE_MSG = 2208;
    // 大红包全平台通知
    int BIG_LUCKY_BOX_MSG = 2210;
    // 房间活动数量改变消息
    int ROOM_EVENT_NUM_CHANGE_MSG = 2212;
    // 房间召集消息
    int ROOM_GATHERING_MSG = 2214;
    // 麦位申请列表变更
    int MIC_APPLY_CHANGE_MSG = 2216;
    // 房间召集新记录
    int ROOM_GATHERING_NEW_RECORD_MSG = 2218;
    // 麦位申请已被取消
    int MIC_APPLY_CANCEL_MSG = 2220;
    // 房间投票消息
    int ROOM_VOTE_MSG = 2222;
    // 房间hot排名及贡献榜变化消息
    int ROOM_HOT_DEVOTE_CHANGE_MSG = 2224;
    // 进阶礼物消息推送
    int ADVANCED_GIFT_MSG = 2228;
    // 房间点赞消息
    int ROOM_LIKE_MSG = 2230;
    // 聊天大厅消息
    int CHAT_HALL_MSG = 2232;
    // 钻石版本幸运礼物中奖消息
    int LUCKY_GIFT_REWARD_MSG = 2234;
    // 新版全平台大礼物广播
    int GLOBAL_BIG_GIFT_MSG = 2236;
    // 房间等级升级广播
    int ROOM_LEVEL_UP_MSG = 2238;
    // 用户任务完成消息
    int USER_TASK_COMPLETION_MSG = 2240;
    // 房间内用户排行榜变化
    int USER_ROOM_RANK_MSG = 2242;
    // 钻石版幸运礼物中奖广播
    int LUCKY_GIFT_BROADCAST_MSG = 2244;
    // 幸运骰子消息
    int LUCKY_DICE_MSG = 2246;
    // 首充广播公屏通知
    int ROOM_FIRST_RECHARGE_MSG = 2248;
    // 房间通用中奖广播消息
    int ROOM_COMMON_SCROLL_MSG = 2250;
    // 房间火箭发射平台消息
    int ROCKET_V2_LAUNCH_PLATFORM_MSG = 2252;
    // 在房邀请好友玩游戏等消息
    int ROOM_INVITE_USER_MSG = 2254;
    // 房间火箭获得奖励推送 2256
    int ROCKET_V2_REWARD_MSG = 2256;
    // 房间内真心话大冒险主题操作消息
    int TRUTH_DARE_THEME_MSG = 2260;
    // 用户任务(web版)完成消息
    int USER_WEB_TASK_COMPLETION_MSG = 2262;
    // 用户(web版本)已领取全部奖励
    int USER_WEB_TASK_NO_REWARD_MSG = 2264;
    // 用户监控推送
    int USER_MONITOR_PUSH = 3002;
    // 系统通知
    int ERROR_NOTIFICATION = 3004;
    // 设置为副房主
    int ROOM_VICE_HOST = 3008;
    // 取消副房主或管理员
    int CANCEL_ADMIN_MESSAGE = 3010;
    // 朋友圈好友新动态更新
    int MOMENTS_FRIEND_WHAT_NEW_MSG = 3012;
    // 朋友圈被点赞
    int MOMENTS_LIKE_MSG = 3014;
    // 用户等级升级通知
    int USER_LEVEL_UP_MSG = 3016;
    // 新用户获得经验值toast提示
    int NEW_USER_EXP_TOAST_MSG = 3018;
    // 上麦时给最近匹配的好友推送通知
    int MATCH_FRIEND_NOTICE_MSG = 3026;
    // 主页有新访客
    int NEW_VISITOR_MSG = 3028;
    // admin开通、续费通Queen通知
    int QUEEN_VIP_ACTIVE_MSG = 3030;
    // 获取背包礼物通知
    int GET_GIFT_BAG_MSG = 3032;
    // 粉丝数变动消息
    int FOLLOW_CHANGE_MSG = 3034;
    // 用户获取了新的资源
    int NEW_GET_RESOURCE_MSG = 3036;
    // 礼物冠名消息变化
    int GIFT_NAMING_MSG = 3038;
    // 客户端日志上报消息
    int CLIENT_LOG_REPORT_MSG = 3040;
    // 解锁礼物倒计时结束礼物切换消息
    int UNLOCK_GIFT_MSG = 3044;
    // 聊天气泡改变通知 cmdid = 3046
    int BUBBLE_CHANGE_MSG = 3046;
    // 举报私信违规时隐藏消息 cmdid = 3048
    int REPORT_HIDDEN_MSG = 3048;
    //  用户通用顶部弹窗消息
    int USER_COMMON_POP_UP_MSG = 3050;
    //  迎新房用户进房推荐给迎新员工 推送3052
    int JOIN_ROOKIE_ROOM_MSG = 3052;
    // 私信消息推送
    int CHAT_MSG_PUSH = 4002;
    // 撤回私信消息
    int RECALL_CHAT_MSG = 4004;
    // 点赞私信消息
    int LIKE_CHAT_MSG = 4006;
    // 关注通知消息
    int NOTICE_MSG = 4008;
    // 新朋友添加消息
    int FRIEND_APPLY_MSG = 4014;
    // 官方消息
    int OFFICIAL_MSG = 4016;
}
