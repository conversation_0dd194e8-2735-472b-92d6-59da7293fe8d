package com.quhong.enums;

/**
 * <AUTHOR>
 * @date 2024/3/25
 */
public class EventGameTypeConstant {

    /**
     * 游戏类型 2：新ludo游戏 3:转盘 4：猜拳游戏 5：umo 6:Fishing 7:horseRacing 8:Crash
     * 9:消消乐 10:Slots 11:多米诺 12:Fast3 13greedy 14animalParty 15BigBattle 16Slots 17FruitParty...
     * 自研游戏预留: 13~50，自研游戏预留
     *
     * @see com.quhong.service.QhGameService#init()
     */
    public static final int LUDO = 2;
    public static final int TURNTABLE = 3;
    public static final int FINGER_GUESS = 4;
    public static final int UMO = 5;
    public static final int FISHING = 6;
    public static final int HORSE_RACING = 7;
    public static final int CRASH = 8;
    public static final int MONSTER_CRUSH = 9;
    public static final int SLOTS = 10;
    public static final int DOMINO = 11;
    public static final int FAST3 = 12;
    // 自研游戏预留: 13~50
    public static final int CARROM_POOL = 51; // 克罗姆
    public static final int BILLIARD_POOL = 52; // 桌球-8球
    public static final int JACKAROO_POOL = 53; // Jackaroo游戏
    public static final int BALOOT_POOL = 54; // Baloot游戏
}
