package com.quhong.analysis;

/**
 * 房间消息
 */
public class RoomMsgNewEvent extends UserEvent {
    public String room_id;  // 房间id
    public int msg_type;  // 消息类型 1为文本 2为@他人消息
    public int send_user_count;  // 发送人数 (不包含悬浮窗人数)
    public int room_user_count;  // 房间当前人数
    public String msg_id;  // 消息id
    public int from_os;  // 发送人操作系统
    public String from_uid;  // 发送人uid
    public String to_uid;  // 收到打招呼消息用户id
    public int send_msg_type; // 0房间消息，1聊天大厅
    public int ctime;  // 数据创建时间

    @Override
    public String getEventName() {
        return "room_msg_new";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public int getMsg_type() {
        return msg_type;
    }

    public void setMsg_type(int msg_type) {
        this.msg_type = msg_type;
    }

    public int getSend_user_count() {
        return send_user_count;
    }

    public void setSend_user_count(int send_user_count) {
        this.send_user_count = send_user_count;
    }

    public int getRoom_user_count() {
        return room_user_count;
    }

    public void setRoom_user_count(int room_user_count) {
        this.room_user_count = room_user_count;
    }

    public String getMsg_id() {
        return msg_id;
    }

    public void setMsg_id(String msg_id) {
        this.msg_id = msg_id;
    }

    public int getFrom_os() {
        return from_os;
    }

    public void setFrom_os(int from_os) {
        this.from_os = from_os;
    }

    public String getFrom_uid() {
        return from_uid;
    }

    public void setFrom_uid(String from_uid) {
        this.from_uid = from_uid;
    }

    public String getTo_uid() {
        return to_uid;
    }

    public void setTo_uid(String to_uid) {
        this.to_uid = to_uid;
    }

    public int getSend_msg_type() {
        return send_msg_type;
    }

    public void setSend_msg_type(int send_msg_type) {
        this.send_msg_type = send_msg_type;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
