package com.quhong.msg.obj;

import com.quhong.core.msg.IProto;
import com.quhong.proto.YoustarProtoUser;

public class LevelRewardObject implements IProto<YoustarProtoUser.LevelRewardInfo> {
    private String name;
    private String url;

    @Override
    public void doFromBody(YoustarProtoUser.LevelRewardInfo proto) {
        this.name = proto.getName();
        this.url = proto.getUrl();
    }

    @Override
    public YoustarProtoUser.LevelRewardInfo.Builder doToBody() {
        YoustarProtoUser.LevelRewardInfo.Builder builder = YoustarProtoUser.LevelRewardInfo.newBuilder();
        builder.setName(this.name == null ? "" : this.name);
        builder.setUrl(this.url == null ? "" : this.url);
        return builder;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
