package com.quhong.msg.obj;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.msg.IProto;
import com.quhong.proto.YoustarProtoUname;

public class UserInfoObject implements IProto<YoustarProtoUname.UserInfo> {
    private String uid;
    private String name;
    private String head;
    private int viceHost;

    public void fillFrom(JSONObject src){
        this.uid = src.getString("uid");
        this.name = src.getString("name");
        this.head = src.getString("head");
        this.viceHost = src.getIntValue("viceHost");
    }

    @Override
    public void doFromBody(YoustarProtoUname.UserInfo proto) {
        this.uid = proto.getUid();
        this.name = proto.getName();
        this.head = proto.getHead();
        this.viceHost = proto.getViceHost();
    }

    @Override
    public YoustarProtoUname.UserInfo.Builder doToBody() {
        YoustarProtoUname.UserInfo.Builder builder = YoustarProtoUname.UserInfo.newBuilder();
        builder.setUid(uid == null ? "" : uid);
        builder.setName(name == null ? "" : name);
        builder.setHead(head == null ? "" : head);
        builder.setViceHost(viceHost);
        return builder;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public int getViceHost() {
        return viceHost;
    }

    public void setViceHost(int viceHost) {
        this.viceHost = viceHost;
    }
}
