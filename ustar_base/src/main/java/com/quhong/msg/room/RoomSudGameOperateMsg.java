package com.quhong.msg.room;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.PKUserInfoObject;
import com.quhong.msg.obj.UNameObject;
import com.quhong.proto.YoustarProtoRoom;

/**
 * <AUTHOR>
 * @date 2022/7/4
 */
@Message(cmd = Cmd.ROOM_SUD_GAME_OPERATE_MSG)
public class RoomSudGameOperateMsg extends MarsServerMsg {

    private UNameObject opt_user;
    private int opt;  // 操作 1创建游戏 2关闭游戏 4开始游戏 5 remind 提示消息
    private int game_type; // 游戏类型
    private String game_icon;  // 游戏图标
    private String msg;  // 阿语消息内容
    private String msg_ar;  // 阿语消息内容

    @Override
    public void fillFrom(JSONObject object) {
        JSONObject userObj = object.getJSONObject("opt_user");
        if (userObj != null) {
            opt_user = new UNameObject();
            opt_user.fillFrom(userObj);
        }
        this.opt = object.getIntValue("opt");
        this.game_type = object.getIntValue("game_type");
        this.game_icon = object.getString("game_icon");
        this.msg = object.getString("msg");
        this.msg_ar = object.getString("msg_ar");
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.RoomSudGameOperateMessage msg = YoustarProtoRoom.RoomSudGameOperateMessage.parseFrom(bytes);
        if (msg.getOptUser() != null) {
            this.opt_user = new UNameObject();
            this.opt_user.doFromBody(msg.getOptUser());
        }
        this.protoHeader.doFromBody(msg.getHeader());
        this.opt = msg.getOpt();
        this.game_type = msg.getGameType();
        this.game_icon = msg.getGameIcon();
        this.msg = msg.getMsg();
        this.msg_ar = msg.getMsgAr();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.RoomSudGameOperateMessage.Builder builder = YoustarProtoRoom.RoomSudGameOperateMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        if (this.opt_user != null) {
            builder.setOptUser(this.opt_user.doToBody());
        }
        builder.setOpt(this.opt);
        builder.setGameType(this.getGame_type());
        builder.setGameIcon(this.game_icon);
        builder.setMsg(this.msg);
        builder.setMsgAr(this.msg_ar);
        return builder.build().toByteArray();
    }

    public int getOpt() {
        return opt;
    }

    public void setOpt(int opt) {
        this.opt = opt;
    }

    public String getGame_icon() {
        return game_icon;
    }

    public void setGame_icon(String game_icon) {
        this.game_icon = game_icon;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getMsg_ar() {
        return msg_ar;
    }

    public void setMsg_ar(String msg_ar) {
        this.msg_ar = msg_ar;
    }

    public int getGame_type() {
        return game_type;
    }

    public void setGame_type(int game_type) {
        this.game_type = game_type;
    }

    public UNameObject getOpt_user() {
        return opt_user;
    }

    public void setOpt_user(UNameObject opt_user) {
        this.opt_user = opt_user;
    }
}
