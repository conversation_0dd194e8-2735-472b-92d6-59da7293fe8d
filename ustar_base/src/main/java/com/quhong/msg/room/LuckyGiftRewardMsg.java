package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.YoustarProtoRoom;

@Message(cmd = Cmd.LUCKY_GIFT_REWARD_MSG)
public class LuckyGiftRewardMsg extends MarsServerMsg {

    private int reward; // 中奖钻石数
    private int jackpot; // 奖池大小

    public LuckyGiftRewardMsg() {
    }

    public LuckyGiftRewardMsg(int reward, int jackpot) {
        this.reward = reward;
        this.jackpot = jackpot;
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.LuckyGiftRewardMessage msg = YoustarProtoRoom.LuckyGiftRewardMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.reward = msg.getReward();
        this.jackpot = msg.getJackpot();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.LuckyGiftRewardMessage.Builder builder = YoustarProtoRoom.LuckyGiftRewardMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setReward(reward);
        builder.setJackpot(jackpot);
        return builder.build().toByteArray();
    }

    public int getReward() {
        return reward;
    }

    public void setReward(int reward) {
        this.reward = reward;
    }

    public int getJackpot() {
        return jackpot;
    }

    public void setJackpot(int jackpot) {
        this.jackpot = jackpot;
    }
}
