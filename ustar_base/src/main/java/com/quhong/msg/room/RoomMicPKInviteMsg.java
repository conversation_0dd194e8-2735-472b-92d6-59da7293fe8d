package com.quhong.msg.room;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.YoustarProtoRoom;

@Message(cmd = Cmd.ROOM_MIC_PK_INVITE_MSG)
public class RoomMicPKInviteMsg extends MarsServerMsg {
    private String pid;
    private String gicon;
    private String creator_uid;
    private String room_id;
    private String name;
    private String head;
    private int vip;
    private int total_time;
    private String msg_id;
    private int identify;

    @Override
    public void fillFrom(JSONObject data) {
        data = data.getJSONObject("data");
        if (null == data) {
            return;
        }
        this.pid = data.getString("pid");
        this.gicon = data.getString("gicon");
        this.creator_uid = data.getString("creator_uid");
        this.room_id = data.getString("room_id");
        this.name = data.getString("name");
        this.head = data.getString("head");
        this.vip = data.getIntValue("vip");
        this.total_time = data.getIntValue("total_time");
        this.msg_id = data.getString("msg_id");
        this.identify = data.getIntValue("identify");
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.RoomMicPKInviteMessage msg = YoustarProtoRoom.RoomMicPKInviteMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.pid = msg.getPid();
        this.gicon = msg.getGicon();
        this.creator_uid = msg.getCreatorUid();
        this.room_id = msg.getRoomId();
        this.name = msg.getName();
        this.head = msg.getHead();
        this.vip = msg.getVip();
        this.total_time = msg.getTotalTime();
        this.msg_id = msg.getMsgId();
        this.identify = msg.getIdentify();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.RoomMicPKInviteMessage.Builder builder = YoustarProtoRoom.RoomMicPKInviteMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setPid(pid == null ? "" : pid);
        builder.setGicon(gicon == null ? "" : gicon);
        builder.setCreatorUid(creator_uid == null ? "" : creator_uid);
        builder.setRoomId(room_id == null ? "" : room_id);
        builder.setName(name == null ? "" : name);
        builder.setHead(head == null ? "" : head);
        builder.setVip(vip);
        builder.setTotalTime(total_time);
        builder.setMsgId(msg_id == null ? "" : msg_id);
        builder.setIdentify(identify);
        return builder.build().toByteArray();
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getGicon() {
        return gicon;
    }

    public void setGicon(String gicon) {
        this.gicon = gicon;
    }

    public String getCreator_uid() {
        return creator_uid;
    }

    public void setCreator_uid(String creator_uid) {
        this.creator_uid = creator_uid;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public int getVip() {
        return vip;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }

    public int getTotal_time() {
        return total_time;
    }

    public void setTotal_time(int total_time) {
        this.total_time = total_time;
    }

    public String getMsg_id() {
        return msg_id;
    }

    public void setMsg_id(String msg_id) {
        this.msg_id = msg_id;
    }

    public int getIdentify() {
        return identify;
    }

    public void setIdentify(int identify) {
        this.identify = identify;
    }
}
