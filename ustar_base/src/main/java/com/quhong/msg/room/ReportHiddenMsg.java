package com.quhong.msg.room;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.YoustarProtoUser;

@Message(cmd = Cmd.REPORT_HIDDEN_MSG)
public class ReportHiddenMsg extends MarsServerMsg {
    private String messageId;

    @Override
    public void fillFrom(JSONObject data) {
        this.messageId = data.getString("messageId");
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoUser.ReportHiddenMessage msg = YoustarProtoUser.ReportHiddenMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.messageId = msg.getMessageId();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoUser.ReportHiddenMessage.Builder builder = YoustarProtoUser.ReportHiddenMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setMessageId(messageId);
        return builder.build().toByteArray();
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }
}
