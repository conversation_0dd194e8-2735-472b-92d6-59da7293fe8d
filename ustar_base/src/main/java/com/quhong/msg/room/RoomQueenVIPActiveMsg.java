package com.quhong.msg.room;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.RidInfoObject;
import com.quhong.proto.YoustarProtoRoom;

@Message(cmd = Cmd.ROOM_QUEEN_VIP_ACTIVE_MSG)
public class RoomQueenVIPActiveMsg extends MarsServerMsg {
    private String uid;
    private String head;
    private String rid;
    private RidInfoObject ridInfo;
    private String name;
    private String title;
    private String title_ar;


    @Override
    public void fillFrom(JSONObject data) {
        this.uid = data.getString("uid");
        this.head = data.getString("head");
        this.rid = data.getString("rid");
        this.name = data.getString("name");
        this.title = data.getString("title");
        this.title_ar = data.getString("title_ar");
        JSONObject ridInfo = data.getJSONObject("ridInfo");
        if(ridInfo != null){
            this.ridInfo = new RidInfoObject();
            this.ridInfo.fillFrom(ridInfo);
        }
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.RoomQueenVIPActiveMessage msg = YoustarProtoRoom.RoomQueenVIPActiveMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.uid = msg.getUid();
        this.head = msg.getHead();
        this.rid = msg.getRid();
        this.name = msg.getName();
        this.title = msg.getTitle();
        this.title_ar = msg.getTitleAr();
        this.ridInfo = new RidInfoObject();
        this.ridInfo.doFromBody(msg.getRidInfo());
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.RoomQueenVIPActiveMessage.Builder builder = YoustarProtoRoom.RoomQueenVIPActiveMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setUid(uid == null ? "" : uid);
        builder.setHead(head == null ? "" : head);
        builder.setRid(rid == null ? "" : rid);
        builder.setName(name == null ? "" : name);
        builder.setTitle(title == null ? "" : title);
        builder.setTitleAr(title_ar == null ? "" : title_ar);
        if (this.ridInfo != null) {
            builder.setRidInfo(this.ridInfo.doToBody());
        }
        return builder.build().toByteArray();
    }

    @Override
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle_ar() {
        return title_ar;
    }

    public void setTitle_ar(String title_ar) {
        this.title_ar = title_ar;
    }

    public RidInfoObject getRidInfo() {
        return ridInfo;
    }

    public void setRidInfo(RidInfoObject ridInfo) {
        this.ridInfo = ridInfo;
    }
}
