package com.quhong.msg.room;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.UNameObject;
import com.quhong.proto.YoustarProtoRoom;

@Message(cmd = Cmd.BIG_LUCKY_BOX_MSG)
public class BigLuckyBoxMsg extends MarsServerMsg {

    private String fromRoomId; // 发送者所在房间id
    private String fromRid;   //发送者rid
    private String fromName;   //发送者名字
    private String fromHead;  //发送者头像
    private int fromVipLevel; //发送者vip等级
    private String boxId; //红包id
    private int boxType; // 0 钻石红包,1 礼物红包


    @Override
    public void fillFrom(JSONObject object) {
        JSONObject unameObject = object.getJSONObject("user_info");
        if (unameObject != null) {
            this.fromRid = unameObject.getString("rid");
            this.fromName = unameObject.getString("name");
            this.fromHead = unameObject.getString("head");
            this.fromVipLevel = unameObject.getIntValue("vip");
        }
        this.fromRoomId = object.getString("room_id");
        this.boxId = object.getString("box_id");
        this.boxType = object.getIntValue("box_type");
    }


    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.BigLuckBoxMessage msg = YoustarProtoRoom.BigLuckBoxMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.fromRoomId = msg.getFromRoomId();
        this.fromRid = msg.getFromRid();
        this.fromName = msg.getFromName();
        this.fromHead = msg.getFromHead();
        this.fromVipLevel = msg.getFromVipLevel();
        this.boxId = msg.getBoxId();
        this.boxType = msg.getBoxType();

    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.BigLuckBoxMessage.Builder builder = YoustarProtoRoom.BigLuckBoxMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setFromRoomId(this.fromRoomId == null ? "" : this.fromRoomId);
        builder.setFromRid(this.fromRid == null ? "" : this.fromRid);
        builder.setFromName(this.fromName == null ? "" : this.fromName);
        builder.setFromHead(this.fromHead == null ? "" : this.fromHead);
        builder.setFromVipLevel(this.fromVipLevel);
        builder.setBoxId(this.boxId == null ? "" : this.boxId);
        builder.setBoxType(this.boxType);
        return builder.build().toByteArray();
    }

    public String getFromRoomId() {
        return fromRoomId;
    }

    public void setFromRoomId(String fromRoomId) {
        this.fromRoomId = fromRoomId;
    }

    public String getFromRid() {
        return fromRid;
    }

    public void setFromRid(String fromRid) {
        this.fromRid = fromRid;
    }

    public String getFromName() {
        return fromName;
    }

    public void setFromName(String fromName) {
        this.fromName = fromName;
    }

    public String getFromHead() {
        return fromHead;
    }

    public void setFromHead(String fromHead) {
        this.fromHead = fromHead;
    }

    public int getFromVipLevel() {
        return fromVipLevel;
    }

    public void setFromVipLevel(int fromVipLevel) {
        this.fromVipLevel = fromVipLevel;
    }

    public String getBoxId() {
        return boxId;
    }

    public void setBoxId(String boxId) {
        this.boxId = boxId;
    }

    public int getBoxType() {
        return boxType;
    }

    public void setBoxType(int boxType) {
        this.boxType = boxType;
    }
}
