package com.quhong.msg;

import com.quhong.proto.YoustarProtoBase;
import io.netty.buffer.ByteBuf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GateMsg extends MarsMsg {
    private static final Logger logger = LoggerFactory.getLogger(GateMsg.class);

    private ProtoHeader protoHeader;
    private byte[] body;

    public GateMsg() {

    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        this.body = bytes;
        YoustarProtoBase.GateMsg msg = YoustarProtoBase.GateMsg.parseFrom(bytes);
        protoHeader = new ProtoHeader();
        protoHeader.doFromBody(msg.getHeader());
    }

    @Override
    protected byte[] doToBody() throws Exception {
        return this.body != null ? this.body : new byte[0];
    }

    public byte[] getBody() {
        return body;
    }

    public void setBody(byte[] body) {
        try {
            doFromBody(body);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    public ProtoHeader getProtoHeader() {
        return protoHeader;
    }

    public void setProtoHeader(ProtoHeader protoHeader) {
        this.protoHeader = protoHeader;
    }
}
