package com.quhong.lang;

public class LanguageData {
    private String[] msgArr;
    private int code;

    public LanguageData(int code, String[] msgArr){
        this.code = code;
        this.msgArr = msgArr;
    }

    public String[] getMsgArr() {
        return msgArr;
    }

    public String getMsg(int slang){
        slang = slang - 1;
        if(slang < 0){
            slang = 0;
        }
        if(msgArr == null){
            return "";
        }
        if(slang > msgArr.length){
            return msgArr[0];
        }
        return msgArr[slang];
    }

    public int getCode() {
        return code;
    }
}
