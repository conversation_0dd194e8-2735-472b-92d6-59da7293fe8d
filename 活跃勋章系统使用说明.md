# 活跃勋章系统使用说明

## 功能概述

活跃勋章系统是一个长期活动，旨在鼓励用户持续活跃。用户通过连续满足每日麦上时长要求来累积活跃天数，达到特定天数可解锁对应等级的勋章。

## 核心功能

### 1. 活跃天数统计
- 根据用户每日麦上时长计算活跃天数
- 不同累积天数阶段有不同的麦上时长要求：
  - 第1-3天：需要上麦15分钟
  - 第4-7天：需要上麦20分钟  
  - 第8-15天：需要上麦25分钟
  - 第16-90天：需要上麦30分钟

### 2. 勋章等级
共有6个等级勋章：
- 3天活跃勋章
- 7天活跃勋章
- 15天活跃勋章
- 30天活跃勋章
- 60天活跃勋章
- 90天活跃勋章

### 3. 规则说明
- 任务中断则重新开始累积天数
- 每次达标都自动下发勋章
- 勋章显示格式：{下发日期}已解锁，取最新下发日期
- 用户累积活跃90天后，次日从第一天开始重新累积

### 4. 互动功能
- 点击GO随机跳转同国家在开播异性房间
- 每天沙特时间20:00推送给当天未有麦位互动行为的用户提醒消息

## API接口

### 1. 获取用户活跃勋章信息
```http
GET /activity/activeBadge/info?uid={用户ID}
```

响应示例：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "accumulateDays": 5,
    "daysToNextBadge": 2,
    "nextBadgeDays": 7,
    "todayRequiredMicTime": 20,
    "todayMicTime": 15,
    "todayTaskCompleted": false,
    "badgeList": [
      {
        "level": 3,
        "unlocked": true,
        "unlockDate": "2023.05.27已解锁",
        "iconUrl": "https://static.youstar.live/badges/active_3_days.png",
        "name": "3天活跃勋章"
      }
    ]
  }
}
```

### 2. 获取随机房间
```http
POST /activity/activeBadge/randomRoom?uid={用户ID}
```

响应示例：
```json
{
  "code": 0,
  "msg": "success", 
  "data": "room123456"
}
```

## 核心类说明

### 1. ActiveBadgeService
主要业务逻辑处理类，包含：
- 麦上时长处理逻辑
- 活跃天数计算
- 勋章解锁判断
- 随机房间推荐

### 2. ActiveBadgeRedis
Redis操作类，负责：
- 用户累积天数存储
- 每日麦上时长记录
- 勋章解锁记录管理
- 最后活跃日期跟踪

### 3. ActiveBadgeController
HTTP接口控制器，提供：
- 用户活跃信息查询接口
- 随机房间获取接口

### 4. ActiveBadgeScheduleTask
定时任务类，处理：
- 每日20:00推送提醒消息
- 过期数据清理

## 配置项

### 1. 勋章ID映射
在ActiveBadgeService中配置：
```java
private static final Map<Integer, Integer> BADGE_ID_MAP = new HashMap<>();
static {
    BADGE_ID_MAP.put(3, 1001);   // 3天勋章ID
    BADGE_ID_MAP.put(7, 1002);   // 7天勋章ID  
    BADGE_ID_MAP.put(15, 1003);  // 15天勋章ID
    BADGE_ID_MAP.put(30, 1004);  // 30天勋章ID
    BADGE_ID_MAP.put(60, 1005);  // 60天勋章ID
    BADGE_ID_MAP.put(90, 1006);  // 90天勋章ID
}
```

### 2. 麦上时长要求
在ActiveBadgeConstant中配置：
```java
public static final int MIC_TIME_LEVEL_1_3 = 15;    // 第1-3天
public static final int MIC_TIME_LEVEL_4_7 = 20;    // 第4-7天
public static final int MIC_TIME_LEVEL_8_15 = 25;   // 第8-15天
public static final int MIC_TIME_LEVEL_16_90 = 30;  // 第16-90天
```

## 集成说明

### 1. 依赖服务
- Redis服务：存储用户活跃数据
- 勋章系统：发放勋章奖励
- 推送服务：发送提醒消息
- 房间服务：获取房间信息
- 用户服务：获取用户信息

### 2. MQ消息处理
系统监听麦上时长消息：
```java
// 消息类型
CommonMqTaskConstant.ON_MIC_TIME

// 消息数据
CommonMqTopicData {
    uid: "用户ID",
    value: "麦上时长（分钟）",
    dateStr: "日期（yyyy-MM-dd）"
}
```

### 3. 定时任务
确保应用启用了Spring的定时任务功能：
```java
@EnableScheduling
```

## 注意事项

1. **时区处理**：系统使用沙特时区（Asia/Riyadh）进行日期计算和定时任务
2. **数据一致性**：Redis数据需要定期备份，防止数据丢失
3. **性能考虑**：大量用户的麦上时长更新可能对Redis造成压力，建议使用Redis集群
4. **推送服务**：需要根据实际的推送服务接口实现消息发送功能
5. **房间推荐**：需要根据实际的房间服务接口实现房间查询功能

## 扩展功能

1. **数据统计**：可以添加用户活跃度统计报表
2. **个性化推荐**：基于用户喜好推荐房间
3. **社交功能**：添加好友活跃度排行榜
4. **奖励系统**：除勋章外可以添加其他奖励
5. **活动运营**：可以基于活跃度数据进行精准运营 