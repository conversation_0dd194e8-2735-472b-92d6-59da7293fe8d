package com.quhong.enums;

import java.io.Serializable;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class HttpCode implements Serializable {
    public static final HttpCode SUCCESS = new HttpCode(0, "");
    public static final HttpCode  PARAM_ERROR = new HttpCode(1, "params error");
    public static final HttpCode BLOCK_ERROR = new HttpCode(72, "");
    public static final HttpCode SESSION_INVALID = new HttpCode(40, "session invalid");
    public static final HttpCode SERVER_ERROR = new HttpCode(1002, "server error");
    public static final HttpCode USER_MONITOR_FREEZE = new HttpCode(1003, "Your account has been frozen", "تم تجميد حسابك");
    public static final HttpCode USER_LIMIT = new HttpCode(1004, "Your are not allowed to take mic due to violation of YouStar regulation.");
    public static final HttpCode NOT_IN_ROOM = new HttpCode(1005, "You are not in the room", "أنت لست في الغرفة");
    public static final HttpCode AUTH_ERROR = new HttpCode(2001, "No right to operate", "لا تستطيع لتشفيل");
    public static final HttpCode ROOM_OWNER = new HttpCode(2105, "Only room owner can open it.", "يمكن لمالك الغرفة فقط فتحه.");
    public static final HttpCode TYCOON_3 = new HttpCode(2106, "Only Tycoon 3 to King privileged users can open it.", "يمكن للمستخدمين المتميزين من عضوية التميز ٣ وملك فقط فتحه.");
    public static final HttpCode BADGE_WERA_FAIL = new HttpCode(2107, "Wearing failed.", "ارتداء الفاشلة");
    public static final HttpCode WEAR_POSITION_FULL = new HttpCode(2108, "Please take one badge off first.", "يرجى إزالة وسام واحدة أولاً. ");
    public static final HttpCode GAME_OVER = new HttpCode(2109, "Game over", "انتهت اللعبة");
    public static final HttpCode WRONG_NUMBER_OF_PLAYERS = new HttpCode(2110, "wrong number of players");
    public static final HttpCode UPDATE_APP = new HttpCode(1, "For better experience, please update YouStar in the store.", "من أجل الحصول على تجربة أفضل ، يرجى تحديث YouStar في المتجر");
    public static final HttpCode BLOCKED_BY_ACTOR = new HttpCode(47, "You have been blacklisted by this user.", "لقد قمت بالفعل بالوصول إلى القائمة السوداء بواسطة هذا المستخدم.");
    public static final HttpCode TICKETS_NUM_NOT_ENOUGH = new HttpCode(101, "Insufficient number of raffle tickets", "عدد تذاكر اليانصيب غير كافي");

    private int code;
    private String msg;
    private final Map<Integer, String> msgMap;

    public HttpCode() {
        msgMap = Collections.emptyMap();
    }

    public HttpCode(int code, String... langMsg) {
        this.code = code;
        if (langMsg.length > 0) {
            this.msg = langMsg[0];
        }
        this.msgMap = new HashMap<>();
        int i = SLangType.ENGLISH;
        for (String element : langMsg) {
            this.msgMap.put(i, element);
            i++;
        }
    }

    public void setCode(int code) {
        this.code = code;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public String getMsg(int slang) {
        String ret = this.msgMap.get(slang);
        if (ret == null) {
            return msg;
        }
        return ret;
    }
}
