package com.quhong.monitor.mem;

import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import com.quhong.monitor.BaseMonitorSender;
import com.quhong.monitor.MonitorChecker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryUsage;
import java.lang.management.GarbageCollectorMXBean;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Resource;

@Component
public class MemoryIMonitor {
    private static final Logger logger = LoggerFactory.getLogger(MemoryIMonitor.class);
    private static final String[] unitArr = {"B", "K", "M", "G"};
    private static final List<String> gcServerNameList = Arrays.asList("ustar_room_list","ustar_java_gift","ustar_java_user");

    @Value("${mem.ratio:85}")
    private int ratio;
    @Value("${spring.application.name}")
    protected String appName;

    private final MonitorChecker monitorChecker;

    public MemoryIMonitor() {
        monitorChecker = new MonitorChecker("memory");
    }

    @PostConstruct
    public void postInit() {
        TimerService.getService().addDelay(new LoopTask(60 * 1000) {
            @Override
            protected void execute() {
                tick();
            }
        });
    }

    private void tick() {
        MemoryUsage memoryUsage = ManagementFactory.getMemoryMXBean().getHeapMemoryUsage();
        long max = memoryUsage.getMax();
        long used = memoryUsage.getUsed();
        double memRatio = (double) used / max * 100;
        logger.info("heap memory init={} used={} committed={} max={} memRatio={}",
                toHuman(memoryUsage.getInit()), toHuman(used), toHuman(memoryUsage.getCommitted()), toHuman(max), memRatio);
        if (memRatio > ratio) {
            if (!monitorChecker.inWarning()) {
                sendMonitor(used, max, memRatio);
                logger.info("mem used warning. {} / {}", toHuman(used), toHuman(max));
            }
        } else {
            monitorChecker.stopWarning();
        }
    }

    private void sendMonitor(long used, long max, double memRatio) {
        monitorChecker.startWarning("mem used too high, memRatio=" + memRatio, "mem used too high. " + toHuman(used) + "/" + toHuman(max));
        BaseMonitorSender sender = monitorChecker.getSender();
        if (sender != null && gcServerNameList.contains(sender.getServerName())) {
            // 触发GC
            logger.info("serverName:{} 内存使用率超过:{}，触发主动GC", sender.getServerName(), memRatio);
            triggerGC();
        }
    }

    private String toHuman(long value) {
        double mem = value;
        int unitIndex = 0;
        while (mem >= 1024) {
            mem = mem / 1024;
            unitIndex++;
            if (unitIndex >= unitArr.length) {
                break;
            }
        }
        return String.format("%.2f", mem) + unitArr[unitIndex];
    }


    public void triggerGC() {
        // 记录GC前的内存使用情况
        long beforeUsed = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();

        // 执行GC
        System.gc();

        // 记录GC后的内存使用情况
        long afterUsed = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();

        logger.info("GC执行完成 - 释放内存: {}MB", (beforeUsed - afterUsed) / 1024 / 1024);

        // 输出当前GC统计信息
        printGCStats();
    }

    private void printGCStats() {
        List<GarbageCollectorMXBean> gcBeans = ManagementFactory.getGarbageCollectorMXBeans();
        for (GarbageCollectorMXBean gcBean : gcBeans) {
            logger.info("GC统计 - {}: 次数={}, 总时间={}ms",
                    gcBean.getName(),
                    gcBean.getCollectionCount(),
                    gcBean.getCollectionTime());
        }
    }
}
