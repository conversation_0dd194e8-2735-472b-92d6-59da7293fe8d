/**
 * All rights reserved. This material is confidential and proprietary to Tungsten.
 */
package com.quhong.core.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

public class PropertyConfig extends AbstractCacheConfig {
    private static final Logger logger = LoggerFactory.getLogger(PropertyConfig.class);

    private Properties properties;

    public PropertyConfig(Properties properties){
        this.properties = properties;
    }

    @Override
    protected void doDispose() {
        super.doDispose();
        properties.clear();
        properties = null;
    }

    @Override
    public String getStr(String key) {
        String str = properties.getProperty(key);
        if (isEmpty(str)) {
            return null;
        }
        return str;
    }

    @Override
    public void setStr(String key, String value) {
        properties.put(key, value);
    }
}
