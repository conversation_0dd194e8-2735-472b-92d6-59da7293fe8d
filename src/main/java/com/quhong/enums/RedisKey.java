package com.quhong.enums;

public interface RedisKey extends BaseRedisKey {
    String MATCH_KEY = "match_users";
    String FAKE_MATCH_KEY = "users_fake_call_scene";
    String MATCH_TIMES = "match_times_";
    String FAKE_MATCH_TIMES = "times_fake_called_";

    String CONFIG_FREEZE_TIME = "freeze_time";

    String ONLINE_USER_HASH = "online_user_hash"; // 在线用户hash
    String ONLINE_HOST_HASH = "online_host_hash"; //在线主播hash, 不包含测试主播
//    String ONLINE_GOLD_USER_HASH = "online_gold_user_hash"; //在线有钱的主播hash
    String ONLINE_GOLD_USER_ZSET = "online_gold_user_zset"; //在线有钱的主播set

    String GATE_LIST = "gate_list"; //gate在线列表
}
