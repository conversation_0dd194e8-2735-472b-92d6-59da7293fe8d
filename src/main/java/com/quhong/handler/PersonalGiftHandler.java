
package com.quhong.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.api.MsgService;
import com.quhong.constant.GiftConstant;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.GiftContext;
import com.quhong.data.dto.MsgSendDTO;
import com.quhong.data.dto.SendGiftDTO;
import com.quhong.data.vo.SendMsgVO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.MsgType;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.dao.FamilyDao;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.data.FamilyData;
import com.quhong.mysql.data.GiftData;
import com.quhong.utils.AppVersionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 个人主页及私信礼物，写入私信记录
 */
@Component
public class PersonalGiftHandler implements IGiftHandler {
    protected static final Logger logger = LoggerFactory.getLogger(PersonalGiftHandler.class);

    @Resource
    private MsgService msgService;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private FamilyDao familyDao;

    @Override
    public void process(SendGiftDTO req, GiftData giftData, GiftContext context) {
        if (req.getSendType() == GiftConstant.ST_SEND_ONE
                && (req.getSendScene() == GiftConstant.SEND_SCENE_MSG ||
                req.getSendScene() == GiftConstant.SEND_SCENE_GUARD ||
                req.getSendScene() == GiftConstant.SEND_SCENE_GIFT_WALL)) {
            if (AppVersionUtils.versionCheck(845, req)) {
                MsgSendDTO msgDto = new MsgSendDTO();
                msgDto.setUid(req.getUid());
                msgDto.setAid(req.getAid());
                msgDto.setMsg_type(MsgType.GIFT);
                msgDto.setOs(req.getOs());
                msgDto.setSlang(req.getSlang());
                msgDto.setVersioncode(req.getVersioncode());
                msgDto.setMsg_body(giftData.getGicon());
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("giftId", giftData.getRid());
                jsonObject.put("gatype", giftData.getGatype());
                jsonObject.put("price", giftData.getPrice());
                jsonObject.put("gname", giftData.getGname());
                jsonObject.put("sendNum", req.getNumber());
                int isFusionAnimation = giftData.getIsFusionAnimation();
                String fusionId = giftData.getFusionId();
                if(isFusionAnimation == GiftDao.FUSION_MODE_3){
                    if (StringUtils.isEmpty(fusionId)){
                        return;
                    }
                    ActorData actorData = actorDao.getActorDataFromCache(fusionId);
                    if (actorData == null){
                        return;
                    }
                    jsonObject.put("fusion_id", actorData.getStrRid());
                    jsonObject.put("fusion_name", actorData.getName());
                    jsonObject.put("fusion_head", ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                }

                if(isFusionAnimation == GiftDao.FUSION_MODE_4){
                    if (StringUtils.isEmpty(fusionId)){
                        return;
                    }
                    FamilyData familyData = familyDao.selectById(Integer.valueOf(fusionId));
                    if (familyData == null){
                        return;
                    }
                    jsonObject.put("fusion_id", String.valueOf(familyData.getRid()));
                    jsonObject.put("fusion_name", familyData.getName());
                    jsonObject.put("fusion_head", ImageUrlGenerator.generateRoomUserUrl(familyData.getHead()));
                }


                msgDto.setMsg_info(jsonObject);
                SendMsgVO result = msgService.sendMsg(msgDto);
                logger.info("inner send msg result={}", JSON.toJSONString(result));
                //commonTaskService.sendCommonTaskMq(new CommonMqTopicData(req.getUid(), "", req.getAid(), "", CommonMqTaskConstant.SEND_PRIVATE_GIFT, context.getCostBeans()));

            }
        }
    }
}
