package com.quhong.constant;

/**
 * 活跃勋章常量
 */
public class ActiveBadgeConstant {

    /**
     * 活动名称
     */
    public static final String ACTIVITY_NAME = "ActiveBadge";
    
    /**
     * 活动ID
     */
    public static final String ACTIVITY_ID = "active_badge_activity";
    
    /**
     * 勋章等级天数要求
     */
    public static final int[] BADGE_LEVEL_DAYS = {3, 7, 15, 30, 60, 90};
    
    /**
     * 不同累积天数区间的麦上时长要求（分钟）
     */
    public static final int MIC_TIME_LEVEL_1_3 = 15;    // 第1-3天
    public static final int MIC_TIME_LEVEL_4_7 = 20;    // 第4-7天
    public static final int MIC_TIME_LEVEL_8_15 = 25;   // 第8-15天
    public static final int MIC_TIME_LEVEL_16_90 = 30;  // 第16-90天
    
    /**
     * 最大累积天数
     */
    public static final int MAX_ACCUMULATE_DAYS = 90;
    
    /**
     * 推送时间（沙特时间20:00对应的小时）
     */
    public static final int PUSH_HOUR = 20;
    
    /**
     * Redis Key前缀
     */
    public static final String REDIS_KEY_PREFIX = "active_badge:";
    
    /**
     * 用户累积活跃天数Key
     */
    public static final String USER_ACCUMULATE_DAYS_KEY = REDIS_KEY_PREFIX + "accumulate_days:";
    
    /**
     * 用户当日麦上时长Key
     */
    public static final String USER_DAILY_MIC_TIME_KEY = REDIS_KEY_PREFIX + "daily_mic_time:";
    
    /**
     * 用户勋章解锁记录Key
     */
    public static final String USER_BADGE_UNLOCK_KEY = REDIS_KEY_PREFIX + "badge_unlock:";
    
    /**
     * 用户最后活跃日期Key
     */
    public static final String USER_LAST_ACTIVE_DATE_KEY = REDIS_KEY_PREFIX + "last_active_date:";
} 