package com.quhong.constant;


import java.util.Arrays;
import java.util.List;

public class LoginConstant {
    public static final int GOOGLE_TYPE = 1;
    public static final int FACE_BOOK_TYPE = 2;
    public static final int FIRE_BASE_MAIL_TYPE = 3;
    public static final int FIRE_BASE_PHONE_TYPE = 4;
    public static final int APPLE_TYPE = 5;
    public static final int HUAWEI_TYPE = 6;
    public static final int SPECIAL_GUST_TYPE = 7;// vip登入
    public static final int HUAWEI_PHONE_TYPE = 8;// 华为验证手机号登入

    public static final int homeTab = 1;// 1为进入Crush，2为进入Party-Chat列表，3为Crush和Party-Global交替进入，默认配置为3 ，8.43以后 1为hot 2为Discover
    public static final List<Integer> ALL_LOGIN_TYPE = Arrays.asList
            (GOOGLE_TYPE, FACE_BOOK_TYPE, FIRE_BASE_MAIL_TYPE, FIRE_BASE_PHONE_TYPE,
                    APPLE_TYPE, HUAWEI_TYPE, SPECIAL_GUST_TYPE,HUAWEI_PHONE_TYPE);

    public static final String USTAR_JAVA_EXCEPTION_WARN_NAME = "ustar_java_exception";

    public static final String USTAR_RISK_LOGIN_WARN_NAME = "ustar_risk_login";

    public static final List<Integer> DEVICE_LIMIT_ACCOUNT_NUM = Arrays.asList(3, 20, 30, 50);

    public static final int DEVICE_LIMIT_ACCOUNT_TEST_MAX_NUM = 500;

    public static final int LOGIN_STATE_SUCCESS = 0;
    public static final int LOGIN_STATE_ACCOUNT_INVALID = 101;
    public static final int LOGIN_STATE_ACCOUNT_RISK_ZERO = 102;
    public static final int LOGIN_STATE_DEVICE_INVALID = 201;
    public static final int LOGIN_STATE_DEVICE_EMULATOR = 202;
    public static final int LOGIN_STATE_DEVICE_LANGUAGE = 203;
    public static final int LOGIN_STATE_DEVICE_NUM = 204;
    public static final int LOGIN_STATE_DEVICE_LIMIT_USER = 205;
    public static final int LOGIN_STATE_THIRD_LOGIN_FAIL_G = 301;
    public static final int LOGIN_STATE_THIRD_LOGIN_FAIL_FB = 302;
    public static final int LOGIN_STATE_THIRD_LOGIN_FAIL_MAIL = 303;
    public static final int LOGIN_STATE_THIRD_LOGIN_FAIL_PHONE = 304;
    public static final int LOGIN_STATE_THIRD_LOGIN_FAIL_APPLE = 305;
    public static final int LOGIN_STATE_THIRD_LOGIN_FAIL_HUAWEI = 306;
    public static final int LOGIN_STATE_THIRD_LOGIN_FAIL_VIP = 307;
    public static final int LOGIN_STATE_THIRD_LOGIN_FAIL_TN = 308;
    public static final int LOGIN_STATE_SEVER_PARM_ERROR = 401;
    public static final int LOGIN_STATE_SEVER_TIME_OUT = 402;
    public static final int LOGIN_STATE_SEVER_GEN_RID_ERROR = 403;
    public static final int LOGIN_STATE_SEVER_OTHER_ERROR = 404;
    public static final int LOGIN_STATE_IP_LIMIT_ERROR = 501;

    public static final int TN_ROOT_TYPE = 202;
    public static final int TN_DUAL_APP_TYPE = 5009;
    public static final int TN_DUAL_SYSTEM_TYPE = 214;
    public static final int TN_FAKE_GEO_TYPE = 208;


    // 203, 204, 205, 206, 209, 216, 301, 302, 303, 401, 402, 1001, 1100
    public static final List<Integer> TN_LIMIT_TYPE = Arrays.asList
            (18, 5012, 5014, 5017, 901, 1002, 1003, 5013, 1202, 504, 5016);

    public static final String TEST_USER_PRE = "test-user-";
}
