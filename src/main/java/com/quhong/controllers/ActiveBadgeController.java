package com.quhong.controllers;

import com.quhong.data.vo.ActiveBadgeVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.service.ActiveBadgeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 活跃勋章控制器
 */
@RestController
@RequestMapping("/activity/activeBadge")
public class ActiveBadgeController {

    private static final Logger logger = LoggerFactory.getLogger(ActiveBadgeController.class);

    @Resource
    private ActiveBadgeService activeBadgeService;

    /**
     * 获取用户活跃勋章信息
     */
    @GetMapping("/info")
    public HttpResult<ActiveBadgeVO> getUserActiveBadgeInfo(@RequestParam String uid) {
        try {
            logger.info("获取用户活跃勋章信息: uid={}", uid);
            ActiveBadgeVO vo = activeBadgeService.getUserActiveBadgeInfo(uid);
            return HttpResult.getOk(vo);
        } catch (Exception e) {
            logger.error("获取用户活跃勋章信息失败: uid={}, error={}", uid, e.getMessage(), e);
            return new HttpResult<>(HttpCode.SERVER_ERROR.getCode(), "获取活跃勋章信息失败", null);
        }
    }

    /**
     * 点击GO跳转到随机房间
     */
    @PostMapping("/randomRoom")
    public HttpResult<String> getRandomRoom(@RequestParam String uid) {
        try {
            logger.info("获取随机房间: uid={}", uid);
            String roomId = activeBadgeService.getRandomRoomForUser(uid);
            if (roomId != null) {
                return HttpResult.getOk(roomId);
            } else {
                return new HttpResult<>(HttpCode.PARAM_ERROR.getCode(), "暂无可用房间", null);
            }
        } catch (Exception e) {
            logger.error("获取随机房间失败: uid={}, error={}", uid, e.getMessage(), e);
            return new HttpResult<>(HttpCode.SERVER_ERROR.getCode(), "获取随机房间失败", null);
        }
    }
} 