package com.quhong.controllers;

import com.quhong.data.dto.SetResourcesDTO;
import com.quhong.data.dto.StoreDTO;
import com.quhong.data.vo.GoodsListHomeVO;
import com.quhong.data.vo.MyRippleVO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.WebController;
import com.quhong.service.RippleService;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 声波资源
 *
 * <AUTHOR>
 * @date 2022/9/14
 */
@RestController
@RequestMapping(value = "${baseUrl}")
public class RippleController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(RippleController.class);

    @Resource
    private RippleService rippleService;



    /**
     * 声波装饰列表
     */
    @RequestMapping("new_store/ripple/list")
    public String getRippleList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreDTO req = RequestUtils.getSendData(request, StoreDTO.class);
        GoodsListHomeVO vo = rippleService.getStoreGoodsList(req);
        logger.info("get float screen list. uid={} page={} requestId={} timeMillis={}", req.getUid(), req.getPage(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 我的声波资源列表
     */
    @RequestMapping("new_store/mine_ripple_list")
    public String myRippleList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreDTO req = RequestUtils.getSendData(request, StoreDTO.class);
        GoodsListHomeVO vo = rippleService.getMyGoodsList(req);
        logger.info("get my ripple list. uid={} page={} requestId={} timeMillis={}", req.getUid(), req.getPage(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 设置声波资源
     */
    @RequestMapping("new_store/set_ripple")
    public String setRipple(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        SetResourcesDTO req = RequestUtils.getSendData(request, SetResourcesDTO.class);
        rippleService.setRipple(req);
        logger.info("set ripple. uid={} pid={} timeMillis={}", req.getUid(), req.getRipple_id(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, null);
    }
}
