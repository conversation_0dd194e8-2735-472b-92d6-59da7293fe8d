package com.quhong.controllers;

import com.quhong.data.dto.RoomKickDTO;
import com.quhong.data.vo.KickUserInfoVO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.RoomHttpCode;
import com.quhong.handler.WebController;
import com.quhong.service.RoomKickService;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 房间踢人Controller
 *
 * <AUTHOR>
 * @date 2022/7/25
 */
@RestController
@RequestMapping("${baseUrl}")
public class RoomKickController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(RoomKickController.class);

    @Resource
    private RoomKickService roomKickService;

    /**
     * 将用户踢出房间,先请求服务端记一条记录
     */
    @RequestMapping("kick")
    private String roomKick(HttpServletRequest request) {
        RoomKickDTO req = RequestUtils.getSendData(request, RoomKickDTO.class);
        logger.info("room kick. roomId={} uid={} aid={} forbid={}", req.getRoomId(), req.getUid(), req.getAid(), req.getForbid());
        roomKickService.roomKick(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 查询踢出用户的人
     */
    @RequestMapping("kick_info")
    private String kickUserInfo(HttpServletRequest request) {
        RoomKickDTO req = RequestUtils.getSendData(request, RoomKickDTO.class);
        logger.info("get kick user info. roomId={} uid={} requestId={}", req.getRoom_id(), req.getUid(), req.getRequestId());
        KickUserInfoVO vo = roomKickService.kickUserInfo(req);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 检测自己是否被踢
     */
    @RequestMapping("kick_check")
    private String kickCheck(HttpServletRequest request) {
        RoomKickDTO req = RequestUtils.getSendData(request, RoomKickDTO.class);
        logger.info("check yourself have been kicked. roomId={} uid={} requestId={}", req.getRoomId(), req.getUid(), req.getRequestId());
        roomKickService.kickCheck(req);
        return createResult(req, RoomHttpCode.BE_KICKED_OUT_OF_THE_ROOM, null);
    }
}
