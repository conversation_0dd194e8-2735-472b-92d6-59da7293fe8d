package com.quhong.controllers;


import com.alibaba.fastjson.JSONObject;
import com.quhong.data.dto.ShareActivityDTO;
import com.quhong.data.vo.DragonTrainVO;
import com.quhong.data.vo.GreedyElephantVO;
import com.quhong.datas.HttpResult;
import com.quhong.handler.H5Controller;
import com.quhong.service.DragonTrainService;
import com.quhong.service.GreedyElephantService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 活动接口
 */


@RestController
@RequestMapping(value = "/activity/", produces = MediaType.APPLICATION_JSON_VALUE)
public class GeneralV3Controller extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(GeneralV3Controller.class);

    @Resource
    private DragonTrainService dragonTrainService;
    @Resource
    private GreedyElephantService greedyElephantService;

    /**
     * 组队驯龙配置
     */
    @RequestMapping("dragonTrainConfig")
    public HttpResult<DragonTrainVO> dragonTrainConfig(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("dragonTrainConfig activityId: {}, uid:{}", activityId, uid);
        return HttpResult.getOk(dragonTrainService.dragonTrainConfig(activityId, uid));
    }

    /**
     * 驯龙团队榜单
     */
    @RequestMapping("dragonTrainTeamRank")
    public HttpResult<DragonTrainVO> dragonTrainTeamRank(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("dragonTrainTeamRank activityId: {}, uid:{}", activityId, uid);
        return HttpResult.getOk(dragonTrainService.dragonTrainTeamRank(activityId, uid));
    }

    /**
     * 驯龙抽奖
     */
    @RequestMapping("dragonTrainDraw")
    public HttpResult<DragonTrainVO> dragonTrainDraw(@RequestParam String activityId, @RequestParam String uid, @RequestParam int amount) {
        logger.info("dragonTrainDraw activityId: {}, uid:{}, amount={}", activityId, uid, amount);
        return HttpResult.getOk(dragonTrainService.dragonTrainDraw(activityId, uid, amount));
    }

    /**
     * 驯龙抽奖记录
     */
    @RequestMapping("dragonTrainRecord")
    public HttpResult<DragonTrainVO> dragonTrainRecord(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        logger.info("dragonTrainRecord activityId:{}, uid: {}, page:{}", activityId, uid, page);
        return HttpResult.getOk(dragonTrainService.dragonTrainRecord(activityId, uid, page));
    }


    /**
     * 驯龙加入组队
     */
    @RequestMapping("dragonTrainJoinTeam")
    public HttpResult<?> dragonTrainJoinTeam(@RequestParam String activityId, @RequestParam String uid, @RequestParam String captainUid) {
        logger.info("dragonTrainJoinTeam activityId:{}, uid: {}, captainUid:{}", activityId, uid, captainUid);
        dragonTrainService.dragonTrainJoinTeam(activityId, uid, captainUid);
        return HttpResult.getOk();
    }

    /**
     * 驯龙分享给好友
     */
    @RequestMapping("dragonTrainShare")
    public HttpResult<?> dragonTrainShare(@RequestBody ShareActivityDTO dto) {
        logger.info("dragonTrainShare dto:{} ", JSONObject.toJSONString(dto));
        dragonTrainService.dragonTrainShare(dto);
        return HttpResult.getOk();
    }

    /**
     * 贪婪的大象配置
     */
    @RequestMapping("greedyElephantConfig")
    public HttpResult<GreedyElephantVO> greedyElephantConfig(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("greedyElephantConfig activityId: {}, uid:{}", activityId, uid);
        return HttpResult.getOk(greedyElephantService.greedyElephantConfig(activityId, uid));
    }

}
