package com.quhong.controllers;

import com.quhong.data.dto.RoomThemeDTO;
import com.quhong.data.vo.MicThemeVO;
import com.quhong.data.vo.OptMicThemeVO;
import com.quhong.datas.HttpResult;
import com.quhong.service.RoomThemeService;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
@RestController
@RequestMapping("${baseUrl}")
public class RoomThemeController extends AbstractRoomMicController {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private RoomThemeService roomThemeService;

    @PostMapping("mic_theme_list")
    public HttpResult<PageVO<MicThemeVO>> getMicThemeList(@RequestBody RoomThemeDTO dto) {
        logger.info("get room mic theme list. uid={} roomId={}", dto.getUid(), dto.getRoomId());
        roomIdCheck(dto.getRoomId());
        return HttpResult.getOk(roomThemeService.getMicThemeList(dto));
    }

    /**
     * 操作房间麦位主题
     */
    @PostMapping("opt_mic_theme")
    public HttpResult<OptMicThemeVO> optMicTheme(@RequestBody RoomThemeDTO dto) {
        logger.info("opt mic theme. roomId={} uid={} micTheme={}", dto.getRoomId(), dto.getUid(), dto.getThemeId());
        roomIdCheck(dto.getRoomId());
        return HttpResult.getOk(roomThemeService.optMicTheme(dto));
    }

}
