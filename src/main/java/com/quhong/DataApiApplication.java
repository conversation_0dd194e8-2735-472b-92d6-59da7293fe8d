package com.quhong;

import com.quhong.config.BaseAppConfig;
import com.quhong.config.NettyConfig;
import com.quhong.config.TCPServerConfig;
import com.quhong.core.config.ServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * BaseAppConfig 基础配置
 * NettyConfig 服务发现
 * TCPServerConfig 长连接配置
 * RoomMongoBean 连接room库
 */
@EnableScheduling
@EnableAsync
@ServletComponentScan
@ImportResource("classpath*:spring-context.xml")
@ImportAutoConfiguration({BaseAppConfig.class, NettyConfig.class, TCPServerConfig.class})
@SpringBootApplication(exclude = {RedisAutoConfiguration.class, MongoAutoConfiguration.class,
        DataSourceAutoConfiguration.class})
public class DataApiApplication {
    private static final Logger logger = LoggerFactory.getLogger(DataApiApplication.class);

    public static void main(String[] args) {
        // ServerConfig.loadConfig(DataApiApplication.class.getResource("/").getPath() + "config/config.properties");
        SpringApplication.run(DataApiApplication.class, args);
        logger.info("============= data_api {} start ===============================", ServerConfig.getServerID());
    }
}
