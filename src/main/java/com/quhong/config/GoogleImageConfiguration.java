package com.quhong.config;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.vision.v1.ImageAnnotatorClient;
import com.google.cloud.vision.v1.ImageAnnotatorSettings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022/9/14
 */

@Configuration
public class GoogleImageConfiguration {

    /**
     * 加载 身份验证凭据
     */
    @Value(value = "classpath:ustar-youtube-313414-981d28412a75.json")
    private Resource dataAnalyticsResource;

    /**
     * 配置核心Bean
     */
    @Bean
    ImageAnnotatorClient imageClient() throws IOException {
        GoogleCredentials credentials = GoogleCredentials.fromStream(dataAnalyticsResource.getInputStream());
        ImageAnnotatorSettings imageAnnotatorSettings = ImageAnnotatorSettings.newBuilder().setCredentialsProvider(() -> credentials).build();
        return ImageAnnotatorClient.create(imageAnnotatorSettings);
    }

}
