package com.quhong.operation.dao;

import com.quhong.mongo.config.OpMongoBean;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.share.data.AggStatData;
import com.quhong.operation.share.mongobean.FollowRoom;
import com.quhong.operation.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/8/5
 */
@Component
public class FollowRoomOpDao {

    private final static Logger logger = LoggerFactory.getLogger(FollowRoomOpDao.class);

    @Resource(name= OpMongoBean.MOVIES)
    private MongoTemplate mongoTemp;

    /**
     * 查看用户关注的房间数
     * @param uid 用户user_id
     * @return 关注房间数
     */
    public ApiResult<Integer> actorFollowRoomCount(String uid) {
        ApiResult<Integer> result = new ApiResult<>();
        Query query = new Query(Criteria.where("uid").is(uid));
        List<FollowRoom> list = mongoTemp.find(query, FollowRoom.class);
        logger.info("uid={} follow room num={}", uid, list.size());
        return result.ok(list.size());
    }

    /**
     * 查看房间有多少人关注
     * @param startTime 开始时间
     * @param endTime 结尾时间
     * @param roomId 房间roomId
     * @return 关注数
     */
    public ApiResult<Integer>  roomFollowActor (Integer startTime, Integer endTime, String roomId) {
        ApiResult<Integer> result = new ApiResult<>();
        Query query = new Query(Criteria.where("room_id").is(roomId).and("c_time").gte(startTime).lte(endTime));
        List<FollowRoom> list = mongoTemp.find(query, FollowRoom.class);
        return result.ok(list.size());
    }

    /**
     * 查看房间关注人数
     * @param startTime 开始时间
     * @param endTime 结尾时间
     * @param roomIdSet 房间roomId
     * @return
     */
    public List<AggStatData>  roomFollowActorStat (Integer startTime, Integer endTime, Set<String> roomIdSet) {
        Criteria criteria = Criteria.where("c_time").gte(startTime).lte(endTime);
        if(!CollectionUtils.isEmpty(roomIdSet)){
            criteria = criteria.and("room_id").in(roomIdSet);
        }
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("room_id").count().as("userCount"),
                Aggregation.project("userCount").and("_id").as("uid")
        );
        List<AggStatData> list = mongoTemp.aggregate(aggregation, "follow_room", AggStatData.class).getMappedResults();
        if(list == null){
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 某批用户某天关注房间人数(房间没有固定)
     * @param dateStr 某天
     * @param uidList 某部分用户uid
     * @return 某天关注过房间的人数
     */
    public ApiResult<Integer> partyActorFollowNum(String dateStr, List<String> uidList) {
        ApiResult<Integer> result = new ApiResult<>();
        if (CollectionUtils.isEmpty(uidList)) {
            logger.info("uid list is empty");
            return result.ok(0);
        }
        Integer[] timeArr = DateHelper.ARABIAN.getTimeWhereRange(dateStr);
        Criteria criteria = Criteria.where("c_time").gte(timeArr[0]).lt(timeArr[1]).and("uid").in(uidList);
        // 过滤条件
        MatchOperation match = Aggregation.match(criteria);

        // 分组查询统计
        GroupOperation group = Aggregation.group(Aggregation.fields("uid"));
        group = group.count().as("countNum");

        // 聚合代码
        Aggregation agg = TypedAggregation.newAggregation(match, group);
        AggregationResults<Map> results = mongoTemp.aggregate(agg, FollowRoom.class, Map.class);
        List<Map> data = results.getMappedResults();
        if (CollectionUtils.isEmpty(data)) {
            return result.error("partyActorFollowNum error!");
        }
        try {
            return result.ok(data.size());
        } catch (Exception e) {
            logger.error("{}", e.getMessage(), e);
            return result.error(e.getMessage());
        }
    }

}

