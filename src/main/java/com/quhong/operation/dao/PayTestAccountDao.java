package com.quhong.operation.dao;

import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
public class PayTestAccountDao {

    private final static Logger logger = LoggerFactory.getLogger(PayTestAccountDao.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate userSandBoxRedis;

    /**
     * 判断是否测试用户
     */
    public boolean isTestUser(String uid) {
        try {
            Boolean ret = userSandBoxRedis.opsForSet().isMember(getRedisKey(), uid);
            return ret != null && ret;
        } catch (Exception e) {
            logger.error("check is test user error. uid={} {}", uid, e.getMessage(), e);
        }
        return false;
    }

    private String getRedisKey() {
        return "android_sandbox_buy";
    }

}
