package com.quhong.operation.server;

import com.quhong.operation.common.ApiResult;
import com.quhong.operation.common.ApiResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2020/8/10
 */
@Service
public class FileServer{

    private final static Logger logger = LoggerFactory.getLogger(FileServer.class);

    /**
     * 更新文件
     * @param is InputStream
     * @param pathname 更新到哪里
     * @return 是否更新成功
     */
    public ApiResult<Boolean> updateFile (InputStream is, String pathname) {
        ApiResult<Boolean> result = new ApiResult<>();
        FileOutputStream os = null;
        try {
            File file = new File(pathname);
            byte[] bs = new byte[1024];
            os = new FileOutputStream(file);
            int len;
            while ((len = is.read(bs)) != -1) {
                os.write(bs, 0, len);
            }
            file = null;
        } catch (IOException e) {
            try {
                if (null != is) is.close();
            } catch (IOException ex) {
                logger.error("{}", ex.getMessage(), ex);
            }
            try {
                if (null != os) os.close();
            } catch (IOException ex) {
                logger.error("{}", ex.getMessage(), ex);
            }
            logger.error("{}", e.getMessage(), e);
            return result.error("file upload fail, error info:"+e.getMessage());
        }

        return result.ok();
    }

}
