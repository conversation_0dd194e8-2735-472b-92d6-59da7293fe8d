package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.MonitorCostService;
import com.quhong.operation.share.condition.MonitorWarnCondition;
import com.quhong.operation.share.vo.MonitorCostVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 成本告警配置
 */

@RestController
@RequestMapping(value ="/monitorCost", produces = MediaType.APPLICATION_JSON_VALUE)
public class MonitorCostController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(MonitorCostController.class);

    @Resource
    private MonitorCostService monitorCostService;


    @RequireRole
    @RequestMapping("/list")
    public String getDataList(@RequestBody MonitorWarnCondition condition) {
        logger.info("getDataList {}", JSONObject.toJSONString(condition));
        return createResult(HttpCode.SUCCESS, monitorCostService.getDataList(condition));
    }


    @RequireRole
    @PostMapping("/addData")
    public String addData(@RequestParam String uid, @RequestBody MonitorCostVO dto) {
        logger.info("addData MonitorCostVO Data {}", JSONObject.toJSONString(dto));
        monitorCostService.addData(uid, dto);
        return createResult(HttpCode.SUCCESS, "");
    }


    @RequireRole
    @PostMapping("/updateData")
    public String updateData(@RequestParam String uid, @RequestBody MonitorCostVO dto) {
        logger.info("update MonitorCostVO Data {}", JSONObject.toJSONString(dto));
        monitorCostService.updateData(uid, dto);
        return createResult(HttpCode.SUCCESS, "");
    }


    @RequireRole
    @PostMapping("/deleteData")
    public String deleteData(@RequestBody MonitorCostVO dto) {
        logger.info("deleteData MonitorCostVO Data {}", JSONObject.toJSONString(dto));
        monitorCostService.deleteData(dto);
        return createResult(HttpCode.SUCCESS, "");
    }
}
