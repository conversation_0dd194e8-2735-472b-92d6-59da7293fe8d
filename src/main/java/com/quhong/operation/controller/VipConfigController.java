package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.datas.HttpResult;
import com.quhong.handler.BaseController;
import com.quhong.mysql.data.VipConfigData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.VipConfigService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * VIP配置接口
 */
@RestController
@RequestMapping(value = "/vipConfig", produces = MediaType.APPLICATION_JSON_VALUE)
public class VipConfigController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(VipConfigController.class);

    @Resource
    private VipConfigService vipConfigService;

    /**
     * 列表
     */
    @RequireRole
    @RequestMapping("/list")
    public HttpResult<PageResultVO<VipConfigData>> list(@RequestBody BaseCondition condition) {
        logger.info("get list={}", JSONObject.toJSONString(condition));
        return HttpResult.getOk(vipConfigService.list(condition));
    }

    /**
     * 新增
     */
    @RequireRole
    @RequestMapping("/add")
    public HttpResult<?> addData(@RequestBody VipConfigData dto) {
        logger.info("addData {}", JSONObject.toJSONString(dto));
        vipConfigService.addData(dto);
        return HttpResult.getOk();
    }

    /**
     * 更新
     */
    @RequireRole
    @RequestMapping("/update")
    public HttpResult<?> updateData(@RequestBody VipConfigData dto) {
        logger.info("updateData {}", JSONObject.toJSONString(dto));
        vipConfigService.updateData(dto);
        return HttpResult.getOk();
    }
}
