package com.quhong.operation.task;

import com.quhong.config.AsyncConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.SlaveMoneyDetailDao;
import com.quhong.utils.K8sUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/10/15
 */
@Component
public class AlertTask {

    public final Logger logger = LoggerFactory.getLogger(getClass());

    private final static Map<String, Integer> GAME_MAP = new HashMap<>(16);

    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private SlaveMoneyDetailDao slaveMoneyDetailDao;
    @Resource
    private MonitorSender monitorSender;

    @PostConstruct
    public void init() {
        // sud游戏
        GAME_MAP.put("Ludo", 923);
        GAME_MAP.put("UMO", 926);
        GAME_MAP.put("Monster Crush", 941);
        GAME_MAP.put("Domino", 948);
        GAME_MAP.put("Carrom Pool", 968);
        // bc游戏
        GAME_MAP.put("Fishing", 946);
        GAME_MAP.put("Slots", 944);
        GAME_MAP.put("Crash", 939);
        // 自研 bc游戏
        GAME_MAP.put("Greedy", 966);
        GAME_MAP.put("Fruit Party", 920);
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 0/5 * * * ?")
    public void noUserPlayingGameAlert() {
        if (ServerConfig.isNotProduct()) {
            return;
        }
        if (!k8sUtils.isMasterFromCache()) {
            return;
        }
        // 连续2小时无下注玩家触发告警
        int endTime = DateHelper.getNowSeconds();
        int startTime = endTime - (int) TimeUnit.HOURS.toSeconds(2);
        GAME_MAP.forEach((k, v) -> checkGameStatus(k, v, startTime, endTime));
    }

    private void checkGameStatus(String gameName, int costBeanActType, int startTime, int endTime) {
        if (slaveMoneyDetailDao.getRecordCount(costBeanActType, startTime, endTime) == 0) {
            noticeWarn(gameName);
        }
    }

    private void noticeWarn(String name) {
        String content = "游戏状态告警 \n"
                + ">游戏: " + name + "\n"
                + ">项目: Youstar \n"
                + ">详情: 连续2小时无玩家下注 \n"
                + ">时间: " + DateHelper.BEIJING.formatDateTime(new Date()) + "\n"
                + ">处理人: @谢建良";
        monitorSender.customMarkdown("game_status_alert", content);
    }
}
