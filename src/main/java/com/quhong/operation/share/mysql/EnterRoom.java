package com.quhong.operation.share.mysql;

import java.io.Serializable;

/**
 * 进入房间记录表对象
 * <AUTHOR>
 * @date 2020/8/4
 */
public class EnterRoom implements Serializable {

    private static final long serialVersionUID = -8898671888086605416L;

    public static final int ROOM_TYPE_MUSIC = 2;  //音乐房
    public static final int ROOM_TYPE_VIDEO = 6;  //视频房

    private Integer id;
    private String roomId;
    private String userId;
    // 1 房主
    private Integer isHost;
    // 0 no 1 迎新房
    private Integer rookieStatus;
    // 0 android 1 ios
    private Integer os;
    private Integer ctime;
    private Integer versionCode;
    private Integer mtime;
    // 在房间时长
    private Integer onlineTime;
    private Integer nettyStatus;
    private String ip;
    private String countryCode;
    private Integer roomType; // 2 music 6 video

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Integer getIsHost() {
        return isHost;
    }

    public void setIsHost(Integer isHost) {
        this.isHost = isHost;
    }

    public Integer getRookieStatus() {
        return rookieStatus;
    }

    public void setRookieStatus(Integer rookieStatus) {
        this.rookieStatus = rookieStatus;
    }

    public Integer getOs() {
        return os;
    }

    public void setOs(Integer os) {
        this.os = os;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(Integer versionCode) {
        this.versionCode = versionCode;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getOnlineTime() {
        return onlineTime;
    }

    public void setOnlineTime(Integer onlineTime) {
        this.onlineTime = onlineTime;
    }

    public Integer getNettyStatus() {
        return nettyStatus;
    }

    public void setNettyStatus(Integer nettyStatus) {
        this.nettyStatus = nettyStatus;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public Integer getRoomType() {
        return roomType;
    }

    public void setRoomType(Integer roomType) {
        this.roomType = roomType;
    }

    @Override
    public String toString() {
        return "EnterRoom{" +
                "id=" + id +
                ", roomId='" + roomId + '\'' +
                ", userId='" + userId + '\'' +
                ", isHost=" + isHost +
                ", rookieStatus=" + rookieStatus +
                ", os=" + os +
                ", ctime=" + ctime +
                ", versionCode=" + versionCode +
                ", mtime=" + mtime +
                ", onlineTime=" + onlineTime +
                ", nettyStatus=" + nettyStatus +
                ", ip='" + ip + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", roomType=" + roomType +
                '}';
    }
}
