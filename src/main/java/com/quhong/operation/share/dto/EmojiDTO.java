package com.quhong.operation.share.dto;

public class EmojiDTO {
    private String docId;
    private String emojiConfigId;
    private String name;       // 自定义名称
    private String nameAr;       // 自定义阿语名称
    private String icon;       // 麦位图标
    private String emojiUrl;   // 资源下载路径
    private int emojiSubType;  // 子类型 0 图片表情 1 文字表情
    private int status;        // 1 有效 0 无效
    private int order;         // 排序字段

    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public String getEmojiConfigId() {
        return emojiConfigId;
    }

    public void setEmojiConfigId(String emojiConfigId) {
        this.emojiConfigId = emojiConfigId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getEmojiUrl() {
        return emojiUrl;
    }

    public void setEmojiUrl(String emojiUrl) {
        this.emojiUrl = emojiUrl;
    }

    public int getEmojiSubType() {
        return emojiSubType;
    }

    public void setEmojiSubType(int emojiSubType) {
        this.emojiSubType = emojiSubType;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }
}
