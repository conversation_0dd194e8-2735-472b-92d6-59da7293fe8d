package com.quhong.operation.share.dto;


import com.quhong.mongo.data.RankingActivity;

import java.util.List;

public class OtherActivityTemplateDTO {
    private String activityId;
    private String acNameEn; // 英文活动名
    private String acNameAr; // 阿语活动名
    private String acUrl; // h5地址(自动生成)
    private Integer startTime; // 活动开始时间
    private Integer endTime; // 活动结束时间
    private Integer status; // 1 是否已结束 1 已结束 0 未结束
    private List<Integer> activityGiftList; // 活动礼物
    private List<RankingActivity.ActivityGift> activityGiftInfoList; // 活动礼物信息-非数据库字段

    private String uid; // 预览连接uid
    private String token; // 预览连接token

    private Integer ctime; // 创建时间

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getAcNameEn() {
        return acNameEn;
    }

    public void setAcNameEn(String acNameEn) {
        this.acNameEn = acNameEn;
    }

    public String getAcNameAr() {
        return acNameAr;
    }

    public void setAcNameAr(String acNameAr) {
        this.acNameAr = acNameAr;
    }

    public String getAcUrl() {
        return acUrl;
    }

    public void setAcUrl(String acUrl) {
        this.acUrl = acUrl;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<RankingActivity.ActivityGift> getActivityGiftInfoList() {
        return activityGiftInfoList;
    }

    public void setActivityGiftInfoList(List<RankingActivity.ActivityGift> activityGiftInfoList) {
        this.activityGiftInfoList = activityGiftInfoList;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public List<Integer> getActivityGiftList() {
        return activityGiftList;
    }

    public void setActivityGiftList(List<Integer> activityGiftList) {
        this.activityGiftList = activityGiftList;
    }
}
