package com.quhong.operation.share.dto;

import com.quhong.handler.HttpEnvData;

/**
 * <AUTHOR>
 * @date 2023/4/4
 */
public class ShareConfigDTO extends HttpEnvData {

    private Integer id;

    /**
     * 活动id
     */
    private String activityId;

    /**
     * 活动名称英语
     */
    private String name;

    /**
     * 活动名称阿语
     */
    private String nameAr;

    /**
     * 活动介绍英语
     */
    private String description;

    /**
     * 活动介绍阿语
     */
    private String descriptionAr;

    /**
     * 活动icon英语
     */
    private String icon;

    /**
     * 活动icon阿语
     */
    private String iconAr;

    /**
     * 活动banner英语
     */
    private String banner;

    /**
     * 活动banner阿语
     */
    private String bannerAr;

    /**
     * 活动banner阿语
     */
    private String url;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Integer ctime;

    private Integer page;

    private Integer pageSize;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescriptionAr() {
        return descriptionAr;
    }

    public void setDescriptionAr(String descriptionAr) {
        this.descriptionAr = descriptionAr;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIconAr() {
        return iconAr;
    }

    public void setIconAr(String iconAr) {
        this.iconAr = iconAr;
    }

    public String getBanner() {
        return banner;
    }

    public void setBanner(String banner) {
        this.banner = banner;
    }

    public String getBannerAr() {
        return bannerAr;
    }

    public void setBannerAr(String bannerAr) {
        this.bannerAr = bannerAr;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
