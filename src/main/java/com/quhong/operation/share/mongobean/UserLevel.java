package com.quhong.operation.share.mongobean;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/6/15
 */
@Document(collection = "user_level")
public class UserLevel implements Serializable {
    private static final long serialVersionUID = 6301296259992341761L;

    private String uid;
    private Integer level = 0;
    private Integer exp = 0;
    // 记录上次领取福利的等级，确保只发放一次
    @Field("last_benefit_level")
    private Integer lastBenefitLevel = 0;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getExp() {
        return exp;
    }

    public void setExp(Integer exp) {
        this.exp = exp;
    }

    public Integer getLastBenefitLevel() {
        return lastBenefitLevel;
    }

    public void setLastBenefitLevel(Integer lastBenefitLevel) {
        this.lastBenefitLevel = lastBenefitLevel;
    }

    @Override
    public String toString() {
        return "UserLevel{" +
                "uid='" + uid + '\'' +
                ", level=" + level +
                ", exp=" + exp +
                ", lastBenefitLevel=" + lastBenefitLevel +
                '}';
    }

}
