package com.quhong.operation.share.mongobean;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

@Document(collection = "buddle_source")
public class BuddleSource implements Serializable {

    private static final long serialVersionUID = 8710648014278477432L;

    @Field("buddle_id")
    private Integer buddleId;

    private String name;

    @Field("namear")
    private String nameAr;

    @Field("buddle_icon")
    private String buddleIcon;

    @Field("buddle_color")
    private String buddleColor;


    @Field("android_buddle_source_1x")
    private String androidSource;

    @Field("android_buddle_source")
    private String androidSourceXX;

    @Field("android_buddle_source_3x")
    private String androidSourceXXX;

    @Field("ios_buddle_source")
    private String iosSource;

    @Field("ios_buddle_source_2x")
    private String iosSourceXX;

    @Field("ios_buddle_source_3x")
    private String iosSourceXXX;

    private Integer status;
    private Integer forder;

    @Field("is_activity")
    private Integer isActivity;

    @Field("c_time")
    private Integer ctime;

    public Integer getBuddleId() {
        return buddleId;
    }

    public void setBuddleId(Integer buddleId) {
        this.buddleId = buddleId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getBuddleIcon() {
        return buddleIcon;
    }

    public void setBuddleIcon(String buddleIcon) {
        this.buddleIcon = buddleIcon;
    }

    public String getBuddleColor() {
        return buddleColor;
    }

    public void setBuddleColor(String buddleColor) {
        this.buddleColor = buddleColor;
    }

    public String getAndroidSource() {
        return androidSource;
    }

    public void setAndroidSource(String androidSource) {
        this.androidSource = androidSource;
    }

    public String getAndroidSourceXX() {
        return androidSourceXX;
    }

    public void setAndroidSourceXX(String androidSourceXX) {
        this.androidSourceXX = androidSourceXX;
    }

    public String getAndroidSourceXXX() {
        return androidSourceXXX;
    }

    public void setAndroidSourceXXX(String androidSourceXXX) {
        this.androidSourceXXX = androidSourceXXX;
    }

    public String getIosSource() {
        return iosSource;
    }

    public void setIosSource(String iosSource) {
        this.iosSource = iosSource;
    }

    public String getIosSourceXX() {
        return iosSourceXX;
    }

    public void setIosSourceXX(String iosSourceXX) {
        this.iosSourceXX = iosSourceXX;
    }

    public String getIosSourceXXX() {
        return iosSourceXXX;
    }

    public void setIosSourceXXX(String iosSourceXXX) {
        this.iosSourceXXX = iosSourceXXX;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getForder() {
        return forder;
    }

    public void setForder(Integer forder) {
        this.forder = forder;
    }

    public Integer getIsActivity() {
        return isActivity;
    }

    public void setIsActivity(Integer isActivity) {
        this.isActivity = isActivity;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
