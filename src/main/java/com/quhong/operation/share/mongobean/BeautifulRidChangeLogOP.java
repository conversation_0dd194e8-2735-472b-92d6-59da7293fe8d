package com.quhong.operation.share.mongobean;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * 靓号记录
 * <AUTHOR>
 * @date 2020/6/13
 */
@Document(collection = "beautiful_rid_change_log")
public class BeautifulRidChangeLogOP implements Serializable {

    private static final long serialVersionUID = -3762970227454913077L;
    //用户uid
    private String uid;
    //用户真实的rid
    @Field("real_rid")
    private Integer realRid;
    //修改之前的靓号
    @Field("before_rid")
    private Integer beforeRid;
    //修改之后的靓号
    @Field("after_rid")
    private Integer afterRid;
    //是否是admin修改
    @Field("is_admin_handle")
    private Integer isAdminHandle;
    //当前变动的荣誉等级，只有非admin申请靓号或者更改靓号才有记录，其余情况都为0
    @Field("honor_level")
    private Integer honorLevel;
    //更改之前的rid 换字母靓号类型才有
    @Field("bf_alpha_rid")
    private String bfAlphaRid;
    //更改之后的rid 换字母靓号类型才有
    @Field("af_alpha_rid")
    private String afAlphaRid;
    //记录时间
    @Field("c_time")
    private Integer cTime;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getRealRid() {
        return realRid;
    }

    public void setRealRid(Integer realRid) {
        this.realRid = realRid;
    }

    public Integer getBeforeRid() {
        return beforeRid;
    }

    public void setBeforeRid(Integer beforeRid) {
        this.beforeRid = beforeRid;
    }

    public Integer getAfterRid() {
        return afterRid;
    }

    public void setAfterRid(Integer afterRid) {
        this.afterRid = afterRid;
    }

    public Integer getIsAdminHandle() {
        return isAdminHandle;
    }

    public void setIsAdminHandle(Integer isAdminHandle) {
        this.isAdminHandle = isAdminHandle;
    }

    public Integer getHonorLevel() {
        return honorLevel;
    }

    public void setHonorLevel(Integer honorLevel) {
        this.honorLevel = honorLevel;
    }

    public Integer getcTime() {
        return cTime;
    }

    public void setcTime(Integer cTime) {
        this.cTime = cTime;
    }

    public String getBfAlphaRid() {
        return bfAlphaRid;
    }

    public void setBfAlphaRid(String bfAlphaRid) {
        this.bfAlphaRid = bfAlphaRid;
    }

    public String getAfAlphaRid() {
        return afAlphaRid;
    }

    public void setAfAlphaRid(String afAlphaRid) {
        this.afAlphaRid = afAlphaRid;
    }
}
