package com.quhong.operation.share.mongobean;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/8/15
 */
@Deprecated
@Document(collection = "party_girl_send_bean_log")
public class PartyGirlSendBeanLog implements Serializable {

    private static final long serialVersionUID = -3840964699252630486L;

    private ObjectId _id;

    private String uid;

    @Field("in_room_time")
    private Integer inRoomTime;

    private Integer beans = 0;

    private Long ctime;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getInRoomTime() {
        return inRoomTime;
    }

    public void setInRoomTime(Integer inRoomTime) {
        this.inRoomTime = inRoomTime;
    }

    public Integer getBeans() {
        return beans;
    }

    public void setBeans(Integer beans) {
        this.beans = beans;
    }

    public Long getCtime() {
        return ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    @Override
    public String toString() {
        return "PartyGirlSendBeanLog{" +
                "_id='" + _id + '\'' +
                ", uid=" + uid +
                ", inRoomTime=" + inRoomTime +
                ", beans=" + beans +
                ", ctime=" + ctime +
                '}';
    }
}
