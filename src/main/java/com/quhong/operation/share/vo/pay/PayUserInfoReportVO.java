package com.quhong.operation.share.vo.pay;

import com.alibaba.excel.annotation.ExcelProperty;

public class PayUserInfoReportVO {

    @ExcelProperty("排序")
    private int order;
    @ExcelProperty("日期")
    private String date;
    @ExcelProperty("大R数量")
    private int bigRNum;
    @ExcelProperty("中R数量")
    private int middleRNum;
    @ExcelProperty("小R数量")
    private int smallRNum;
    @ExcelProperty("半流失用户数")
    private int halfLoss;
    @ExcelProperty("准备流失用户数")
    private int readyLoss;
    @ExcelProperty("重度流失用户数")
    private int severeLoss;
    @ExcelProperty("完全流失用户数")
    private int allLoss;

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getBigRNum() {
        return bigRNum;
    }

    public void setBigRNum(int bigRNum) {
        this.bigRNum = bigRNum;
    }

    public int getMiddleRNum() {
        return middleRNum;
    }

    public void setMiddleRNum(int middleRNum) {
        this.middleRNum = middleRNum;
    }

    public int getSmallRNum() {
        return smallRNum;
    }

    public void setSmallRNum(int smallRNum) {
        this.smallRNum = smallRNum;
    }

    public int getHalfLoss() {
        return halfLoss;
    }

    public void setHalfLoss(int halfLoss) {
        this.halfLoss = halfLoss;
    }

    public int getReadyLoss() {
        return readyLoss;
    }

    public void setReadyLoss(int readyLoss) {
        this.readyLoss = readyLoss;
    }

    public int getSevereLoss() {
        return severeLoss;
    }

    public void setSevereLoss(int severeLoss) {
        this.severeLoss = severeLoss;
    }

    public int getAllLoss() {
        return allLoss;
    }

    public void setAllLoss(int allLoss) {
        this.allLoss = allLoss;
    }
}
