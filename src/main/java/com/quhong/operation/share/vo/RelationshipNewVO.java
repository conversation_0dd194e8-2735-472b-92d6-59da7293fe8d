package com.quhong.operation.share.vo;

import com.alibaba.excel.annotation.ExcelProperty;


public class RelationshipNewVO {

    @ExcelProperty("日期")
    private String date;
    @ExcelProperty("新增用户")
    private int newUser; // 新增用户
    @ExcelProperty("点击关注/喜欢人数")
    private int newStatPerson; // 点击关注/喜欢人数
    @ExcelProperty("关注/喜欢总用户数")
    private int newStatCount; // 关注/喜欢总用户数

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getNewUser() {
        return newUser;
    }

    public void setNewUser(int newUser) {
        this.newUser = newUser;
    }

    public int getNewStatPerson() {
        return newStatPerson;
    }

    public void setNewStatPerson(int newStatPerson) {
        this.newStatPerson = newStatPerson;
    }

    public int getNewStatCount() {
        return newStatCount;
    }

    public void setNewStatCount(int newStatCount) {
        this.newStatCount = newStatCount;
    }
}
