package com.quhong.operation.share.vo;

import com.alibaba.fastjson.JSONObject;

public class GiftVO {
    private int rid;
    private String gname;                  //  礼物名称
    private String gnamear;                //  礼物名称阿语
    private int gptype;                    //  礼物所属面板类型
    private int gtype;                     //  礼物货币类型 1: diamond 2: gold 3:vip
    private int cacheGtype;                //  缓存礼物货币类型 1: diamond 2: gold 3:vip
    private int gatype;                    //  礼物播放类型 1：普通礼物  2：互动礼物  3：全屏礼物  5: 宝宝特效  6:国旗礼物  7:svg 礼物 8:mp4礼物
    private int grtype;
    private int gweight;
    private int gplatform;                 // 是否平台礼物
    private int forder;                    // 房间礼物排序字段
    private String gicon;                  // 房间礼物图标
    private String preview;                // 礼物预览图
    private int iconSize;                  //  礼物图标size
    private int price;                     //  礼物价格
    private int cachePrice;                //  礼物缓存价格
    private int status;                    // 房间礼物状态
    private String actionUrl;              // 动画url
    private String audioUrl;               // 音效url
    private String fusionUrl;              // 融合图层Url
    private Integer hot;                   // 1: 热门礼物  0: 非热门礼物 字段可能不存在
    private int gtime;                     //  礼物动画时长
    private int scheduleTime;              //  是否开启定时上下线 0: 关闭  1: 开启
    private int upTime;                    //  上线时间戳
    private int downTime;                  //  下线时间戳
    private int gsorder;                   //  私信礼物排序
    private int gstatus;                   //  私信礼物状态
    private ZipInfoVO zipInfoVO;           // 礼物信息说明
    private long zipSize;                   //  zip包大小
    private long ctime;                    //  礼物创建时间
    private int level;                     // 特权礼物等级
    private int isFusionAnimation;         // 是融合动画
    private int totalNum;                  // 已下发数量
    private int parentId;                  // 所属父类礼物id
    private int activityStatus;            // 是否处于某活动中的礼物  0: 否  1: 是
    private int bagGift;                   // 是否背包礼物  0: 否  1: 是
    private String fusionId;               // 融合动画指定id

    public static class ZipInfoVO{
        private String url; // 资源包地址
        private String md5;
        private Integer ztype; // 1 恶魔礼物 2 天使礼物 3 新版整蛊礼物 4 带礼物介绍的礼物 5 幸运礼物 6变声礼物 7盲盒礼物
        private Integer showDetail; // 是否展示礼物详情 0不展示 1展示普通礼物banner 2表示展示盲盒礼物banner 3奖池类幸运礼物

        private String descUrl; // h5介绍页
        private String desc; // 礼物介绍英语
        private String descAr; // 礼物介绍阿语
        private String propIcon; // 道具说明图片
        private Integer hot; // 1: 热门礼物  0: 非热门礼物 字段可能不存在
        private Integer jackpot; // 1奖池类幸运礼物

        private Integer webType; // 0=跳转、1=半屏
        private Integer width; // 礼物详情宽度，字段可能不存在
        private Integer height; // 礼物详情高度，字段可能不存在

        private Integer level; // 特权礼物等级

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getMd5() {
            return md5;
        }

        public void setMd5(String md5) {
            this.md5 = md5;
        }

        public Integer getZtype() {
            return ztype;
        }

        public void setZtype(Integer ztype) {
            this.ztype = ztype;
        }

        public Integer getShowDetail() {
            return showDetail;
        }

        public void setShowDetail(Integer showDetail) {
            this.showDetail = showDetail;
        }

        public String getDescUrl() {
            return descUrl;
        }

        public void setDescUrl(String descUrl) {
            this.descUrl = descUrl;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public String getDescAr() {
            return descAr;
        }

        public void setDescAr(String descAr) {
            this.descAr = descAr;
        }

        public String getPropIcon() {
            return propIcon;
        }

        public void setPropIcon(String propIcon) {
            this.propIcon = propIcon;
        }

        public Integer getHot() {
            return hot;
        }

        public void setHot(Integer hot) {
            this.hot = hot;
        }

        public Integer getJackpot() {
            return jackpot;
        }

        public void setJackpot(Integer jackpot) {
            this.jackpot = jackpot;
        }

        public Integer getWebType() {
            return webType;
        }

        public void setWebType(Integer webType) {
            this.webType = webType;
        }

        public Integer getWidth() {
            return width;
        }

        public void setWidth(Integer width) {
            this.width = width;
        }

        public Integer getHeight() {
            return height;
        }

        public void setHeight(Integer height) {
            this.height = height;
        }

        public Integer getLevel() {
            return level;
        }

        public void setLevel(Integer level) {
            this.level = level;
        }

        @Override
        public String toString() {
            return "ZipInfoVO{" +
                    "url='" + url + '\'' +
                    ", md5='" + md5 + '\'' +
                    ", ztype=" + ztype +
                    ", showDetail=" + showDetail +
                    ", descUrl='" + descUrl + '\'' +
                    ", desc='" + desc + '\'' +
                    ", descAr='" + descAr + '\'' +
                    ", propIcon='" + propIcon + '\'' +
                    ", hot=" + hot +
                    ", webType=" + webType +
                    ", width=" + width +
                    ", height=" + height +
                    ", level=" + level +
                    '}';
        }
    }




    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public String getGname() {
        return gname;
    }

    public void setGname(String gname) {
        this.gname = gname;
    }

    public String getGnamear() {
        return gnamear;
    }

    public void setGnamear(String gnamear) {
        this.gnamear = gnamear;
    }

    public int getGptype() {
        return gptype;
    }

    public void setGptype(int gptype) {
        this.gptype = gptype;
    }

    public int getGtype() {
        return gtype;
    }

    public void setGtype(int gtype) {
        this.gtype = gtype;
    }

    public int getCacheGtype() {
        return cacheGtype;
    }

    public void setCacheGtype(int cacheGtype) {
        this.cacheGtype = cacheGtype;
    }

    public int getGatype() {
        return gatype;
    }

    public void setGatype(int gatype) {
        this.gatype = gatype;
    }

    public int getGrtype() {
        return grtype;
    }

    public void setGrtype(int grtype) {
        this.grtype = grtype;
    }

    public int getGweight() {
        return gweight;
    }

    public void setGweight(int gweight) {
        this.gweight = gweight;
    }

    public int getGplatform() {
        return gplatform;
    }

    public void setGplatform(int gplatform) {
        this.gplatform = gplatform;
    }

    public int getForder() {
        return forder;
    }

    public void setForder(int forder) {
        this.forder = forder;
    }

    public String getGicon() {
        return gicon;
    }

    public void setGicon(String gicon) {
        this.gicon = gicon;
    }

    public String getPreview() {
        return preview;
    }

    public void setPreview(String preview) {
        this.preview = preview;
    }

    public int getIconSize() {
        return iconSize;
    }

    public void setIconSize(int iconSize) {
        this.iconSize = iconSize;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public int getCachePrice() {
        return cachePrice;
    }

    public void setCachePrice(int cachePrice) {
        this.cachePrice = cachePrice;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getActionUrl() {
        return actionUrl;
    }

    public void setActionUrl(String actionUrl) {
        this.actionUrl = actionUrl;
    }

    public String getAudioUrl() {
        return audioUrl;
    }

    public void setAudioUrl(String audioUrl) {
        this.audioUrl = audioUrl;
    }

    public String getFusionUrl() {
        return fusionUrl;
    }

    public void setFusionUrl(String fusionUrl) {
        this.fusionUrl = fusionUrl;
    }

    public Integer getHot() {
        return hot;
    }

    public void setHot(Integer hot) {
        this.hot = hot;
    }

    public int getGtime() {
        return gtime;
    }

    public void setGtime(int gtime) {
        this.gtime = gtime;
    }

    public int getScheduleTime() {
        return scheduleTime;
    }

    public void setScheduleTime(int scheduleTime) {
        this.scheduleTime = scheduleTime;
    }

    public int getUpTime() {
        return upTime;
    }

    public void setUpTime(int upTime) {
        this.upTime = upTime;
    }

    public int getDownTime() {
        return downTime;
    }

    public void setDownTime(int downTime) {
        this.downTime = downTime;
    }

    public int getGsorder() {
        return gsorder;
    }

    public void setGsorder(int gsorder) {
        this.gsorder = gsorder;
    }

    public int getGstatus() {
        return gstatus;
    }

    public void setGstatus(int gstatus) {
        this.gstatus = gstatus;
    }

    public ZipInfoVO getZipInfoVO() {
        return zipInfoVO;
    }

    public void setZipInfoVO(ZipInfoVO zipInfoVO) {
        this.zipInfoVO = zipInfoVO;
    }

    public long getZipSize() {
        return zipSize;
    }

    public void setZipSize(long zipSize) {
        this.zipSize = zipSize;
    }

    public long getCtime() {
        return ctime;
    }

    public void setCtime(long ctime) {
        this.ctime = ctime;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getIsFusionAnimation() {
        return isFusionAnimation;
    }

    public void setIsFusionAnimation(int isFusionAnimation) {
        this.isFusionAnimation = isFusionAnimation;
    }

    public int getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(int totalNum) {
        this.totalNum = totalNum;
    }

    public int getParentId() {
        return parentId;
    }

    public void setParentId(int parentId) {
        this.parentId = parentId;
    }

    public int getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(int activityStatus) {
        this.activityStatus = activityStatus;
    }

    public int getBagGift() {
        return bagGift;
    }

    public void setBagGift(int bagGift) {
        this.bagGift = bagGift;
    }

    public String getFusionId() {
        return fusionId;
    }

    public void setFusionId(String fusionId) {
        this.fusionId = fusionId;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
