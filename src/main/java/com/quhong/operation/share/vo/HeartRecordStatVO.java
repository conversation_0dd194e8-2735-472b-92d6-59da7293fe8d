package com.quhong.operation.share.vo;

import java.io.Serializable;


public class HeartRecordStatVO implements Serializable {

    private static final long serialVersionUID = -1094638604776149785L;

    private int receivePerson;
    private int receiveCount;
    private int sendPerson;
    private int sendCount;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public int getReceivePerson() {
        return receivePerson;
    }

    public void setReceivePerson(int receivePerson) {
        this.receivePerson = receivePerson;
    }

    public int getReceiveCount() {
        return receiveCount;
    }

    public void setReceiveCount(int receiveCount) {
        this.receiveCount = receiveCount;
    }

    public int getSendPerson() {
        return sendPerson;
    }

    public void setSendPerson(int sendPerson) {
        this.sendPerson = sendPerson;
    }

    public int getSendCount() {
        return sendCount;
    }

    public void setSendCount(int sendCount) {
        this.sendCount = sendCount;
    }
}
