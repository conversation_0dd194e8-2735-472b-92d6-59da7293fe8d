package com.quhong.operation.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Pattern;

public class ValidateUtils {

    private static final Logger logger = LoggerFactory.getLogger(ValidateUtils.class);


    /**
     * 判断是否为整数
     * @param str
     * @return
     */
    public static boolean isNumeric(String str) {
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        return pattern.matcher(str).matches();
    }

}
