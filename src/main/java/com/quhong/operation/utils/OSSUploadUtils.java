package com.quhong.operation.utils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.quhong.core.utils.DateHelper;
import com.quhong.exception.CommonH5Exception;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;

public class OSSUploadUtils {

    private static final Logger logger = LoggerFactory.getLogger(OSSUploadUtils.class);

    private static final String OSS_END_POINT = "https://oss-me-east-1.aliyuncs.com";
    private static final String CLOUDCDN_DOMAIN = "https://cloudcdn.qmovies.tv/";

    private static final String accessKeyId = "LTAI5tPAmfMkNHRaC4XMgHf1";
    private static final String accessKeySecret = "******************************";
    private static final String bucketName = "qhclient";
    private static final String ACTIVITY_PATH = "activity/";
    private static final String REPLACE_STRING ="[\u4E00-\u9FA5|\\！|\\，|\\。|\\（|\\）|\\《|\\》|\\“|\\”|\\？|\\：|\\；|\\【|\\】|\\ |\\、]";
    private static OSS ossClient = null;

    static  {
        ossClient = new OSSClientBuilder().build(OSS_END_POINT, accessKeyId, accessKeySecret);
    }

    public static String upload(MultipartFile file) {
        return upload(file, ACTIVITY_PATH);
    }

    public static String upload(MultipartFile file, String path) {

        String fileUrl = "";
        // 创建OSSClient实例。
        try {
            String originFileName = file.getOriginalFilename().replaceAll(REPLACE_STRING, "");
            String ossFilePath = path + "op_" + DateHelper.getNowSeconds() + "_" + originFileName;
            ossClient.putObject(bucketName, ossFilePath, file.getInputStream());
            fileUrl = CLOUDCDN_DOMAIN + ossFilePath;
        } catch (IOException e) {
            logger.error("uploading failed {}", e.getMessage(), e);
            throw new CommonH5Exception();
        }

        return fileUrl;
    }

    public static void download(HttpServletResponse response, String bucketName, String path) {
        try {
            response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(path, "UTF-8"));
            int len;
            byte[] buffer = new byte[1024];
            ServletOutputStream outputStream = response.getOutputStream();
            OSSObject ossObject = ossClient.getObject(bucketName, path);
            InputStream inputStream = ossObject.getObjectContent();
            while ((len = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }
            //关闭流
            outputStream.close();
            inputStream.close();
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }
}
