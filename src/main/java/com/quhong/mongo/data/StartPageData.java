package com.quhong.mongo.data;

import com.quhong.mongo.dao.StartPageDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection = StartPageDao.TABLE_NAME)
public class StartPageData {

    @Id
    private ObjectId _id;
    private String url;
    @Field("url_ar")
    private String urlAr;

    @Field("iphonex_url")
    private String iphonexUrl;

    @Field("iphonex_urlar")
    private String iphonexUrlAr;

    private String link;
    private Integer skip;
    private Integer valid;

    @Field("valid_time")
    private Integer validTime;

    @Field("flash_id")
    private Integer flashId;
    private String title;
    @Field("title_ar")
    private String titleAr;
    @Field("isdelete")
    private Integer isDelete;
    private Integer slang;
    private Integer atype;
    @Field("room_id")
    private String roomId;
    @Field("jump_aid")
    private String jumpAid;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUrlAr() {
        return urlAr;
    }

    public void setUrlAr(String urlAr) {
        this.urlAr = urlAr;
    }

    public String getIphonexUrl() {
        return iphonexUrl;
    }

    public void setIphonexUrl(String iphonexUrl) {
        this.iphonexUrl = iphonexUrl;
    }

    public String getIphonexUrlAr() {
        return iphonexUrlAr;
    }

    public void setIphonexUrlAr(String iphonexUrlAr) {
        this.iphonexUrlAr = iphonexUrlAr;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public Integer getSkip() {
        return skip;
    }

    public void setSkip(Integer skip) {
        this.skip = skip;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public Integer getValidTime() {
        return validTime;
    }

    public void setValidTime(Integer validTime) {
        this.validTime = validTime;
    }

    public Integer getFlashId() {
        return flashId;
    }

    public void setFlashId(Integer flashId) {
        this.flashId = flashId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleAr() {
        return titleAr;
    }

    public void setTitleAr(String titleAr) {
        this.titleAr = titleAr;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getSlang() {
        return slang;
    }

    public void setSlang(Integer slang) {
        this.slang = slang;
    }

    public Integer getAtype() {
        return atype;
    }

    public void setAtype(Integer atype) {
        this.atype = atype;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getJumpAid() {
        return jumpAid;
    }

    public void setJumpAid(String jumpAid) {
        this.jumpAid = jumpAid;
    }
}
