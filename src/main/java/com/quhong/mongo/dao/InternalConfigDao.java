package com.quhong.mongo.dao;

import com.quhong.cache.CacheMap;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.InternalConfigData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/8/22
 */
@Component
public class InternalConfigDao {

    private static final Logger logger = LoggerFactory.getLogger(InternalConfigDao.class);

    public static final String TABLE_NAME = "internal_config";

    private static final long CACHE_TIME_MILLIS = 3 * 60 * 1000L;

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    private final CacheMap<Integer, InternalConfigData> cacheMap;

    public InternalConfigDao() {
        cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public InternalConfigData findDataFromCache(int os) {
        InternalConfigData data = cacheMap.getData(os);
        if (data != null) {
            return data;
        }
        data = findData(os);
        if (data != null) {
            cacheMap.cacheData(os, data);
        }
        return data;
    }

    public InternalConfigData findData(int os) {
        try {
            Criteria criteria = Criteria.where("os").is(os).and("status").is(1);
            return mongoTemplate.findOne(new Query(criteria), InternalConfigData.class);
        } catch (Exception e) {
            logger.error("find internal config data error. {}", e.getMessage(), e);
        }
        return null;
    }

    public void save(InternalConfigData data) {
        try {
            mongoTemplate.save(data);
            cacheMap.remove(data.getOs());
        } catch (Exception e) {
            logger.error("save internal config data error. {}", e.getMessage(), e);
        }
    }
}
