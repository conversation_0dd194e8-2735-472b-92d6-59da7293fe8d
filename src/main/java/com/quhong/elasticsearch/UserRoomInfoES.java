package com.quhong.elasticsearch;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;

import java.io.Serializable;

/**
 * elasticsearch 用户及房间信息
 */
@Document(indexName = "user_room_info_es", type = "_doc")
public class UserRoomInfoES implements Serializable {
    private static final long serialVersionUID = -3417596718935957637L;

    @Id
    private String id;
    // 用户uid、 roomId
    private String searchId;
    private String nameInfo;
    private Integer ridInfo;

    private String searchContent;
    private Integer storeType;

    // 更新时间
    private Integer mtime;
    private Integer ctime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSearchId() {
        return searchId;
    }

    public void setSearchId(String searchId) {
        this.searchId = searchId;
    }

    public String getNameInfo() {
        return nameInfo;
    }

    public void setNameInfo(String nameInfo) {
        this.nameInfo = nameInfo;
    }

    public Integer getRidInfo() {
        return ridInfo;
    }

    public void setRidInfo(Integer ridInfo) {
        this.ridInfo = ridInfo;
    }

    public String getSearchContent() {
        return searchContent;
    }

    public void setSearchContent(String searchContent) {
        this.searchContent = searchContent;
    }

    public Integer getStoreType() {
        return storeType;
    }

    public void setStoreType(Integer storeType) {
        this.storeType = storeType;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
