package com.quhong.redis;

import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.LuckyBoxInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/21
 */
@Component
public class LuckyBoxRedis {

    private static final Logger logger = LoggerFactory.getLogger(LuckyBoxRedis.class);

    public static final String SEND_RECORD = "send";
    public static final String RECEIVE_RECORD = "receive";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    /**
     * 保存红包到用户池子
     */
    public void addBoxMoneyInUserPool(String boxId, List<Integer> moneyList) {
        if (CollectionUtils.isEmpty(moneyList)) {
            logger.info("moneyList is empty.");
            return;
        }
        String key = getBoxMoneyKey(boxId);
        try {
            List<String> strMoneyList = moneyList.stream().map(String::valueOf).collect(Collectors.toList());
            redisTemplate.opsForList().leftPushAll(key, strMoneyList);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_SEVEN, TimeUnit.DAYS);
            logger.info("boxId={} boxNum={} sumMoney={} moneyList={}", boxId, moneyList.size(), moneyList.stream().reduce(Integer::sum).orElse(0), Arrays.toString(moneyList.toArray()));
        } catch (Exception e) {
            logger.error("add box money in user poor error. boxId={} {}", boxId, e.getMessage(), e);
        }
    }

    /**
     * 保存红包到机器人池子
     */
    public void addBoxMoneyInRobotPool(String boxId, List<Integer> moneyList) {
        if (CollectionUtils.isEmpty(moneyList)) {
            logger.info("moneyList is empty.");
            return;
        }
        String key = getRobotBoxMoneyKey(boxId);
        try {
            List<String> strMoneyList = moneyList.stream().map(String::valueOf).collect(Collectors.toList());
            redisTemplate.opsForList().leftPushAll(key, strMoneyList);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_SEVEN, TimeUnit.DAYS);
            logger.info("boxId={} boxNum={} sumMoney={} moneyList={}", boxId, moneyList.size(), moneyList.stream().reduce(Integer::sum).orElse(0), Arrays.toString(moneyList.toArray()));
        } catch (Exception e) {
            logger.error("add box money in robot poor error. boxId={} {}", boxId, e.getMessage(), e);
        }
    }

    /**
     * 获取红包
     */
    public int getBoxMoney(String boxId, boolean isRobot) {
        int boxMoney;
        if (isRobot) {
            boxMoney = getBoxMoneyFromRobotPool(boxId);
            if (boxMoney == 0) {
                boxMoney = getBoxMoneyFromUserPool(boxId);
            }
        } else {
            boxMoney = getBoxMoneyFromUserPool(boxId);
            if (boxMoney == 0) {
                boxMoney = getBoxMoneyFromRobotPool(boxId);
            }
        }
        return boxMoney;
    }

    private int getBoxMoneyFromUserPool(String boxId) {
        try {
            String value = redisTemplate.opsForList().rightPop(getBoxMoneyKey(boxId));
            return !StringUtils.isEmpty(value) ? Integer.parseInt(value) : 0;
        } catch (Exception e) {
            logger.error("get box money from user pool error. boxId={} {}", boxId, e.getMessage(), e);
            return 0;
        }
    }

    private int getBoxMoneyFromRobotPool(String boxId) {
        try {
            String value = redisTemplate.opsForList().rightPop(getRobotBoxMoneyKey(boxId));
            return !StringUtils.isEmpty(value) ? Integer.parseInt(value) : 0;
        } catch (Exception e) {
            logger.error("get box money from robot pool error. boxId={} {}", boxId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取所有未领取的红包
     */
    public List<Integer> getAllBoxMoneyAndRemove(String boxId) {
        String userPoolKey = getBoxMoneyKey(boxId);
        String robotPoolKey = getRobotBoxMoneyKey(boxId);
        try {
            List<String> list = new ArrayList<>();
            List<String> userPoolMoneyList = redisTemplate.opsForList().range(userPoolKey, 0, -1);
            // 获取后立即清除
            redisTemplate.delete(userPoolKey);
            if (!CollectionUtils.isEmpty(userPoolMoneyList)) {
                list.addAll(userPoolMoneyList);
            }
            logger.info("list={}", Arrays.toString(list.toArray()));
            List<String> robotPoolMoneyList = redisTemplate.opsForList().range(robotPoolKey, 0, -1);
            // 获取后立即清除
            redisTemplate.delete(robotPoolKey);
            if (!CollectionUtils.isEmpty(robotPoolMoneyList)) {
                list.addAll(robotPoolMoneyList);
            }
            logger.info("list={}", Arrays.toString(list.toArray()));
            return list.stream().map(Integer::parseInt).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("get all box money from redis error. boxId={} {}", boxId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取指定红包数量
     */
    public int getBoxNum(String boxId) {
        try {
            Long userPoolSize = redisTemplate.opsForList().size(getBoxMoneyKey(boxId));
            int userPoolBoxNum = userPoolSize != null ? userPoolSize.intValue() : 0;
            Long robotPoolSize = redisTemplate.opsForList().size(getRobotBoxMoneyKey(boxId));
            int robotPoolBoxNum = robotPoolSize != null ? robotPoolSize.intValue() : 0;
            return userPoolBoxNum + robotPoolBoxNum;
        } catch (Exception e) {
            logger.error("get box num from redis error. boxId={} {}", boxId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 保存当天红包发送或领取总金额
     */
    public void saveSendOrReceiveRecord(String uid, String recordType, int money) {
        try {
            String key = getBoxMoneyRecordKey(uid);
            redisTemplate.opsForHash().increment(key, recordType, money);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveSendOrReceiveRecord error. uid={}, recordType={}, money={} {}", uid, recordType, money, e.getMessage(), e);
        }
    }

    /**
     * 获取当天红包发送或领取总金额
     */
    public int getSendOrReceiveMoney(String uid, String recordType) {
        try {
            String value = (String) redisTemplate.opsForHash().get(getBoxMoneyRecordKey(uid), recordType);
            return !StringUtils.isEmpty(value) ? Integer.parseInt(value) : 0;
        } catch (Exception e) {
            logger.error("getSendOrReceiveMoney error. uid={}, recordType={} {}", uid, recordType, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 保存用户领取红包记录
     */
    public void saveGainLuckyBoxReward(String uid, String boxId) {
        try {
            String key = getGainLuckyBoxRewardKey(uid);
            redisTemplate.opsForZSet().add(key, boxId, DateHelper.getNowSeconds());
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveGainLuckyBoxReward error. uid={} boxId={} {}", uid, boxId, e.getMessage(), e);
        }
    }

    /**
     * 获取用户领取红包记录
     */
    public int getUserGainLuckyBoxNum(String uid, int startTime, int endTime) {
        try {
            Long count = redisTemplate.opsForZSet().count(getGainLuckyBoxRewardKey(uid), startTime, endTime);
            return count != null ? count.intValue() : 0;
        } catch (Exception e) {
            logger.error("getUserGainLuckyBoxNum error. uid={} startTime={} endTime={} {}", uid, startTime, endTime, e.getMessage(), e);
            return 0;
        }
    }

    public void saveRoomIdByBoxId(String boxId, String roomId) {
        String key = getLuckyBoxRoomKey();
        try {
            redisTemplate.opsForHash().put(key, boxId, roomId);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveRoomIdByBoxId error. boxId={} roomId={} {}", boxId, roomId, e.getMessage(), e);
        }
    }

    /**
     * 通过boxId查找roomId
     */
    public String getRoomIdByBoxId(String boxId) {
        try {
            return (String) redisTemplate.opsForHash().get(getLuckyBoxRoomKey(), boxId);
        } catch (Exception e) {
            logger.error("getRoomIdByBoxId error. boxId={} {}", boxId, e.getMessage(), e);
            return "";
        }
    }

    public void removeLuckyBoxRoom(String roomId, String boxId) {
        try {
            redisTemplate.opsForHash().delete(getLuckyBoxRoomKey(), boxId, roomId);
        } catch (Exception e) {
            logger.error("removeLuckyBoxRoom error. roomId={} boxId={} {}", roomId, boxId, e.getMessage(), e);
        }
    }

    /**
     * 保存红包信息
     */
    public void saveLuckyBoxInfo(LuckyBoxInfo luckyBoxInfo) {
        String key = getRoomLuckyBoxDataKey(luckyBoxInfo.getRoom_id());
        try {
            redisTemplate.opsForHash().put(key, luckyBoxInfo.getBoxId(), JSONObject.toJSONString(luckyBoxInfo));
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveLuckyBoxInfo error. roomId={} boxId={} {}", luckyBoxInfo.getRoom_id(), luckyBoxInfo.getBoxId(), e.getMessage(), e);
        }
    }

    /**
     * 获取红包信息
     */
    public LuckyBoxInfo getLuckyBoxInfo(String roomId, String boxId) {
        try {
            String value = (String) redisTemplate.opsForHash().get(getRoomLuckyBoxDataKey(roomId), boxId);
            if (StringUtils.isEmpty(value)) {
                return null;
            }
            return JSONObject.parseObject(value, LuckyBoxInfo.class);
        } catch (Exception e) {
            logger.error("getLuckyBoxInfo error. roomId={} boxId={} {}", roomId, boxId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 移除红包信息
     */
    public void removeLuckyBoxInfo(String roomId, String boxId) {
        try {
            redisTemplate.opsForHash().delete(getRoomLuckyBoxDataKey(roomId), boxId);
        } catch (Exception e) {
            logger.error("removeLuckyBoxInfo error. roomId={} boxId={} {}", roomId, boxId, e.getMessage(), e);
        }
    }

    /**
     * 获取房间内所有红包信息
     */
    public Map<String, LuckyBoxInfo> getRoomAllLuckyBoxInfo(String roomId) {
        Map<String, LuckyBoxInfo> resultMap = new HashMap<>();
        try {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(getRoomLuckyBoxDataKey(roomId));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                resultMap.put((String) entry.getKey() , JSONObject.parseObject((String)entry.getValue(), LuckyBoxInfo.class));
            }
            return resultMap;
        } catch (Exception e) {
            logger.error("getRoomAllLuckyBoxInfo error. roomId={} {}", roomId, e.getMessage(), e);
            return resultMap;
        }
    }

    /**
     * 检查是否已经领过当前红包了
     */
    public boolean hasGainedLuckyBox(String boxId, String uid) {
        try {
            return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(getLuckyBoxGainRecord(boxId), uid));
        } catch (Exception e) {
            logger.error("check hasGainedLuckyBox error. boxId={} uid={} {}", boxId, uid, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 保存红包领取记录
     */
    public void saveLuckyBoxGainRecord(String boxId, String uid) {
        String key = getLuckyBoxGainRecord(boxId);
        try {
            redisTemplate.opsForSet().add(key, uid);
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_SEVEN, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveLuckyBoxGainRecord error. boxId={} uid={} {}", boxId, uid, e.getMessage(), e);
        }
    }

    public void setBoxTimerWaiting(String boxId, int endTime) {
        try {
            redisTemplate.opsForZSet().add(getBoxTimerWaitingKey(), boxId, endTime);
        } catch (Exception e) {
            logger.error("set lucky box timer waiting error. boxId={} endTime={} {}", boxId, endTime, e.getMessage());
        }
    }

    public Set<String> getWaitingEndBoxIds(int timestamp) {
        try {
            return redisTemplate.opsForZSet().rangeByScore(getBoxTimerWaitingKey(), 0, timestamp);
        } catch (Exception e) {
            logger.error("get waiting end lucky box ids error. timestamp={} {}", timestamp, e.getMessage());
            return null;
        }
    }

    public void removeBoxTimerWaiting(String boxId) {
        try {
            redisTemplate.opsForZSet().remove(getBoxTimerWaitingKey(), boxId);
        } catch (Exception e) {
            logger.error("remove lucky box timer waiting error. boxId={} {}", boxId, e.getMessage(), e);
        }
    }

    /**
     * 红包队列(优先给用户分配)
     */
    private String getBoxMoneyKey(String boxId) {
        return "list:boxMoney_" + boxId;
    }

    /**
     * 红包队列(优先给机器人分配)
     */
    private String getRobotBoxMoneyKey(String boxId) {
        return "list:robotBoxMoney_" + boxId;
    }

    /**
     * 当天红包领取或发送总金额记录
     */
    private String getBoxMoneyRecordKey(String uid) {
        return "hash:boxMoneyRecord_" + DateHelper.ARABIAN.formatDateInDay() + "_" + uid;
    }

    /**
     * 用户领取红包记录
     */
    private String getGainLuckyBoxRewardKey(String uid) {
        return "zset:gainLuckyBoxReward_" + uid;
    }

    /**
     * 红包映射房间 key:boxId value:roomId
     */
    private String getLuckyBoxRoomKey() {
        return "hash:luckyBoxRoom";
    }

    /**
     * 红包数据信息
     */
    private String getRoomLuckyBoxDataKey(String roomId) {
        return "hash:luckyBoxInRoom_" + roomId;
    }

    /**
     * 红包被领取记录
     */
    private String getLuckyBoxGainRecord(String boxId) {
        return "set:luckyBoxGainRecord_" + boxId;
    }

    /**
     * 红包过期时间
     */
    private String getBoxTimerWaitingKey() {
        return "zset:LuckyBoxTimerWaiting";
    }
}
