package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Component
public class RoomDevoteRedis {
    private static final Logger logger = LoggerFactory.getLogger(RoomDevoteRedis.class);

//    public static final String ROOM_BEANS_DEVOTE_ONE_KEY = "room_beans_devote_one";
//    public static final String ROOM_BEANS_DEVOTE_TOTAL_KEY = "room_beans_devote_total";
//    private static final String ROOM_MEMBER_COST_KEY = "room_member_cost";
//
////    @Resource(name = DataRedisBean.ROOM_DEVOTE_CACHE)
////    private StringRedisTemplate devoteCacheTemplate;
////    @Resource(name = DataRedisBean.ROOM_BEANS_DEVOTE)
////    private StringRedisTemplate devoteTemplate;
//
//    public void delRoomBeansDevoteTotal(String roomId, String key) {
//        try {
//            devoteCacheTemplate.opsForHash().delete(key, roomId);
//        } catch (Exception e) {
//            logger.error("delete room beans devote total data from redis error. roomId={}", roomId);
//        }
//    }
//
//    public Map<String, Integer> getAllRoomBeansDevoteTotal(String key) {
//        Map<String, Integer> resultMap = new HashMap<>();
//        try {
//            Map<Object, Object> entries = devoteCacheTemplate.opsForHash().entries(key);
//            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
//                try {
//                    resultMap.put(String.valueOf(entry.getKey()), Integer.parseInt(String.valueOf(entry.getValue())));
//                } catch (Exception e) {
//                    logger.error("parse room beans devote error. key={} value={}", entry.getKey(), entry.getValue(), e);
//                }
//            }
//        } catch (Exception e) {
//            logger.error("get all room beans devote total error. {}", e.getMessage(), e);
//        }
//        return resultMap;
//    }
//
//    public void delRoomBeansDevoteOne(String uid, String roomId, String key) {
//        try {
//            devoteCacheTemplate.opsForHash().delete(key, getRoomBeansOneHashKey(roomId, uid));
//        } catch (Exception e) {
//            logger.error("delete room beans devote one data from redis error. uid={} roomId={}", uid, roomId);
//        }
//    }
//
//    public Map<String, Integer> getAllRoomBeansDevoteOne(String key) {
//        Map<String, Integer> resultMap = new HashMap<>();
//        try {
//            Map<Object, Object> entries = devoteCacheTemplate.opsForHash().entries(key);
//            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
//                try {
//                    resultMap.put(String.valueOf(entry.getKey()), Integer.parseInt(String.valueOf(entry.getValue())));
//                } catch (Exception e) {
//                    logger.error("parse room beans devote error. key={} value={}", entry.getKey(), entry.getValue(), e);
//                }
//            }
//        } catch (Exception e) {
//            logger.error("get all room beans devote one error. {}", e.getMessage(), e);
//        }
//        return resultMap;
//    }
//
//    public Map<String, Integer> getAllRoomMemberCost() {
//        Map<String, Integer> resultMap = new HashMap<>();
//        try {
//            Map<Object, Object> entries = devoteTemplate.opsForHash().entries(ROOM_MEMBER_COST_KEY);
//            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
//                try {
//                    resultMap.put(String.valueOf(entry.getKey()), Integer.parseInt(String.valueOf(entry.getValue())));
//                } catch (Exception e) {
//                    logger.error("parse room member cost error. key={} value={}", entry.getKey(), entry.getValue(), e);
//                }
//            }
//        } catch (Exception e) {
//            logger.error("get all room member cost error. {}", e.getMessage(), e);
//        }
//        return resultMap;
//    }
//
//    public void delRoomMemberCost(String roomId, String uid) {
//        try {
//            devoteTemplate.opsForHash().delete(ROOM_MEMBER_COST_KEY, getMemberDevoteHashKey(roomId, uid));
//        } catch (Exception e) {
//            logger.error("delete room member cost data from redis error. roomId={}", roomId);
//        }
//    }
//
//    private String getRoomBeansOneHashKey(String roomId, String uid) {
//        return roomId + "_" + uid;
//    }
//
//    private String getMemberDevoteHashKey(String roomId, String uid) {
//        return roomId + "_" + uid;
//    }
}
