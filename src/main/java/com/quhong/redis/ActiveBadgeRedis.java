package com.quhong.redis;

import com.quhong.constant.ActiveBadgeConstant;
import com.quhong.core.utils.DateHelper;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 活跃勋章Redis操作类
 */
@Component
public class ActiveBadgeRedis {
    
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 获取用户累积活跃天数
     */
    public int getUserAccumulateDays(String uid) {
        String key = ActiveBadgeConstant.USER_ACCUMULATE_DAYS_KEY + uid;
        Object value = redisTemplate.opsForValue().get(key);
        return value != null ? (Integer) value : 0;
    }
    
    /**
     * 设置用户累积活跃天数
     */
    public void setUserAccumulateDays(String uid, int days) {
        String key = ActiveBadgeConstant.USER_ACCUMULATE_DAYS_KEY + uid;
        redisTemplate.opsForValue().set(key, days);
    }
    
    /**
     * 获取用户当日麦上时长（分钟）
     */
    public int getUserDailyMicTime(String uid, String dateStr) {
        String key = ActiveBadgeConstant.USER_DAILY_MIC_TIME_KEY + uid + ":" + dateStr;
        Object value = redisTemplate.opsForValue().get(key);
        return value != null ? (Integer) value : 0;
    }
    
    /**
     * 增加用户当日麦上时长
     */
    public void addUserDailyMicTime(String uid, String dateStr, int minutes) {
        String key = ActiveBadgeConstant.USER_DAILY_MIC_TIME_KEY + uid + ":" + dateStr;
        redisTemplate.opsForValue().increment(key, minutes);
        // 设置过期时间为7天
        redisTemplate.expire(key, 7, TimeUnit.DAYS);
    }
    
    /**
     * 获取用户勋章解锁记录
     */
    public String getUserBadgeUnlockDate(String uid, int level) {
        String key = ActiveBadgeConstant.USER_BADGE_UNLOCK_KEY + uid;
        Object value = redisTemplate.opsForHash().get(key, String.valueOf(level));
        return value != null ? value.toString() : null;
    }
    
    /**
     * 设置用户勋章解锁记录
     */
    public void setUserBadgeUnlockDate(String uid, int level, String unlockDate) {
        String key = ActiveBadgeConstant.USER_BADGE_UNLOCK_KEY + uid;
        redisTemplate.opsForHash().put(key, String.valueOf(level), unlockDate);
    }
    
    /**
     * 获取用户所有已解锁的勋章等级
     */
    public Set<Object> getUserUnlockedBadgeLevels(String uid) {
        String key = ActiveBadgeConstant.USER_BADGE_UNLOCK_KEY + uid;
        return redisTemplate.opsForHash().keys(key);
    }
    
    /**
     * 获取用户最后活跃日期
     */
    public String getUserLastActiveDate(String uid) {
        String key = ActiveBadgeConstant.USER_LAST_ACTIVE_DATE_KEY + uid;
        Object value = redisTemplate.opsForValue().get(key);
        return value != null ? value.toString() : null;
    }
    
    /**
     * 设置用户最后活跃日期
     */
    public void setUserLastActiveDate(String uid, String dateStr) {
        String key = ActiveBadgeConstant.USER_LAST_ACTIVE_DATE_KEY + uid;
        redisTemplate.opsForValue().set(key, dateStr);
    }
    
    /**
     * 重置用户累积天数（任务中断时使用）
     */
    public void resetUserAccumulateDays(String uid) {
        setUserAccumulateDays(uid, 0);
    }
    
    /**
     * 检查用户是否今日已有麦位互动
     */
    public boolean hasUserMicInteractionToday(String uid) {
        String dateStr = DateHelper.ARABIAN.formatDateInDay();
        return getUserDailyMicTime(uid, dateStr) > 0;
    }
    
    /**
     * 获取今日未有麦位互动的用户列表（用于推送）
     */
    public Set<String> getTodayInactiveUsers() {
        String pattern = ActiveBadgeConstant.USER_DAILY_MIC_TIME_KEY + "*:" + DateHelper.ARABIAN.formatDateInDay();
        Set<String> activeUsers = redisTemplate.keys(pattern);
        
        // 这里需要结合具体的用户系统来获取所有用户，然后排除今日活跃的用户
        // 实际实现中需要根据具体的业务逻辑来获取用户列表
        return null; // 暂时返回null，需要具体实现
    }
} 