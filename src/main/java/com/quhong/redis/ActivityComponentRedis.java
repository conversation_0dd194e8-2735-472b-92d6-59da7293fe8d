package com.quhong.redis;

import com.quhong.constant.ActivityComponentConstant;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.bo.ComponentCommonBO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Lazy
@Component
public class ActivityComponentRedis extends ComponentRedis {
    private static final Logger logger = LoggerFactory.getLogger(ActivityComponentRedis.class);

    public String getCurrentDay(String activityId) {
        return DateHelper.ARABIAN.formatDateInDay();
        // return activityCommonRedis.getCommonStrValue(getDailyDate(activityId));
    }

    public String getCurrentWeek(String activityId) {
        return DateSupport.ARABIAN.getStrCurrentWeek();
        // return activityCommonRedis.getCommonStrValue(getDailyDate(activityId));
    }

    // 任务领取状态
    public String getUserTaskStatusKey(ComponentCommonBO bo) {
        return String.format("%s:status", bo.getComponentKey());
    }

    // 组任务领取状态
    public String getUserGroupTaskStatusKey(ComponentCommonBO bo) {
        return String.format("%s:status", bo.getTaskMetaId());
    }

    /**
     * 用户任务hash key
     */
    public String getUserTaskHashKey(ComponentCommonBO bo) {
        switch (bo.getPeriod()){
            case ActivityComponentConstant.PERIOD_DAILY:
                String currentDay = getCurrentDay(bo.getActivityId());
                return String.format("hash:activityComponentTask:%s:%s:%s", bo.getActivityId(), bo.getCountId(), currentDay);
            case ActivityComponentConstant.PERIOD_WEEK:
                String currentWeek = getCurrentWeek(bo.getActivityId());
                return String.format("hash:activityComponentTask:%s:%s:%s", bo.getActivityId(), bo.getCountId(), currentWeek);
            default:
                return String.format("hash:activityComponentTask:%s:%s", bo.getActivityId(), bo.getCountId());
        }
    }


    /**
     * 用户任务去重 key
     */
    public String getUserTaskSetKey(ComponentCommonBO bo) {
        switch (bo.getPeriod()){
            case ActivityComponentConstant.PERIOD_DAILY:
                String currentDay = getCurrentDay(bo.getActivityId());
                return String.format("set:activityComponentTask:%s:%s:%s", bo.getActivityId(), bo.getCountId(), currentDay);
            case ActivityComponentConstant.PERIOD_WEEK:
                String currentWeek = getCurrentWeek(bo.getActivityId());
                return String.format("set:activityComponentTask:%s:%s:%s", bo.getActivityId(), bo.getCountId(), currentWeek);
            default:
                return String.format("set:activityComponentTask:%s:%s", bo.getActivityId(), bo.getCountId());
        }
    }


    public String getRankZSetKey(ComponentCommonBO bo) {
        switch (bo.getPeriod()){
            case ActivityComponentConstant.PERIOD_DAILY:
                String currentDay = getCurrentDay(bo.getActivityId());
                return String.format("zset:activityComponentRank:%s:%s:%s:%s", bo.getActivityId(), bo.getCountId(), bo.getCalculateMethod(), currentDay);
            case ActivityComponentConstant.PERIOD_WEEK:
                String currentWeek = getCurrentWeek(bo.getActivityId());
                return String.format("zset:activityComponentRank:%s:%s:%s:%s", bo.getActivityId(), bo.getCountId(), bo.getCalculateMethod(), currentWeek);
            default:
                return String.format("zset:activityComponentRank:%s:%s:%s", bo.getActivityId(), bo.getCountId(), bo.getCalculateMethod());
        }
    }


}
