package com.quhong.sud.service;

import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.CreateGameLogEvent;
import com.quhong.analysis.EventReport;
import com.quhong.api.SudGameApi;
import com.quhong.config.SudGameConfig;
import com.quhong.controllers.SudController;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.queues.SlowTaskQueue;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.*;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.dto.InnerSudGameDTO;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.exception.GameException;
import com.quhong.feign.DataCenterService;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.SudGameData;
import com.quhong.mongo.data.SudGamePlayerData;
import com.quhong.monitor.MonitorSender;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.obj.UNameObject;
import com.quhong.msg.room.RoomLudoKitOutMsg;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.msg.room.RoomSudGameOperateMsg;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.mapper.ustar.RoomMicInfoMapper;
import com.quhong.redis.GameRoomRedis;
import com.quhong.redis.SudGameRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.service.TurntableService;
import com.quhong.sud.data.SudGameConfigInfo;
import com.quhong.sud.dto.*;
import com.quhong.sud.vo.*;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.InnerSudGameVO;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import tech.sud.mgp.auth.api.SudCode;
import tech.sud.mgp.auth.api.SudMGPAuth;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/1
 */
@Service
public class SudGameRoomService extends SlowTaskQueue {

    private static final Logger logger = LoggerFactory.getLogger(SudGameRoomService.class);

    private static final String CREATE_GAME_MSG = "Created the %s game."; // 创建游戏消息
    private static final String CREATE_GAME_MSG_AR = "إنشاء لعبة %s"; // 创建游戏消息阿语
    private static final String CLOSED_GAME_MSG = "%s game is closed."; // 关闭游戏消息
    private static final String CLOSED_GAME_MSG_AR = "تم إغلاق اللعبة %s"; // 关闭游戏消息阿语
    private static final String OTHER_GAME_IN_PROGRESS = "The %s game is playing in the room, please wait for the game to end and try again."; // 有其他游戏正在进行提示
    private static final String OTHER_GAME_IN_PROGRESS_AR = "هناك لعبة %s تلعب في الغرفة ، يرجى الانتظار حتى تنتهي اللعبة والمحاولة مرة أخرى."; // 有其他游戏正在进行提示阿语
    private static final String GAME_TIMEOUT_NOT_START = "%s game timeout does not start, it has ended automatically."; // 游戏超时未开始
    private static final String GAME_TIMEOUT_NOT_START_AR = "لا تبدأ مهلة لعبة %s وتنتهي تلقائيا."; // 游戏超时未开始阿语

    private static final int COIN = 1;
    private static final int DIAMOND = 2;

    private static final int LOOP_TIME = 60 * 1000; // 每分钟扫描一下是否有超时的游戏
    private static final int CHECK_TIME = 5 * 60; // 5分钟未开始游戏系统自动关闭游戏
    private static final int CHECK_OVER_TIME = 2 * 60 * 60; // 存在2个小时的游戏系统自动关闭游戏

    private static final int GAME_ROOM_CHECK_TIME = 1 * 60 * 60; // 游戏房1小时未开始游戏系统自动关闭游戏
    private static final int GAME_ROOM_CHECK_OVER_TIME = 2 * 60 * 60; // 游戏房存在2个小时的游戏系统自动关闭游戏


    private static final String JOIN_GAME_MSG = "%s has joined %s game.";
    private static final String JOIN_GAME_MSG_AR = "انضم %s إلى لعبة %s.";

    private static final int PLATFORM_WARNING_LINE = 10000; // 单款游戏平台亏损1万钻以上时触发告警

    private static final int UMO_CREATE_FEE = 0;

    private static final int LUDO_CREATE_FEE = 10;

    private final List<Integer> GAME_MATCHING_LIST = Arrays.asList(SudGameConstant.GAME_MATCHING, SudGameConstant.GAME_MATCHING_PAUSE);

    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private SudGameConfig sudGameConfig;
    @Resource
    private SudGameDao sudGameDao;
    @Resource
    private SudGameRedis sudGameRedis;
    @Resource
    private SudMGPAuth sudMGPAuth;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private RoomMicInfoMapper roomMicInfoMapper;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private SudGameApi sudGameApi;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private RoomConfigDao roomConfigDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private TurntableService turntableService;
    @Resource
    private GameRoomRedis gameRoomRedis;
    @Resource
    protected BaseInitData baseInitData;
    @Resource
    protected SudService sudService;

    @PostConstruct
    public void postInit() {

    }

    public CreateSudGameV2VO info(SudGameDTO reqDTO) {
        if (null == reqDTO.getRoomId()) {
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        String uid = reqDTO.getUid();
        String roomId = reqDTO.getRoomId();
        String gameId = reqDTO.getGameId();

        MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
        if (null == roomData) {
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        SudGameData data = sudService.checkGame(roomId);
        CreateSudGameV2VO createSudGameVO = new CreateSudGameV2VO();
        if (data == null || data.getStatus() == SudGameConstant.GAME_CLOSED
                || data.getStatus() == SudGameConstant.GAME_FINISH) {
            logger.info("check game, can not find sud game data, roomId ={}", roomId);
            SudGameConfigInfo sudGameConfigInfo = sudGameConfig.getSudGameInfoMap().get(reqDTO.getGameType());
            data = new SudGameData();
            data.setRoomId(reqDTO.getRoomId());
            data.setCurrency(sudGameConfigInfo.getGameRoomCoinFeeList().get(0));
            data.setCurrencyType(1);
            data.setPlayerList(Collections.emptyList());
            data.setGameType(reqDTO.getGameType());
        }
        BeanUtils.copyProperties(data, createSudGameVO);
        createSudGameVO.setGameId(null == data.getGameId() ? null : data.getGameId().toString());
        sudService.setGameConfigInfo(data.getGameType(), createSudGameVO);
        return createSudGameVO;
    }


    /**
     * 游戏暂停匹配状态
     */
    public CreateSudGameVO matchPause(SudGameDTO reqDTO) {
        String uid = reqDTO.getUid();
        String roomId = reqDTO.getRoomId();
        Integer pauseType = reqDTO.getPauseType();
        SudGameData data = sudGameDao.findData(reqDTO.getGameId());
        if (data == null || data.getStatus() == SudGameConstant.GAME_CLOSED || data.getStatus() == SudGameConstant.GAME_FINISH) {
            throw new GameException(GameHttpCode.GAME_CLOSED);
        }
        if (data.getStatus() == SudGameConstant.GAME_PROCESSING) {
            logger.error("matchPause: game running gameId:{}  pauseType:{}", data.getGameId().toString(), pauseType);
            throw new GameException(GameHttpCode.GAME_RUNNING);
        }
        int state = 0;
        if (pauseType == 1 && data.getStatus() == SudGameConstant.GAME_MATCHING) {
            state = SudGameConstant.GAME_MATCHING_PAUSE;
        }
        if (pauseType == 2 && data.getStatus() == SudGameConstant.GAME_MATCHING_PAUSE) {
            state = SudGameConstant.GAME_MATCHING;
        }
        if (state != 0) {
            data.setStatus(state);
            // 更新游戏信息
            sudGameDao.save(data);
            // 更新redis里的游戏相关信息
            SudGameInfo gameInfo = new SudGameInfo();
            BeanUtils.copyProperties(data, gameInfo);
            gameInfo.setGameId(data.getGameId().toString());
            sudGameRedis.updateSudGameInfo(gameInfo);
            SudService.gameMap.put(data.getGameId().toString(), gameInfo);
            logger.info("pause sud game. uid={} gameId={} gameType={}", uid, gameInfo.getGameId(), gameInfo.getGameType());
            return sudService.gameChange(data);
        } else {
            return sudService.gameDataTOVO(data);
        }
    }

    /**
     * 游戏暂停匹配状态
     */
    public void remind(SudGameDTO reqDTO) {
        String uid = reqDTO.getUid();
        String roomId = reqDTO.getRoomId();
        RoomActorDetailData actorData = roomActorCache.getData(roomId, uid, false);
        if (actorData == null) {
            logger.error("can not find actor data, uid ={}", uid);
            throw new GameException(HttpCode.PARAM_ERROR);
        }
        String gameId = sudGameRedis.getGameIdByRoomId(roomId);
        if (StringUtils.isEmpty(gameId)) {
            logger.error("can not find game data, gameId ={}", gameId);
            throw new GameException(GameHttpCode.GAME_CLOSED);
        } else {
            SudGameInfo gameInfo = SudService.gameMap.get(gameId);
            if (gameInfo == null) {
                logger.error("can not find game data, gameId ={}", gameId);
                throw new GameException(GameHttpCode.GAME_CLOSED);
            }
            sudService.sendRoomSudGameOperateMsg(actorData, gameInfo, SudGameConstant.GAME_REMIND_MSG);
        }
    }

    public MatchingVO gameRoomMatching(SudGameDTO req) {
        SudGameConfigInfo sudGameConfigInfo = sudGameConfig.getSudGameInfoMap().get(req.getGameType());
        ActorData actorData = actorDao.getActorData(req.getUid());
        if (sudGameConfigInfo == null || actorData == null) {
            logger.error("not find sudGameConfigInfo or actorData req:{}", JSONObject.toJSONString(req));
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }

        int currency = sudGameConfigInfo.getGameRoomCoinFeeList().get(0);
        int gold = actorData.getHeartGot();
        if (gold < currency) {
            throw new GameException(GameHttpCode.NOT_ENOUGH_COIN);
        }

        // 校验玩家是否有未退出的游戏
        String inGameId = sudGameRedis.getPlayerData(req.getUid());
        if (null != inGameId) {
            SudGameInfo sudGameInfo = SudService.gameMap.get(inGameId);
            if (null == sudGameInfo) {
                // 游戏不存在，是脏数据，需要删除
                sudGameRedis.removePlayerData(req.getUid());
                logger.error("clear dirty data uid={} inGameId={}", req.getUid(), inGameId);
                // && ObjectUtils.isEmpty(roomPlayerRedis.getActorRoomStatus(req.getUid()))
            } else if (GAME_MATCHING_LIST.contains(sudGameInfo.getStatus())) {
                String nowRoomId = roomPlayerRedis.getActorRoomStatus(req.getUid());
                logger.info("prepare leave old room game uid={} inGameId={} oldGameRoomId={} nowRoomId={}",
                        req.getUid(), inGameId, sudGameInfo.getRoomId(), nowRoomId);
                sudService.leaveRoomQuiteGame(req.getUid(), inGameId);
            } else {
                logger.info("player has in game uid={}", req.getUid());
                String gameName = sudGameConfig.getGameNameByGameType(sudGameInfo.getGameType(), req.getSlang());
                throw new GameException(GameHttpCode.PLAYER_IN_GAME_J, new Object[]{gameName});
            }
        }

        MatchingVO vo = new MatchingVO();
        // 正式逻辑
        List<SudGameData> matchingByGameType = sudGameDao.findMatchingByGameType(req.getGameType(), SudGameConstant.GAME_ROOM);
        Collections.shuffle(matchingByGameType);
        for (SudGameData sudGameData : matchingByGameType) {
            MongoRoomData roomData = mongoRoomDao.findData(sudGameData.getRoomId());
            if (sudGameData.getCurrency() <= gold && null != roomData && StringUtils.isEmpty(roomData.getPwd())
                    && !roomBlacklistDao.isBlock(sudGameData.getRoomId(), req.getUid())
                    && !roomKickRedis.isKick(sudGameData.getRoomId(), req.getUid())) {
                vo.setMatchedPlayers(sudGameData.getPlayerList().stream().map(SudGamePlayerData::getHead).collect(Collectors.toSet()));
                vo.setRoomId(sudGameData.getRoomId());
                vo.setGameId(sudGameData.getGameId().toString());
                break;
            }
        }

        if (StringUtils.hasLength(vo.getRoomId())) {
            Set<String> resultSet = new HashSet<>();
            for (int i = 0; i < 5; i++) {
                resultSet.add(baseInitData.generateRandomHead(2));
            }
            // 游戏房没有这个动画
//            sudGameDao.findRecentlyPlayer(req.getUid(), req.getGameType());
            vo.setRecentlyPlayers(resultSet);
        } else {
            logger.info("uid:{} not find match game please create", req.getUid());
            throw new GameException(GameHttpCode.GAME_CREATE_GAME, new Object[]{SLangType.ENGLISH == req.getSlang() ?
                    sudGameConfigInfo.getName()
                    : sudGameConfigInfo.getNameAr()});
//            List<SudGameData> processingByGameType = sudGameDao.findProcessingByGameType(req.getGameType(), SudGameConstant.GAME_ROOM);
//            Collections.shuffle(processingByGameType);
//            for (SudGameData sudGameData : processingByGameType) {
//                MongoRoomData roomData = mongoRoomDao.findData(sudGameData.getRoomId());
//                if (sudGameData.getCurrency() <= gold && null != roomData && StringUtils.isEmpty(roomData.getPwd())
//                        && !roomBlacklistDao.isBlock(sudGameData.getRoomId(), req.getUid())
//                        && !roomKickRedis.isKick(sudGameData.getRoomId(), req.getUid())) {
//                    vo.setMatchedPlayers(sudGameData.getPlayerList().stream().map(SudGamePlayerData::getHead).collect(Collectors.toSet()));
//                    vo.setRoomId(sudGameData.getRoomId());
//                    vo.setGameId(sudGameData.getGameId().toString());
//                    break;
//                }
//            }
//            if (StringUtils.hasLength(vo.getRoomId())) {
//                vo.setRecentlyPlayers(sudGameDao.findRecentlyPlayer(req.getUid(), req.getGameType()));
//            }
        }


        // 测试逻辑
//        Map<String, Integer> roomMapType = gameRoomRedis.getAllGameRoomType();
//        List<String> roomList = new ArrayList<>();
//        for (Map.Entry<String, Integer> entry : roomMapType.entrySet()) {
//            if (req.getGameType() == entry.getValue()) {
//                roomList.add(entry.getKey());
//            }
//        }
//        if (CollectionUtils.isEmpty(roomList)) {
//            logger.info("uid:{} not find match game ", req.getUid());
//            throw new GameException(new HttpCode(1, "not find match game"));
//        }
//        Collections.shuffle(roomList);
//        Set<String> resultSet = new HashSet<>();
//        for (int i = 0; i < 5; i++) {
//            resultSet.add(baseInitData.generateRandomHead(2));
//        }
//
//        for (String roomId : roomList) {
//            MongoRoomData roomData = mongoRoomDao.findData(roomId);
//            if (null != roomData && StringUtils.isEmpty(roomData.getPwd())
//                    && !roomBlacklistDao.isBlock(roomId, req.getUid())
//                    && !roomKickRedis.isKick(roomId, req.getUid())) {
//                vo.setMatchedPlayers(resultSet);
//                vo.setRoomId(roomId);
//                vo.setGameId("test_gameId");
//                break;
//            }
//        }
//        vo.setRecentlyPlayers(resultSet);

//        if (StringUtils.isEmpty(vo.getRoomId())) {
//            logger.info("uid:{} not find match game please create", req.getUid());
//            throw new GameException(GameHttpCode.GAME_CREATE_GAME, new Object[]{SLangType.ENGLISH == req.getSlang() ?
//                    sudGameConfigInfo.getName()
//                    : sudGameConfigInfo.getNameAr()});
//        }
        return vo;
    }

    public void autoCreateOrJoinNext(SudGameData oldGameData) {
        List<SudGameData.GameResult> gameResultList = oldGameData.getGameResultList();
        List<String> preJoinList = gameResultList.stream().map(SudGameData.GameResult::getUid).collect(Collectors.toList());
        List<SudGameData.GameResult> sortList = new ArrayList<>();
        Set<String> actorSet = roomPlayerRedis.getRoomActors(oldGameData.getRoomId());
        preJoinList.forEach(item -> {
            SudGameData.GameResult itemSort = new SudGameData.GameResult();
            ActorData actorData = actorDao.getActorDataFromCache(item);
            if (actorData.getRobot() != 1 && actorSet.contains(item) && actorData.getHeartGot() >= oldGameData.getCurrency()) {
                // 不是机器人，并且在房间
                itemSort.setUid(item);
                itemSort.setJoinRank(1);
                if (oldGameData.getLeaderUid().equals(item)) {
                    itemSort.setJoinRank(100);
                }
                if (RoomUtils.getRoomHostId(oldGameData.getRoomId()).equals(item)) {
                    itemSort.setJoinRank(1000);
                }
                sortList.add(itemSort);
            } else {
                logger.info("uid:{} is robot:{} or not enough gold:{} or not in room", item, actorData.getRobot(), actorData.getHeartGot());
            }
        });

        if (CollectionUtils.isEmpty(sortList)) {
            logger.info("autoCreateOrJoinNext fail sortList is empty");
            return;
        }
        sortList.sort(Comparator.comparing(SudGameData.GameResult::getJoinRank).reversed());

        try (DistributeLock lock = new DistributeLock(SudController.SUD_LOCK_KEY + oldGameData.getRoomId(), 30)) {
            lock.lock();
            sortList.forEach(item -> {
                CreateSudGameDTO reqDTO = buildCreateSudGameDTO(oldGameData);
                reqDTO.setUid(item.getUid());
                sudService.createOrJoin(reqDTO);
            });
            logger.info("autoCreateOrJoinNext success sortList:{}", JSONObject.toJSONString(sortList));
        } catch (GameException e) {
            logger.error("autoCreateOrJoinNext e:{}", e.getMessage(), e);
        } catch (Exception e) {
            logger.error(" createOrJoin. reqDTO={}", JSONObject.toJSONString(buildCreateSudGameDTO(oldGameData)), e);
        }
    }

    private CreateSudGameDTO buildCreateSudGameDTO(SudGameData gameData) {
        CreateSudGameDTO reqDTO = new CreateSudGameDTO();
        reqDTO.setRoomId(gameData.getRoomId());
        reqDTO.setGameType(gameData.getGameType());
        reqDTO.setCurrencyType(gameData.getCurrencyType());
        reqDTO.setCurrency(gameData.getCurrency());
        reqDTO.setEnterRoomType(4);
        reqDTO.setPlayerNumber(gameData.getPlayerNumber());
        reqDTO.setRule(gameData.getRule());
        return reqDTO;
    }
}
