package com.quhong.mysql.dao;

import com.quhong.cache.CacheMap;
import com.quhong.mysql.mapper.ustar.RoomMicInfoMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import com.quhong.mysql.data.RoomMicData;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Component
@Lazy
public class RoomMicUserDao {

    private static final Logger logger = LoggerFactory.getLogger(RoomMicUserDao.class);

    private static final long CACHE_TIME_MILLIS = 30 * 1000L; // 30秒
    private final CacheMap<String, List<RoomMicData>> cacheMap;

    @Autowired
    private RoomMicInfoMapper mapper;

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public RoomMicUserDao() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    public List<RoomMicData> getRecommendUserDataList(int gender) {
        List<RoomMicData> dataList = new ArrayList<>();
        try {
            dataList = cacheMap.getData(getCaCheKey(gender));
            if (!CollectionUtils.isEmpty(dataList)) {
                return dataList;
            }
            dataList = getDataList(gender);
            if (!CollectionUtils.isEmpty(dataList)) {
                cacheMap.cacheData(getCaCheKey(gender), dataList);
            }
        } catch (Exception e) {
            logger.error("get recommend user error. {}" , e.getMessage(), e);
        }
        return dataList;
    }

    public List<RoomMicData> getDataList(int gender) {
        return mapper.getRecommendUserDataList(gender);
    }

    private String getCaCheKey(int gender) {
        return "recommendUser:" + gender;
    }
}
