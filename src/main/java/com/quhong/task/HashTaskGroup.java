package com.quhong.task;

import com.quhong.core.concurrency.queues.ITaskQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

/**
 * 任务组
 */
public class HashTaskGroup {
    private static final Logger logger = LoggerFactory.getLogger(HashTaskGroup.class);

    protected ITaskQueue[] queues;

    public HashTaskGroup(int size, Class<? extends ITaskQueue> clazz){
        init(size, clazz);
    }

    protected void init(int size, Class<? extends ITaskQueue> clazz){
        if(size < 1){
            size = 1;
        }
        queues = new ITaskQueue[size];
        for(int i = 0; i < queues.length; i ++){
            try {
                ITaskQueue taskQueue = clazz.newInstance();
                queues[i] = taskQueue;
            }catch (Exception e){
                logger.error("init task queue error. {}", e.getMessage(), e);
            }
        }
    }

    public ITaskQueue getQueue(){
        return queues[0];
    }

    public ITaskQueue getQueue(String id){
        int index;
        if(StringUtils.isEmpty(id)){
            index = 0;
        }else{
            index = id.charAt(id.length() - 1);
            if(index < 0){
                index = - index;
            }
        }
        int hash = index % queues.length;
        return queues[hash];
    }
}
