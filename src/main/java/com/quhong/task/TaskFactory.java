package com.quhong.task;

import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.pools.TaskPool;
import com.quhong.core.concurrency.tasks.ITask;
import org.springframework.stereotype.Component;

@Component
public class TaskFactory extends BaseTaskFactory {
    public static TaskFactory getFactory() {
        return (TaskFactory) factory;
    }

    public TaskFactory() {
        super(16, 32);
    }

    /**
     * 慢业务任务池
     */
    private TaskPool msgPool;

    @Override
    public void init() {
        super.init();
        msgPool = new TaskPool().init(16,"msg");
    }

    public void addPush(ITask task){
        msgPool.add(task);
    }
}
