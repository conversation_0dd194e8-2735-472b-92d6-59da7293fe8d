package com.quhong.task;

import com.quhong.constant.ActiveBadgeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.redis.ActiveBadgeRedis;
import com.quhong.service.ActiveBadgeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.time.ZonedDateTime;

/**
 * 活跃勋章定时任务
 */
@Component
public class ActiveBadgeScheduleTask {

    private static final Logger logger = LoggerFactory.getLogger(ActiveBadgeScheduleTask.class);

    @Resource
    private ActiveBadgeService activeBadgeService;

    @Resource
    private ActiveBadgeRedis activeBadgeRedis;

    /**
     * 每天沙特时间20:00推送活跃提醒
     * 使用cron表达式：0 0 20 * * ? 表示每天20:00触发
     * 注意：这里需要根据服务器时区调整，如果服务器是UTC时区，需要调整为对应的UTC时间
     */
    @Scheduled(cron = "0 0 20 * * ?", zone = "Asia/Riyadh")
    public void sendDailyActiveReminder() {
        logger.info("开始执行每日活跃提醒推送任务");
        
        try {
            // 检查当前是否为沙特时间20:00
            ZonedDateTime saudiTime = ZonedDateTime.now(ZoneId.of("Asia/Riyadh"));
            int currentHour = saudiTime.getHour();
            
            if (currentHour != ActiveBadgeConstant.PUSH_HOUR) {
                logger.warn("当前时间不是推送时间，跳过执行。当前沙特时间: {}", saudiTime);
                return;
            }
            
            // 获取今日未有麦位互动的用户列表
            // 这里需要根据具体的用户系统来实现获取所有用户的逻辑
            // 暂时记录日志，实际实现需要配合用户服务
            logger.info("推送时间到达，开始查找今日未活跃用户");
            
            // TODO: 实现获取今日未活跃用户列表的逻辑
            // 可能的实现方式：
            // 1. 从用户服务获取所有活跃用户列表
            // 2. 过滤出今日未有麦位互动的用户
            // 3. 批量发送推送消息
            
            // 示例代码结构：
            // List<String> allActiveUserIds = userService.getAllActiveUserIds();
            // for (String uid : allActiveUserIds) {
            //     if (!activeBadgeRedis.hasUserMicInteractionToday(uid)) {
            //         activeBadgeService.pushActiveReminder(uid);
            //     }
            // }
            
            logger.info("每日活跃提醒推送任务执行完成");
            
        } catch (Exception e) {
            logger.error("执行每日活跃提醒推送任务失败", e);
        }
    }

    /**
     * 每天凌晨清理过期的Redis数据（可选）
     * 0 0 1 * * ? 表示每天凌晨1:00执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void cleanupExpiredData() {
        logger.info("开始执行Redis数据清理任务");
        
        try {
            // 可以在这里添加清理过期数据的逻辑
            // 例如清理超过7天的麦上时长记录等
            
            logger.info("Redis数据清理任务执行完成");
            
        } catch (Exception e) {
            logger.error("执行Redis数据清理任务失败", e);
        }
    }
} 