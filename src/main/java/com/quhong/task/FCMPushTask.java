package com.quhong.task;

import com.quhong.core.config.ServerConfig;
import com.quhong.enums.SLangType;
import com.quhong.fcm.FCMPusher;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.SignTableDao;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mongo.data.SignTableData;
import com.quhong.redis.PlayerStatusRedis;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.K8sUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


@Component
public class FCMPushTask {
    private static final Logger logger = LoggerFactory.getLogger(FCMPushTask.class);

    public static final String SIGN_EN_TITLE = "Claim your Daily Bonus";
    public static final String SIGN_AR_TITLE = "أحصل على مكافأتك اليومية";
    public static final String SIGN_EN_BODY = "You don’t want it to expire!";
    public static final String SIGN_AR_BODY = "أحصل عليها قبل أن ينتهي وقتها المحدد !";


    @Resource
    private SignTableDao signTableDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private PlayerStatusRedis playerStatusRedis;
    @Resource
    private FCMPusher fcmPusher;
    @Resource
    private K8sUtils k8sUtils;

    /**
     * FCM每日签到消息通知，UTC+3 20:00
     */
    @Scheduled(cron = "0 0 17 * * ?")
    public void dailySignNotice() {
        if (ServerConfig.isProduct()) {
            return;
        }
        if (!k8sUtils.isMaster()) {
            return;
        }
        long startTimeMillis = System.currentTimeMillis();
        logger.info("start dailySignNotice.");
        List<SignTableData> yesterdaySign = signTableDao.getYesterdaySign();
        Set<String> uidSet = CollectionUtil.listToPropertySet(yesterdaySign, SignTableData::get_id);
        Set<String> enBatch = new HashSet<>();
        Set<String> arBatch = new HashSet<>();
        List<MongoActorData> actors = actorDao.getActors(uidSet);
        for (MongoActorData actor : actors) {
            if (null != actor.getPush()
                    && actor.getPush().containsKey("token")
                    && (int) actor.getPush().getOrDefault("private_message", 1) == 1) {
                if (playerStatusRedis.getPlayerStatus(actor.get_id().toString()) > 0) {
                    continue;
                }
                if (SLangType.ENGLISH == actor.getSlang()) {
                    enBatch.add((String) actor.getPush().get("token"));
                } else {
                    arBatch.add((String) actor.getPush().get("token"));
                }
                if (enBatch.size() == FCMPusher.FCM_BATCH_SEND_MAX) {
                    fcmPusher.pushBatch(enBatch, Collections.emptyMap(), SIGN_EN_TITLE, SIGN_EN_BODY);
                    enBatch.clear();
                }
                if (arBatch.size() == FCMPusher.FCM_BATCH_SEND_MAX) {
                    fcmPusher.pushBatch(enBatch, Collections.emptyMap(), SIGN_AR_TITLE, SIGN_AR_BODY);
                    arBatch.clear();
                }
            }
        }
        // 处理最后一批剩余的元素
        if (!enBatch.isEmpty()) {
            fcmPusher.pushBatch(enBatch, Collections.emptyMap(), SIGN_EN_TITLE, SIGN_EN_BODY);
        }
        if (!arBatch.isEmpty()) {
            fcmPusher.pushBatch(enBatch, Collections.emptyMap(), SIGN_AR_TITLE, SIGN_AR_BODY);
        }
        logger.info("finish dailySignNotice. total={} cost={}", actors.size(), System.currentTimeMillis() - startTimeMillis);
    }
}
