package com.quhong.data;

import com.quhong.data.vo.GameListVO;

import java.util.ArrayList;
import java.util.List;

public class PartyData {
    List<PartyListData> allList = new ArrayList<>();
    List<GameListVO> gameList = new ArrayList<>();
    boolean isRefreshSaWeight;

    public List<PartyListData> getAllList() {
        return allList;
    }

    public void setAllList(List<PartyListData> allList) {
        this.allList = allList;
    }

    public List<GameListVO> getGameList() {
        return gameList;
    }

    public void setGameList(List<GameListVO> gameList) {
        this.gameList = gameList;
    }

    public boolean isRefreshSaWeight() {
        return isRefreshSaWeight;
    }

    public void setRefreshSaWeight(boolean refreshSaWeight) {
        isRefreshSaWeight = refreshSaWeight;
    }
}
