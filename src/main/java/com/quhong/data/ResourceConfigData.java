package com.quhong.data;


import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class ResourceConfigData {
    private String title; // 资源名称
    private String titleAr; // 阿语资源名称
    private String exp; // 经验描述
    private String levelIcon; // 等级勋章
    @JSONField(serialize = false)
    private Integer stepLevel; // 对应 1-8
    private List<ResourceConfigItemData> rewardList ;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleAr() {
        return titleAr;
    }

    public void setTitleAr(String titleAr) {
        this.titleAr = titleAr;
    }

    public String getExp() {
        return exp;
    }

    public void setExp(String exp) {
        this.exp = exp;
    }

    public List<ResourceConfigItemData> getRewardList() {
        return rewardList;
    }

    public void setRewardList(List<ResourceConfigItemData> rewardList) {
        this.rewardList = rewardList;
    }

    public String getLevelIcon() {
        return levelIcon;
    }

    public void setLevelIcon(String levelIcon) {
        this.levelIcon = levelIcon;
    }

    public Integer getStepLevel() {
        return stepLevel;
    }

    public void setStepLevel(Integer stepLevel) {
        this.stepLevel = stepLevel;
    }
}
