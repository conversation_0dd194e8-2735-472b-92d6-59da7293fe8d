package com.quhong.data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/23
 */
public class TuringShieldResponse {

    /**
     * {
     *     "Response":{
     *         "Data":{
     *             "UUid":"b7e2cc24-c387-48aa-b82c-0efb05cbe162",
     *             "Code":0,
     *             "Message":"OK",
     *             "Value":{
     *                 "RiskLevel":"review",
     *                 "RiskType":[
     *                     211,
     *                     201
     *                 ],
     *                 "DeviceId":"60B6C864119E28024FC03426",
     *                 "TokenTime":"1684842112022",
     *                 "ExtraInfo":[
     *                     {
     *                         "Key":"brand",
     *                         "Value":"Redmi"
     *                     },
     *                     {
     *                         "Key":"system_version",
     *                         "Value":"11"
     *                     },
     *                     {
     *                         "Key":"model",
     *                         "Value":"M2012K11AC"
     *                     },
     *                     {
     *                         "Key":"sdk_buildno",
     *                         "Value":"77"
     *                     },
     *                     {
     *                         "Key":"package_name",
     *                         "Value":"com.waho.live"
     *                     },
     *                     {
     *                         "Key":"platform",
     *                         "Value":"2"
     *                     },
     *                     {
     *                         "Key":"app_version",
     *                         "Value":"1.0.1"
     *                     },
     *                     {
     *                         "Key":"client_ip",
     *                         "Value":"*************"
     *                     },
     *                     {
     *                         "Key":"network_type",
     *                         "Value":"0"
     *                     }
     *                 ]
     *             }
     *         },
     *         "RequestId":"21a0f02c-ef66-49cc-9254-0b2a846d1dd1"
     *     }
     * }
     */

    private ResponseData Data;

    private ResponseData Error;

    private String RequestId;

    public static class ResponseData {
        private String UUid;
        private Integer Code;
        private String Message;
        private ValueData Value;

        public class ValueData {

            /**
             * 风险等级（pass/review/reject）
             */
            private String RiskLevel;

            /**
             * 风险标签值
             */
            private List<Integer> RiskType;

            /**
             * 设备OpenId，设备指纹唯一ID，设备ID
             */
            private String DeviceId;

            /**
             * 时间戳（毫秒）
             */
            private Long TokenTime;

            /**
             * 图灵顿设备额外参数
             */
            private List<Map<String, Object>> ExtraInfo;

            public String getRiskLevel() {
                return RiskLevel;
            }

            public void setRiskLevel(String riskLevel) {
                RiskLevel = riskLevel;
            }

            public List<Integer> getRiskType() {
                return RiskType;
            }

            public void setRiskType(List<Integer> riskType) {
                RiskType = riskType;
            }

            public String getDeviceId() {
                return DeviceId;
            }

            public void setDeviceId(String deviceId) {
                DeviceId = deviceId;
            }

            public Long getTokenTime() {
                return TokenTime;
            }

            public void setTokenTime(Long tokenTime) {
                TokenTime = tokenTime;
            }

            public List<Map<String, Object>> getExtraInfo() {
                return ExtraInfo;
            }

            public void setExtraInfo(List<Map<String, Object>> extraInfo) {
                ExtraInfo = extraInfo;
            }
        }

        public String getUUid() {
            return UUid;
        }

        public void setUUid(String UUid) {
            this.UUid = UUid;
        }

        public Integer getCode() {
            return Code;
        }

        public void setCode(Integer code) {
            Code = code;
        }

        public String getMessage() {
            return Message;
        }

        public void setMessage(String message) {
            Message = message;
        }

        public ValueData getValue() {
            return Value;
        }

        public void setValue(ValueData value) {
            Value = value;
        }
    }

    public ResponseData getData() {
        return Data;
    }

    public void setData(ResponseData data) {
        Data = data;
    }

    public ResponseData getError() {
        return Error;
    }

    public void setError(ResponseData error) {
        Error = error;
    }

    public String getRequestId() {
        return RequestId;
    }

    public void setRequestId(String requestId) {
        RequestId = requestId;
    }
}
