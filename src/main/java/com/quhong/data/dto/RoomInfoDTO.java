package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

/**
 * <AUTHOR>
 * @date 2022/7/12
 */
public class RoomInfoDTO extends HttpEnvData {

    private String topic; //房间主题
    private String announce; // 房间公告
    private Integer privi; // 1所有人都可以上麦 2要花钱才能上 3需要申请
    private Integer fee; // 房间会员费用
    private Integer feetype;  //  房间会员费用类型，金币或钻石
    private Integer theme; // 房间主题
    private String name; // 房间名
    private String head; // 房间头像
    private Integer text_limit; // 房间发消息，限制用户等级

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getAnnounce() {
        return announce;
    }

    public void setAnnounce(String announce) {
        this.announce = announce;
    }

    public Integer getPrivi() {
        return privi;
    }

    public void setPrivi(Integer privi) {
        this.privi = privi;
    }

    public Integer getFee() {
        return fee;
    }

    public void setFee(Integer fee) {
        this.fee = fee;
    }

    public Integer getFeetype() {
        return feetype;
    }

    public void setFeetype(Integer feetype) {
        this.feetype = feetype;
    }

    public Integer getTheme() {
        return theme;
    }

    public void setTheme(Integer theme) {
        this.theme = theme;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public Integer getText_limit() {
        return text_limit;
    }

    public void setText_limit(Integer text_limit) {
        this.text_limit = text_limit;
    }

}
