package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
public class SetResourcesDTO extends HttpEnvData {

    /**
     * 声波资源id
     */
    private Integer ripple_id;

    /**
     * 麦位资源id
     */
    private Integer mic_frame_id;

    /**
     * 气泡资源id
     */
    private Integer buddle_id;

    /**
     * 浮萍资源id
     */
    private Integer screen_id;

    /**
     * 坐骑资源id
     */
    private Integer join_id;

    /**
     * 进入特效资源id
     */
    private Integer entry_effect_id;

    /**
     * 新版资源类型
     */
    private Integer res_type;

    /**
     * 新版资源id
     */
    private Integer res_id;

    /**
     * 新版资源动作 1佩戴 2卸下
     */
    private Integer action;

    public Integer getRipple_id() {
        return ripple_id;
    }

    public void setRipple_id(Integer ripple_id) {
        this.ripple_id = ripple_id;
    }

    public Integer getMic_frame_id() {
        return mic_frame_id;
    }

    public void setMic_frame_id(Integer mic_frame_id) {
        this.mic_frame_id = mic_frame_id;
    }

    public Integer getBuddle_id() {
        return buddle_id;
    }

    public void setBuddle_id(Integer buddle_id) {
        this.buddle_id = buddle_id;
    }

    public Integer getScreen_id() {
        return screen_id;
    }

    public void setScreen_id(Integer screen_id) {
        this.screen_id = screen_id;
    }

    public Integer getJoin_id() {
        return join_id;
    }

    public void setJoin_id(Integer join_id) {
        this.join_id = join_id;
    }

    public Integer getRes_type() {
        return res_type;
    }

    public void setRes_type(Integer res_type) {
        this.res_type = res_type;
    }

    public Integer getRes_id() {
        return res_id;
    }

    public void setRes_id(Integer res_id) {
        this.res_id = res_id;
    }

    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

    public Integer getEntry_effect_id() {
        return entry_effect_id;
    }

    public void setEntry_effect_id(Integer entry_effect_id) {
        this.entry_effect_id = entry_effect_id;
    }
}
