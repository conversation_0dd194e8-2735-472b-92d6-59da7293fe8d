package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

/**
 * <AUTHOR>
 * @date 2023/2/10
 */
public class RoomGatheringDTO extends HttpEnvData {

    /**
     * 召集类型：0召集全球用户 1召集房间会员 2召集房间粉丝 3召集我的粉丝
     */
    private Integer type;

    /**
     * 消息内容
     */
    private String msg;

    /**
     * 页数
     */
    private Integer page;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }
}
