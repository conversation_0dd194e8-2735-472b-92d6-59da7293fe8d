package com.quhong.data.vo;

import com.quhong.vo.RoomMicListVo;

public class PwdCheckVo {
    private String streamId; // 流id
    private String zegoToken; // 即构token
    private String agoraToken; // 声网RTC授权
    private String agoraRtmToken; // 声网RTM授权
    private RoomActorVO roomActor; // 房间用户信息
    private RoomMicListVo roomMic = new RoomMicListVo(); // 房间麦位信息
    private int roomMode;

    public String getStreamId() {
        return streamId;
    }

    public void setStreamId(String streamId) {
        this.streamId = streamId;
    }

    public String getZegoToken() {
        return zegoToken;
    }

    public void setZegoToken(String zegoToken) {
        this.zegoToken = zegoToken;
    }

    public String getAgoraToken() {
        return agoraToken;
    }

    public void setAgoraToken(String agoraToken) {
        this.agoraToken = agoraToken;
    }

    public String getAgoraRtmToken() {
        return agoraRtmToken;
    }

    public void setAgoraRtmToken(String agoraRtmToken) {
        this.agoraRtmToken = agoraRtmToken;
    }

    public RoomActorVO getRoomActor() {
        return roomActor;
    }

    public void setRoomActor(RoomActorVO roomActor) {
        this.roomActor = roomActor;
    }

    public RoomMicListVo getRoomMic() {
        return roomMic;
    }

    public void setRoomMic(RoomMicListVo roomMic) {
        this.roomMic = roomMic;
    }

    public int getRoomMode() {
        return roomMode;
    }

    public void setRoomMode(int roomMode) {
        this.roomMode = roomMode;
    }
}
