package com.quhong.data.vo;

import com.quhong.mongo.data.ResourceKeyConfigData;

import java.util.List;

public class DragonTrainVO {

    private Integer stoneNum;            // 我的石头数量
    private Integer dragonLevel;         // 龙的等级
    private Integer trainNum;            // 当前驯服次数
    private Integer trainTotalNum;       // 总驯服次数
    private List<RollRecordData> rollRecordList;   // 滚屏列表
    private DragonTeamConfig teamInfo;   // 团队信息
    private List<DragonTeamConfig> teamRankList;   // 团队榜单

    private List<RollRecordData> recordList;   // 抽奖记录列表
    private Integer nextUrl;


    public static class DragonTeamConfig{
        private String teamId;            // 团队id
        private String teamLeadUid;       // 团队队长id
        private String teamName;          // 团队名称
        private Integer teamScore;        // 团队总分数
        private Integer rank;             // 团队排名
        private Integer muchScore;        // 分数差
        private List<TeamInfo> teamMemberList;  // 团队成员信息

        public String getTeamId() {
            return teamId;
        }

        public void setTeamId(String teamId) {
            this.teamId = teamId;
        }

        public String getTeamLeadUid() {
            return teamLeadUid;
        }

        public void setTeamLeadUid(String teamLeadUid) {
            this.teamLeadUid = teamLeadUid;
        }

        public String getTeamName() {
            return teamName;
        }

        public void setTeamName(String teamName) {
            this.teamName = teamName;
        }

        public Integer getTeamScore() {
            return teamScore;
        }

        public void setTeamScore(Integer teamScore) {
            this.teamScore = teamScore;
        }

        public Integer getRank() {
            return rank;
        }

        public void setRank(Integer rank) {
            this.rank = rank;
        }

        public Integer getMuchScore() {
            return muchScore;
        }

        public void setMuchScore(Integer muchScore) {
            this.muchScore = muchScore;
        }

        public List<TeamInfo> getTeamMemberList() {
            return teamMemberList;
        }

        public void setTeamMemberList(List<TeamInfo> teamMemberList) {
            this.teamMemberList = teamMemberList;
        }
    }

    public static class TeamInfo{
        private String uid;              // 用户uid
        private String name;             // 用户名
        private String head;             // 用户头像
        private Integer score;           // 驯龙次数

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public Integer getScore() {
            return score;
        }

        public void setScore(Integer score) {
            this.score = score;
        }
    }

    public static class RollRecordData extends ResourceKeyConfigData.ResourceMeta{
        private String uid;                 // 用户uid
        private String name;                // 用户名
        private String head;                // 用户头像
        private Integer ctime;              // 抽奖时间

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public Integer getCtime() {
            return ctime;
        }

        public void setCtime(Integer ctime) {
            this.ctime = ctime;
        }
    }

    public Integer getStoneNum() {
        return stoneNum;
    }

    public void setStoneNum(Integer stoneNum) {
        this.stoneNum = stoneNum;
    }

    public Integer getDragonLevel() {
        return dragonLevel;
    }

    public void setDragonLevel(Integer dragonLevel) {
        this.dragonLevel = dragonLevel;
    }

    public Integer getTrainNum() {
        return trainNum;
    }

    public void setTrainNum(Integer trainNum) {
        this.trainNum = trainNum;
    }

    public Integer getTrainTotalNum() {
        return trainTotalNum;
    }

    public void setTrainTotalNum(Integer trainTotalNum) {
        this.trainTotalNum = trainTotalNum;
    }

    public DragonTeamConfig getTeamInfo() {
        return teamInfo;
    }

    public void setTeamInfo(DragonTeamConfig teamInfo) {
        this.teamInfo = teamInfo;
    }

    public List<DragonTeamConfig> getTeamRankList() {
        return teamRankList;
    }

    public void setTeamRankList(List<DragonTeamConfig> teamRankList) {
        this.teamRankList = teamRankList;
    }

    public List<RollRecordData> getRollRecordList() {
        return rollRecordList;
    }

    public void setRollRecordList(List<RollRecordData> rollRecordList) {
        this.rollRecordList = rollRecordList;
    }

    public List<RollRecordData> getRecordList() {
        return recordList;
    }

    public void setRecordList(List<RollRecordData> recordList) {
        this.recordList = recordList;
    }

    public Integer getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(Integer nextUrl) {
        this.nextUrl = nextUrl;
    }
}
