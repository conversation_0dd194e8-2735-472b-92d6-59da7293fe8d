package com.quhong.data.vo;

import java.util.List;

public class GiftWishVO {

    private int rid;
    private int finishUserNum;   // 完成人数
    private int showDailyStatus;   // 0:未完成  1:要展示  2:已展示
    private int showWeeklyStatus;  // 0:未完成  1:要展示  2:已展示

    private List<GiftConfig> giftConfigList;
    private List<RollRecord> rollRecordList;
    private List<WeeklyConfig> dailyFinishStatusList;  // 0:未完成 1:已完成

    public static class GiftConfig{
        private String giftIcon;
        private String giftName;
        private String giftEffect;
        private int giftPrice;
        private int show;

        public String getGiftIcon() {
            return giftIcon;
        }

        public void setGiftIcon(String giftIcon) {
            this.giftIcon = giftIcon;
        }

        public String getGiftName() {
            return giftName;
        }

        public void setGiftName(String giftName) {
            this.giftName = giftName;
        }

        public String getGiftEffect() {
            return giftEffect;
        }

        public void setGiftEffect(String giftEffect) {
            this.giftEffect = giftEffect;
        }

        public int getGiftPrice() {
            return giftPrice;
        }

        public void setGiftPrice(int giftPrice) {
            this.giftPrice = giftPrice;
        }

        public int getShow() {
            return show;
        }

        public void setShow(int show) {
            this.show = show;
        }
    }

    public static class RollRecord{
        private String name;
        private String giftName;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getGiftName() {
            return giftName;
        }

        public void setGiftName(String giftName) {
            this.giftName = giftName;
        }
    }


    public static class WeeklyConfig{
        private String nameEn;
        private String nameAr;
        private int status;
        private String giftIcon;



        public String getNameEn() {
            return nameEn;
        }

        public void setNameEn(String nameEn) {
            this.nameEn = nameEn;
        }

        public String getNameAr() {
            return nameAr;
        }

        public void setNameAr(String nameAr) {
            this.nameAr = nameAr;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getGiftIcon() {
            return giftIcon;
        }

        public void setGiftIcon(String giftIcon) {
            this.giftIcon = giftIcon;
        }
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getFinishUserNum() {
        return finishUserNum;
    }

    public void setFinishUserNum(int finishUserNum) {
        this.finishUserNum = finishUserNum;
    }

    public int getShowDailyStatus() {
        return showDailyStatus;
    }

    public void setShowDailyStatus(int showDailyStatus) {
        this.showDailyStatus = showDailyStatus;
    }

    public int getShowWeeklyStatus() {
        return showWeeklyStatus;
    }

    public void setShowWeeklyStatus(int showWeeklyStatus) {
        this.showWeeklyStatus = showWeeklyStatus;
    }


    public List<WeeklyConfig> getDailyFinishStatusList() {
        return dailyFinishStatusList;
    }

    public void setDailyFinishStatusList(List<WeeklyConfig> dailyFinishStatusList) {
        this.dailyFinishStatusList = dailyFinishStatusList;
    }

    public List<GiftConfig> getGiftConfigList() {
        return giftConfigList;
    }

    public void setGiftConfigList(List<GiftConfig> giftConfigList) {
        this.giftConfigList = giftConfigList;
    }

    public List<RollRecord> getRollRecordList() {
        return rollRecordList;
    }

    public void setRollRecordList(List<RollRecord> rollRecordList) {
        this.rollRecordList = rollRecordList;
    }
}
