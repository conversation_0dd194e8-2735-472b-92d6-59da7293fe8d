package com.quhong.data.vo;


import com.quhong.data.UserBasicInfo;

public class VisitorListVO {

    private int ctime;
    private String date_num;
    private int invisible;
    private int is_new;
    private int mtime;
    private long times;
    private UserBasicInfo user_info;

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public String getDate_num() {
        return date_num;
    }

    public void setDate_num(String date_num) {
        this.date_num = date_num;
    }

    public int getInvisible() {
        return invisible;
    }

    public void setInvisible(int invisible) {
        this.invisible = invisible;
    }

    public int getIs_new() {
        return is_new;
    }

    public void setIs_new(int is_new) {
        this.is_new = is_new;
    }

    public int getMtime() {
        return mtime;
    }

    public void setMtime(int mtime) {
        this.mtime = mtime;
    }

    public long getTimes() {
        return times;
    }

    public void setTimes(long times) {
        this.times = times;
    }

    public UserBasicInfo getUser_info() {
        return user_info;
    }

    public void setUser_info(UserBasicInfo user_info) {
        this.user_info = user_info;
    }
}
