package com.quhong.data.vo;

import java.util.List;

public class PainterConfigVO {
    private Integer startTime;
    private Integer endTime;
    private Integer rid;
    private String name;
    private int painter;
    private int written;
    private WrittenInfo writtenInfo;
    private List<MomentInfo> momentInfoList;
    private List<WrittenInfo> writtenInfoList;


    public static class MomentInfo{
        private String momentId;
        private String title;
        private String picture;
        private int repost;
        private int likes;
        private int likeStatus;
        private int comments;
        private int ctime;

        public String getMomentId() {
            return momentId;
        }

        public void setMomentId(String momentId) {
            this.momentId = momentId;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public int getRepost() {
            return repost;
        }

        public void setRepost(int repost) {
            this.repost = repost;
        }

        public int getLikes() {
            return likes;
        }

        public void setLikes(int likes) {
            this.likes = likes;
        }

        public int getLikeStatus() {
            return likeStatus;
        }

        public void setLikeStatus(int likeStatus) {
            this.likeStatus = likeStatus;
        }

        public int getComments() {
            return comments;
        }

        public void setComments(int comments) {
            this.comments = comments;
        }

        public String getPicture() {
            return picture;
        }

        public void setPicture(String picture) {
            this.picture = picture;
        }

        public int getCtime() {
            return ctime;
        }

        public void setCtime(int ctime) {
            this.ctime = ctime;
        }
    }

    public static class WrittenInfo{
        private String momentId;
        private String message;
        private String head;
        private int repost;
        private int likes;
        private int likeStatus;
        private int comments;
        private int ctime;

        public String getMomentId() {
            return momentId;
        }

        public void setMomentId(String momentId) {
            this.momentId = momentId;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public int getRepost() {
            return repost;
        }

        public void setRepost(int repost) {
            this.repost = repost;
        }

        public int getLikes() {
            return likes;
        }

        public void setLikes(int likes) {
            this.likes = likes;
        }

        public int getLikeStatus() {
            return likeStatus;
        }

        public void setLikeStatus(int likeStatus) {
            this.likeStatus = likeStatus;
        }

        public int getComments() {
            return comments;
        }

        public void setComments(int comments) {
            this.comments = comments;
        }

        public int getCtime() {
            return ctime;
        }

        public void setCtime(int ctime) {
            this.ctime = ctime;
        }
    }


    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getPainter() {
        return painter;
    }

    public void setPainter(int painter) {
        this.painter = painter;
    }

    public int getWritten() {
        return written;
    }

    public void setWritten(int written) {
        this.written = written;
    }

    public WrittenInfo getWrittenInfo() {
        return writtenInfo;
    }

    public void setWrittenInfo(WrittenInfo writtenInfo) {
        this.writtenInfo = writtenInfo;
    }

    public List<MomentInfo> getMomentInfoList() {
        return momentInfoList;
    }

    public void setMomentInfoList(List<MomentInfo> momentInfoList) {
        this.momentInfoList = momentInfoList;
    }

    public List<WrittenInfo> getWrittenInfoList() {
        return writtenInfoList;
    }

    public void setWrittenInfoList(List<WrittenInfo> writtenInfoList) {
        this.writtenInfoList = writtenInfoList;
    }
}
