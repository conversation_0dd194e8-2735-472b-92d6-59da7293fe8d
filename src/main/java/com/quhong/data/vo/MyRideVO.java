package com.quhong.data.vo;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/27
 */
public class MyRideVO {

    private String nextUrl;

    private List<MyRideVO.RideVO> list;

    public static class RideVO {

        /**
         * 进场动画id
         */
        @JSONField(name = "join_carton_id")
        private Integer joinCartonId;

        /**
         * 资源uri
         */
        @JSONField(name = "source_url")
        private String sourceUrl;

        /**
         * 资源md5
         */
        @JSONField(name = "source_md5")
        private String sourceMd5;

        /**
         * 资源md5
         */
        @JSONField(name = "end_days")
        private Integer endDays;

        private Integer states;

        /**
         * 资源图标
         */
        @JSONField(name = "join_icon")
        private String joinIcon;

        /**
         * 动画名称
         */
        private String name;

        /**
         * 资源类型： 1-svga
         */
        @JSONField(name = "source_type")
        private Integer sourceType;

        /**
         * 动画的播放时长
         */
        @JSONField(name = "time_long")
        private Integer timeLong;

        private int item_type ;

        public Integer getJoinCartonId() {
            return joinCartonId;
        }

        public void setJoinCartonId(Integer joinCartonId) {
            this.joinCartonId = joinCartonId;
        }

        public String getSourceUrl() {
            return sourceUrl;
        }

        public void setSourceUrl(String sourceUrl) {
            this.sourceUrl = sourceUrl;
        }

        public String getSourceMd5() {
            return sourceMd5;
        }

        public void setSourceMd5(String sourceMd5) {
            this.sourceMd5 = sourceMd5;
        }

        public String getJoinIcon() {
            return joinIcon;
        }

        public void setJoinIcon(String joinIcon) {
            this.joinIcon = joinIcon;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getSourceType() {
            return sourceType;
        }

        public void setSourceType(Integer sourceType) {
            this.sourceType = sourceType;
        }

        public Integer getTimeLong() {
            return timeLong;
        }

        public void setTimeLong(Integer timeLong) {
            this.timeLong = timeLong;
        }

        public Integer getEndDays() {
            return endDays;
        }

        public void setEndDays(Integer endDays) {
            this.endDays = endDays;
        }

        public Integer getStates() {
            return states;
        }

        public void setStates(Integer states) {
            this.states = states;
        }

        public int getItem_type() {
            return item_type;
        }

        public void setItem_type(int item_type) {
            this.item_type = item_type;
        }
    }

    public List<MyRideVO.RideVO> getList() {
        return list;
    }

    public void setList(List<MyRideVO.RideVO> list) {
        this.list = list;
    }

    public String getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(String nextUrl) {
        this.nextUrl = nextUrl;
    }
}
