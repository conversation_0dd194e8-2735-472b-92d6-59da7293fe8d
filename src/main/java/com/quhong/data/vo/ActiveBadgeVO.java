package com.quhong.data.vo;

import java.util.List;

public class ActiveBadgeVO {
    /**
     * 当前连续活跃天数
     */
    private int activeDay;
    /**
     * 下一个勋章需要的天数
     */
    private int nextBadgeDay;
    /**
     * 下一个勋章需要活跃天数
     */
    private int needActiveDay;
    /**
     * 当天需要的上麦分钟数
     */
    private int needOnMicMinute;
    /**
     * 当天已上麦分钟数
     */
    private int currentOnMicMinute;
    /**
     * 勋章信息列表
     */
    private List<BadgeInfo> activebadgeList;

    public static class BadgeInfo {
        /**
         * 所需连续活跃天数
         */
        private int needActiveDay;
        /**
         * 勋章id
         */
        private int badgeId;
        /**
         * 勋章图标
         */
        private String badgeIcon;
        /**
         * 该等级需要的上麦分钟数
         */
        private int needOnMicMinute;
        /**
         * 勋章解锁状态 0: 未解锁  1: 已解锁
         */
        private int status;

        public BadgeInfo() {
        }

        public BadgeInfo(int needActiveDay, int badgeId, String badgeIcon, int needOnMicMinute, int status) {
            this.needActiveDay = needActiveDay;
            this.badgeId = badgeId;
            this.badgeIcon = badgeIcon;
            this.needOnMicMinute = needOnMicMinute;
            this.status = status;
        }

        public int getNeedActiveDay() {
            return needActiveDay;
        }

        public void setNeedActiveDay(int needActiveDay) {
            this.needActiveDay = needActiveDay;
        }

        public int getBadgeId() {
            return badgeId;
        }

        public void setBadgeId(int badgeId) {
            this.badgeId = badgeId;
        }

        public String getBadgeIcon() {
            return badgeIcon;
        }

        public void setBadgeIcon(String badgeIcon) {
            this.badgeIcon = badgeIcon;
        }

        public int getNeedOnMicMinute() {
            return needOnMicMinute;
        }

        public void setNeedOnMicMinute(int needOnMicMinute) {
            this.needOnMicMinute = needOnMicMinute;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }
    }

    public int getActiveDay() {
        return activeDay;
    }

    public void setActiveDay(int activeDay) {
        this.activeDay = activeDay;
    }

    public int getNextBadgeDay() {
        return nextBadgeDay;
    }

    public void setNextBadgeDay(int nextBadgeDay) {
        this.nextBadgeDay = nextBadgeDay;
    }

    public int getNeedActiveDay() {
        return needActiveDay;
    }

    public void setNeedActiveDay(int needActiveDay) {
        this.needActiveDay = needActiveDay;
    }

    public int getNeedOnMicMinute() {
        return needOnMicMinute;
    }

    public void setNeedOnMicMinute(int needOnMicMinute) {
        this.needOnMicMinute = needOnMicMinute;
    }

    public int getCurrentOnMicMinute() {
        return currentOnMicMinute;
    }

    public void setCurrentOnMicMinute(int currentOnMicMinute) {
        this.currentOnMicMinute = currentOnMicMinute;
    }

    public List<BadgeInfo> getActivebadgeList() {
        return activebadgeList;
    }

    public void setActivebadgeList(List<BadgeInfo> activebadgeList) {
        this.activebadgeList = activebadgeList;
    }
}