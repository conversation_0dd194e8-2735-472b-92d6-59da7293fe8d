package com.quhong.data.vo;


import com.quhong.mongo.data.MomentActivityData;

import java.util.List;

public class MomentHotPostV2VO extends OtherRankConfigVO {

    /**
     * 朋友圈相关
     */
    // 朋友圈点赞上周、本周榜
    private List<HotPostRank> momentLikeLastWeekRank;
    private List<HotPostRank> momentLikeThisWeekRank;

    // 朋友圈接收礼物上周、本周榜
    private List<HotPostRank> momentGiftLastWeekRank;
    private List<HotPostRank> momentGiftThisWeekRank;

    // 朋友圈每日任务、周任务
    private List<TaskConfigVO> momentDailyTaskList;
    private List<TaskConfigVO> momentWeekTaskList;


    /**
     * 话题相关Rank、Task
     */
    // 话题发帖榜上周、本周榜
    private List<HotTopicRank> topicPostLastWeekRank;
    private List<HotTopicRank> topicPostThisWeekRank;

    // 话题点赞上周、本周榜
    private List<HotTopicRank> topicLikeLastWeekRank;
    private List<HotTopicRank> topicLikeThisWeekRank;

    // 话题接收礼物上周、本周榜
    private List<HotTopicRank> topicGiftLastWeekRank;
    private List<HotTopicRank> topicGiftThisWeekRank;

    // 话题每日任务、周任务
    private List<TaskConfigVO> topicDailyTaskList;
    private List<TaskConfigVO> topicWeekTaskList;


    public static class HotTopicRank{
        private Integer topicId;
        private String topicName;
        private String topicHead;
        private String announce;
        private long score;  // 话题- 发帖数、点赞数、礼物接收总价值

        public Integer getTopicId() {
            return topicId;
        }

        public void setTopicId(Integer topicId) {
            this.topicId = topicId;
        }

        public String getTopicName() {
            return topicName;
        }

        public void setTopicName(String topicName) {
            this.topicName = topicName;
        }

        public String getTopicHead() {
            return topicHead;
        }

        public void setTopicHead(String topicHead) {
            this.topicHead = topicHead;
        }

        public String getAnnounce() {
            return announce;
        }

        public void setAnnounce(String announce) {
            this.announce = announce;
        }

        public long getScore() {
            return score;
        }

        public void setScore(long score) {
            this.score = score;
        }
    }

    public static class HotPostRank{
        private String uid;
        private String head;
        private String momentId;
        private String text; // 内容
        private MomentActivityData.Quote quote; // 引用对象，链接、转发及分享等
        private List<MomentActivityData.Image> imgs;  // 图片信息
        private long score;  // 点赞/礼物接收总价值
        private int c_time;  // 发布时间

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getMomentId() {
            return momentId;
        }

        public void setMomentId(String momentId) {
            this.momentId = momentId;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public MomentActivityData.Quote getQuote() {
            return quote;
        }

        public void setQuote(MomentActivityData.Quote quote) {
            this.quote = quote;
        }

        public List<MomentActivityData.Image> getImgs() {
            return imgs;
        }

        public void setImgs(List<MomentActivityData.Image> imgs) {
            this.imgs = imgs;
        }

        public long getScore() {
            return score;
        }

        public void setScore(long score) {
            this.score = score;
        }

        public int getC_time() {
            return c_time;
        }

        public void setC_time(int c_time) {
            this.c_time = c_time;
        }
    }

    public List<HotPostRank> getMomentLikeLastWeekRank() {
        return momentLikeLastWeekRank;
    }

    public void setMomentLikeLastWeekRank(List<HotPostRank> momentLikeLastWeekRank) {
        this.momentLikeLastWeekRank = momentLikeLastWeekRank;
    }

    public List<HotPostRank> getMomentLikeThisWeekRank() {
        return momentLikeThisWeekRank;
    }

    public void setMomentLikeThisWeekRank(List<HotPostRank> momentLikeThisWeekRank) {
        this.momentLikeThisWeekRank = momentLikeThisWeekRank;
    }

    public List<HotPostRank> getMomentGiftLastWeekRank() {
        return momentGiftLastWeekRank;
    }

    public void setMomentGiftLastWeekRank(List<HotPostRank> momentGiftLastWeekRank) {
        this.momentGiftLastWeekRank = momentGiftLastWeekRank;
    }

    public List<HotPostRank> getMomentGiftThisWeekRank() {
        return momentGiftThisWeekRank;
    }

    public void setMomentGiftThisWeekRank(List<HotPostRank> momentGiftThisWeekRank) {
        this.momentGiftThisWeekRank = momentGiftThisWeekRank;
    }

    public List<TaskConfigVO> getMomentDailyTaskList() {
        return momentDailyTaskList;
    }

    public void setMomentDailyTaskList(List<TaskConfigVO> momentDailyTaskList) {
        this.momentDailyTaskList = momentDailyTaskList;
    }

    public List<TaskConfigVO> getMomentWeekTaskList() {
        return momentWeekTaskList;
    }

    public void setMomentWeekTaskList(List<TaskConfigVO> momentWeekTaskList) {
        this.momentWeekTaskList = momentWeekTaskList;
    }

    public List<HotTopicRank> getTopicPostLastWeekRank() {
        return topicPostLastWeekRank;
    }

    public void setTopicPostLastWeekRank(List<HotTopicRank> topicPostLastWeekRank) {
        this.topicPostLastWeekRank = topicPostLastWeekRank;
    }

    public List<HotTopicRank> getTopicPostThisWeekRank() {
        return topicPostThisWeekRank;
    }

    public void setTopicPostThisWeekRank(List<HotTopicRank> topicPostThisWeekRank) {
        this.topicPostThisWeekRank = topicPostThisWeekRank;
    }

    public List<HotTopicRank> getTopicLikeLastWeekRank() {
        return topicLikeLastWeekRank;
    }

    public void setTopicLikeLastWeekRank(List<HotTopicRank> topicLikeLastWeekRank) {
        this.topicLikeLastWeekRank = topicLikeLastWeekRank;
    }

    public List<HotTopicRank> getTopicLikeThisWeekRank() {
        return topicLikeThisWeekRank;
    }

    public void setTopicLikeThisWeekRank(List<HotTopicRank> topicLikeThisWeekRank) {
        this.topicLikeThisWeekRank = topicLikeThisWeekRank;
    }

    public List<HotTopicRank> getTopicGiftLastWeekRank() {
        return topicGiftLastWeekRank;
    }

    public void setTopicGiftLastWeekRank(List<HotTopicRank> topicGiftLastWeekRank) {
        this.topicGiftLastWeekRank = topicGiftLastWeekRank;
    }

    public List<HotTopicRank> getTopicGiftThisWeekRank() {
        return topicGiftThisWeekRank;
    }

    public void setTopicGiftThisWeekRank(List<HotTopicRank> topicGiftThisWeekRank) {
        this.topicGiftThisWeekRank = topicGiftThisWeekRank;
    }

    public List<TaskConfigVO> getTopicDailyTaskList() {
        return topicDailyTaskList;
    }

    public void setTopicDailyTaskList(List<TaskConfigVO> topicDailyTaskList) {
        this.topicDailyTaskList = topicDailyTaskList;
    }

    public List<TaskConfigVO> getTopicWeekTaskList() {
        return topicWeekTaskList;
    }

    public void setTopicWeekTaskList(List<TaskConfigVO> topicWeekTaskList) {
        this.topicWeekTaskList = topicWeekTaskList;
    }
}
