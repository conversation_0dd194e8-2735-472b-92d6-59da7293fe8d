package com.quhong.data.vo;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class GiftPanelVO {
    private int rid; // 礼物id
    private String gicon; // 礼物图标
    private String preview; // 礼物预览图
    private String weeklyIcon;   // 周星榜top1用户头像
    private ZipInfoVO zipInfo; // 礼物资源
    private int gatype; // 前端可能会通过该字段来展示动画
    private int price; // 礼物价格
    private int gtype; // 1: diamond 2: gold 3:vip
    private int comp; // ??
    private int gift_num; // vip礼物数量，背包礼物才有
    private long gtime; // 礼物播放时长
    private String gname; // 礼物名字
    private String gnamear; // 阿语礼物名字
    private List<Integer> tags; // 角标
    private long expireTime;  // 背包礼物过期时间
    private int ctime; // 客户端通过该字段判断new标签
    private int blindBox; // 是否盲盒礼物
    private Integer unlockSelect;  // 解锁礼物选择状态
    private Integer getTime;   // 背包礼物 获得此礼物时间
    private int isFusionAnimation;  // 是否融合动画礼物
    @JSONField(name = "fusion_name")
    private String fusionName;
    @JSONField(name = "fusion_head")
    private String fusionHead;
    @JSONField(name = "fusion_id")
    private String fusionId;

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public String getGicon() {
        return gicon;
    }

    public void setGicon(String gicon) {
        this.gicon = gicon;
    }

    public String getPreview() {
        return preview;
    }

    public void setPreview(String preview) {
        this.preview = preview;
    }

    public String getWeeklyIcon() {
        return weeklyIcon;
    }

    public void setWeeklyIcon(String weeklyIcon) {
        this.weeklyIcon = weeklyIcon;
    }

    public ZipInfoVO getZipInfo() {
        return zipInfo;
    }

    public void setZipInfo(ZipInfoVO zipInfo) {
        this.zipInfo = zipInfo;
    }

    public int getGatype() {
        return gatype;
    }

    public void setGatype(int gatype) {
        this.gatype = gatype;
    }

    public List<Integer> getTags() {
        return tags;
    }

    public void setTags(List<Integer> tags) {
        this.tags = tags;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public int getGtype() {
        return gtype;
    }

    public void setGtype(int gtype) {
        this.gtype = gtype;
    }

    public int getComp() {
        return comp;
    }

    public void setComp(int comp) {
        this.comp = comp;
    }

    public int getGift_num() {
        return gift_num;
    }

    public void setGift_num(int gift_num) {
        this.gift_num = gift_num;
    }

    public long getGtime() {
        return gtime;
    }

    public void setGtime(long gtime) {
        this.gtime = gtime;
    }

    public String getGname() {
        return gname;
    }

    public void setGname(String gname) {
        this.gname = gname;
    }

    public String getGnamear() {
        return gnamear;
    }

    public void setGnamear(String gnamear) {
        this.gnamear = gnamear;
    }

    public long getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(long expireTime) {
        this.expireTime = expireTime;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public int getBlindBox() {
        return blindBox;
    }

    public void setBlindBox(int blindBox) {
        this.blindBox = blindBox;
    }

    public Integer getUnlockSelect() {
        return unlockSelect;
    }

    public void setUnlockSelect(Integer unlockSelect) {
        this.unlockSelect = unlockSelect;
    }

    public Integer getGetTime() {
        return getTime;
    }

    public void setGetTime(Integer getTime) {
        this.getTime = getTime;
    }

    public int getIsFusionAnimation() {
        return isFusionAnimation;
    }

    public void setIsFusionAnimation(int isFusionAnimation) {
        this.isFusionAnimation = isFusionAnimation;
    }

    public String getFusionName() {
        return fusionName;
    }

    public void setFusionName(String fusionName) {
        this.fusionName = fusionName;
    }

    public String getFusionHead() {
        return fusionHead;
    }

    public void setFusionHead(String fusionHead) {
        this.fusionHead = fusionHead;
    }

    public String getFusionId() {
        return fusionId;
    }

    public void setFusionId(String fusionId) {
        this.fusionId = fusionId;
    }
}
