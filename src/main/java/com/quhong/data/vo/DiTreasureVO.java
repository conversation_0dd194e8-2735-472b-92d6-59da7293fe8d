package com.quhong.data.vo;

import com.quhong.mongo.data.ResourceKeyConfigData;

import java.util.List;

public class DiTreasureVO extends OtherRankConfigVO{

    /**
     * 当前用户体力数
     */
    private Integer balance;
    /**
     * 当前轮已抽取的次数
     */
    private Integer roundTimes;
    /**
     * 下次抽奖消耗的体力数
     */
    private Integer nextDrawNeed;
    /**
     * 本轮一次性抽完需要的体力数
     */
    private Integer roundAllDrawNeed;

    /**
     * 下次抽奖需要充值的总钻石数
     * （需要的体力运算）
     */
    private Integer onceDrawNeedTotalGold;
    /**
     * 已经充值的金币数
     * （体力运算 + 库存金币）
     */
    private Integer currGoldBalance;
    /**
     * 本轮一次性抽完需要充值的总金币数
     * （需要的体力运算）
     */
    private Integer roundAllDrawNeedTotalGold;

    /**
     * 抽奖奖励列表(需要正序排序)
     */
    private List<DiResourceVO> drawAwardList;
    private List<RecordVO> recordList;
    private Integer nextUrl;

    public static class RecordVO{
        private Integer consumer;
        private List<DiResourceVO> drawAwardList;
        private Integer useWay;
        private Integer ctime;

        public Integer getConsumer() {
            return consumer;
        }

        public void setConsumer(Integer consumer) {
            this.consumer = consumer;
        }

        public List<DiResourceVO> getDrawAwardList() {
            return drawAwardList;
        }

        public void setDrawAwardList(List<DiResourceVO> drawAwardList) {
            this.drawAwardList = drawAwardList;
        }

        public Integer getUseWay() {
            return useWay;
        }

        public void setUseWay(Integer useWay) {
            this.useWay = useWay;
        }

        public Integer getCtime() {
            return ctime;
        }

        public void setCtime(Integer ctime) {
            this.ctime = ctime;
        }
    }


    public static class DiResourceVO extends ResourceKeyConfigData.ResourceMeta{
        private String name;
        private Integer draw;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getDraw() {
            return draw;
        }

        public void setDraw(Integer draw) {
            this.draw = draw;
        }
    }

    public Integer getBalance() {
        return balance;
    }

    public void setBalance(Integer balance) {
        this.balance = balance;
    }

    public Integer getRoundTimes() {
        return roundTimes;
    }

    public void setRoundTimes(Integer roundTimes) {
        this.roundTimes = roundTimes;
    }

    public Integer getNextDrawNeed() {
        return nextDrawNeed;
    }

    public void setNextDrawNeed(Integer nextDrawNeed) {
        this.nextDrawNeed = nextDrawNeed;
    }

    public Integer getRoundAllDrawNeed() {
        return roundAllDrawNeed;
    }

    public void setRoundAllDrawNeed(Integer roundAllDrawNeed) {
        this.roundAllDrawNeed = roundAllDrawNeed;
    }

    public Integer getOnceDrawNeedTotalGold() {
        return onceDrawNeedTotalGold;
    }

    public void setOnceDrawNeedTotalGold(Integer onceDrawNeedTotalGold) {
        this.onceDrawNeedTotalGold = onceDrawNeedTotalGold;
    }

    public Integer getCurrGoldBalance() {
        return currGoldBalance;
    }

    public void setCurrGoldBalance(Integer currGoldBalance) {
        this.currGoldBalance = currGoldBalance;
    }

    public Integer getRoundAllDrawNeedTotalGold() {
        return roundAllDrawNeedTotalGold;
    }

    public void setRoundAllDrawNeedTotalGold(Integer roundAllDrawNeedTotalGold) {
        this.roundAllDrawNeedTotalGold = roundAllDrawNeedTotalGold;
    }

    public List<DiResourceVO> getDrawAwardList() {
        return drawAwardList;
    }

    public void setDrawAwardList(List<DiResourceVO> drawAwardList) {
        this.drawAwardList = drawAwardList;
    }

    public List<RecordVO> getRecordList() {
        return recordList;
    }

    public void setRecordList(List<RecordVO> recordList) {
        this.recordList = recordList;
    }

    public Integer getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(Integer nextUrl) {
        this.nextUrl = nextUrl;
    }
}
