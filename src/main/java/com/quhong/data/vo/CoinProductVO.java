package com.quhong.data.vo;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
public class CoinProductVO {

    /**
     * 商品编号
     */
    private Integer pid;

    /**
     * 需要钻石数
     */
    private Integer diamonds;

    /**
     * 可兑换金币数
     */
    private Integer coins;

    /**
     * 金币图标
     */
    private String icon;

    public CoinProductVO() {
    }

    public CoinProductVO(Integer pid, Integer diamonds, Integer coins, String icon) {
        this.pid = pid;
        this.diamonds = diamonds;
        this.coins = coins;
        this.icon = icon;
    }

    public Integer getPid() {
        return pid;
    }

    public void setPid(Integer pid) {
        this.pid = pid;
    }

    public Integer getDiamonds() {
        return diamonds;
    }

    public void setDiamonds(Integer diamonds) {
        this.diamonds = diamonds;
    }

    public Integer getCoins() {
        return coins;
    }

    public void setCoins(Integer coins) {
        this.coins = coins;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
}
