package com.quhong.data.vo;


import java.util.List;

public class YourCircleVO extends OtherRankConfigVO {
    private RankInfo charmRankInfo; // 魅力榜单信息
    private RankInfo generousRankInfo; // 慷慨榜单信息
    private RankInfo supportInfo; // 支持者榜单信息
    private int fbGender; //  1 男 2女
    private int isJoinSuperQueen; // 是否加入超级女王活动 1已加入 0未加入

    public static class MyCircleRankVO extends OtherRankingListVO  {
        private int sendingScore;
        private int receiveScore;

        public int getSendingScore() {
            return sendingScore;
        }

        public void setSendingScore(int sendingScore) {
            this.sendingScore = sendingScore;
        }

        public int getReceiveScore() {
            return receiveScore;
        }

        public void setReceiveScore(int receiveScore) {
            this.receiveScore = receiveScore;
        }
    }


    public static class RankInfo {
        private List<OtherRankingListVO> rankingList; // 榜单列表
        private List<OtherRankingListVO> supportList; // 支持者列表 
        private MyCircleRankVO myRankVO; // 我的排名

        public List<OtherRankingListVO> getRankingList() {
            return rankingList;
        }

        public void setRankingList(List<OtherRankingListVO> rankingList) {
            this.rankingList = rankingList;
        }

        public List<OtherRankingListVO> getSupportList() {
            return supportList;
        }

        public void setSupportList(List<OtherRankingListVO> supportList) {
            this.supportList = supportList;
        }

        public MyCircleRankVO getMyRankVO() {
            return myRankVO;
        }

        public void setMyRankVO(MyCircleRankVO myRankVO) {
            this.myRankVO = myRankVO;
        }
    }

    public RankInfo getCharmRankInfo() {
        return charmRankInfo;
    }

    public void setCharmRankInfo(RankInfo charmRankInfo) {
        this.charmRankInfo = charmRankInfo;
    }

    public RankInfo getGenerousRankInfo() {
        return generousRankInfo;
    }

    public void setGenerousRankInfo(RankInfo generousRankInfo) {
        this.generousRankInfo = generousRankInfo;
    }

    public RankInfo getSupportInfo() {
        return supportInfo;
    }

    public void setSupportInfo(RankInfo supportInfo) {
        this.supportInfo = supportInfo;
    }

    public int getFbGender() {
        return fbGender;
    }

    public void setFbGender(int fbGender) {
        this.fbGender = fbGender;
    }

    public int getIsJoinSuperQueen() {
        return isJoinSuperQueen;
    }

    public void setIsJoinSuperQueen(int isJoinSuperQueen) {
        this.isJoinSuperQueen = isJoinSuperQueen;
    }
}
