package com.quhong.gate.net;

import com.quhong.core.net.connect.IConnector;
import com.quhong.core.net.server.adapter.IConnectorFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2018/10/15
 * @copyright szxx
 */
@Component
public class GateConnectorFactory implements IConnectorFactory {
    @Autowired
    private GateMsgProcessor msgProcessor;

    public GateConnectorFactory(){

    }

    @Override
    public IConnector createConnector() {
        return new GateConnector(msgProcessor);
    }
}
