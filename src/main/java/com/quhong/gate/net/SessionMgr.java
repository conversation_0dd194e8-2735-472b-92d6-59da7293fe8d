package com.quhong.gate.net;

import com.quhong.core.concurrency.queues.TaskQueue;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2018/10/19
 * @copyright szxx
 */
@Component
public class SessionMgr extends TaskQueue {
    private static final Logger logger = LoggerFactory.getLogger(SessionMgr.class);

    public static SessionMgr instance = null;

    private Map<Long, GateConnector> sessionMap = new ConcurrentHashMap<>();

    private Map<String, GateConnector> uidMap = new ConcurrentHashMap<>();

    private Map<String, Long> blackIpSet = new ConcurrentHashMap<>();

    public SessionMgr() {
        instance = this;
    }

    @PostConstruct
    public void postInit() {
        // 5分钟查询一次
        TimerService.getService().addDelay(new LoopTask(this, 5 * 60 * 1000) {
            @Override
            protected void execute() {
                onTick();
            }
        });
    }

    private void onTick() {
        for (Map.Entry<Long, GateConnector> entry : sessionMap.entrySet()) {
            if (entry.getValue().isDispose()) {
                String uid = entry.getValue().getUid();
                sessionMap.remove(entry.getKey());
                logger.error("sessionMap remove connector. uid={} sessionId={}", uid, entry.getKey());
            }
        }
        for (Map.Entry<String, GateConnector> entry : uidMap.entrySet()) {
            if (entry.getValue().isDispose()) {
                String uid = entry.getValue().getUid();
                long sessionId = entry.getValue().getSessionId();
                uidMap.remove(entry.getKey(), entry.getValue());
                logger.error("uidMap remove connector. uid={} sessionId={}", uid, sessionId);
            }
        }
    }

    public void addToSessionMap(GateConnector connector) {
        if (connector.getSessionId() == 0) {
            logger.error("connector.sessionId == 0, sessionId={}", connector.getSessionId());
            return;
        }
        if (sessionMap.containsKey(connector.getSessionId())) {
            logger.error("connector duplicated. sessionId={}", connector.getSessionId());
        }
        sessionMap.put(connector.getSessionId(), connector);
    }

    public int getSessionCount() {
        return uidMap.size();
    }

    public Map<String, GateConnector> getUidMap() {
        return uidMap;
    }

    public GateConnector getConnectorBySessionId(long sessionId) {
        return sessionMap.get(sessionId);
    }

    public GateConnector getConnectorByUid(String uid) {
        return uidMap.get(uid);
    }

    public boolean addConnectorToUidMap(GateConnector connector) {
        if(StringUtils.isEmpty(connector.getUid())){
            logger.error("add connector error . the connector uid is empty. uid={} sessionI={}", connector.getUid(), connector.getSessionId());
            return false;
        }
//        logger.info("add connector to uidMap. uid={} sessionId={}", connector.getUid(), connector.getSessionId());
        uidMap.put(connector.getUid(), connector);
        if (connector.isDispose()) {
            logger.error("after add connector . the connector is disposed. uid={} sessionI={}", connector.getUid(), connector.getSessionId());
            uidMap.remove(connector.getUid(), connector);
            return false;
        }
        return true;
    }

    public boolean remove(GateConnector connector) {
        boolean result = true;
        if (!StringUtils.isEmpty(connector.getUid())) {
            if (uidMap.remove(connector.getUid(), connector)) {
                // 发送离线消息给聊天服
//                logger.info("remove connector success. uid={} sessionId={}", connector.getUid(), connector.getSessionId());
                connector.sendOffline();
            }else{
                logger.info("remove connector error. sessionId={}", connector.getSessionId());
                result = false;
            }
        }else{
//            logger.info("remove connector error. uid is empty. uid={} sessionId={}", connector.getUid(), connector.getSessionId());
        }
        sessionMap.remove(connector.getSessionId());
        return result;
    }

    public void addBlackIp(String ip) {
        if (blackIpSet.size() > 10000) {
            blackIpSet.clear();
        }
        long time = System.currentTimeMillis() + getLockTime(ip);
        blackIpSet.put(ip, time);
    }

    public boolean inBlackIp(String ip) {
        Long time = blackIpSet.get(ip);
        if (time == null) {
            return false;
        }
        long curTime = System.currentTimeMillis();
        if (curTime < time) {
            return true;
        }
        removeBlackIp(ip);
        return false;
    }

    public void removeBlackIp(String ip) {
        blackIpSet.remove(ip);
    }

    public long getLockTime(String ip) {
        return 1800 * 1000; //半小时
    }
}
