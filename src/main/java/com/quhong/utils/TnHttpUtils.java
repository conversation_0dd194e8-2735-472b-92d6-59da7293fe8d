package com.quhong.utils;

import com.quhong.core.web.HttpResponseData;
import com.quhong.core.web.WebClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.util.HashMap;
import java.util.Map;

@Component
public class TnHttpUtils {
    private static final Logger logger = LoggerFactory.getLogger(TnHttpUtils.class);

    private final static String CHARSET = "UTF-8";

    private static String URL = "https://rce.tencentcloudapi.com/";

    private static final Map<String, String> headers = new HashMap<>();

    static {
        headers.put("accept", "*/*");
        headers.put("connection", "Keep-Alive");
        headers.put("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
        headers.put("Content-Type","application/x-www-form-urlencoded");

    }

    @Autowired
    private WebClient webClient;

    public String sign(String s, String key, String method) throws Exception {
        Mac mac = Mac.getInstance(method);
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(CHARSET), mac.getAlgorithm());
        mac.init(secretKeySpec);
        byte[] hash = mac.doFinal(s.getBytes(CHARSET));
        return DatatypeConverter.printBase64Binary(hash);
    }

    public String getStringToSign(Map<String, String> params) {
        StringBuilder s2s = new StringBuilder("POSTrce.tencentcloudapi.com/?");
        // 签名时要求对参数进行字典排序，此处用TreeMap保证顺序
        for (String k : params.keySet()) {
            s2s.append(k).append("=").append(params.get(k)).append("&");
        }
        return s2s.substring(0, s2s.length() - 1);
    }

    /**
     * 通过给定的请求参数和编码格式，以获取服务器返回的数据
     * params 请求参数
     * encode 编码格式
     * @return
     */
    public HttpResponseData<String> sendPost(Map<String, String> params) {
        return webClient.sendPostWithHttpResp(URL, params, 1);
    }
}
