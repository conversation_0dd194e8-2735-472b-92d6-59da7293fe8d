package com.quhong.vo;

import com.quhong.data.RidData;
import com.quhong.mongo.data.ResourceKeyConfigData;

import java.util.List;

public class WebTurntableVO {

    private Integer coinBalance;    // 金币余额
    private List<ResourceKeyConfigData.ResourceMeta> resourceConfigList;  // 抽奖配置
    private List<RollRecordData> rollRecordList;   // 滚屏列表
    private List<RollRecordData> recordList;   // 抽奖记录列表
    private Integer nextUrl;

    // 抽奖排行榜
    private Integer todayEndTime;                        // 今日结束时间
    private Integer thisWeekEndTime;                     // 本周结束时间
    private List<TurntableRank> todayTurntableRank;      // 今日榜单
    private List<TurntableRank> thisWeekTurntableRank;   // 本周榜单
    private List<TurntableRank> lastWeekTurntableRank;   // 上周榜单

    public static class TurntableRank {
        private String uid;
        private String name;
        private String head;
        private String scoreStr;
        private Integer rank;
        private RidData ridData;

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getScoreStr() {
            return scoreStr;
        }

        public void setScoreStr(String scoreStr) {
            this.scoreStr = scoreStr;
        }

        public Integer getRank() {
            return rank;
        }

        public void setRank(Integer rank) {
            this.rank = rank;
        }

        public RidData getRidData() {
            return ridData;
        }

        public void setRidData(RidData ridData) {
            this.ridData = ridData;
        }
    }


    public static class RollRecordData extends ResourceKeyConfigData.ResourceMeta{
        private String uid;                 // 用户uid
        private String name;                // 用户名
        private String head;                // 用户头像
        private Integer costCoin;           // 消耗金币
        private Integer ctime;              // 抽奖时间

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public Integer getCostCoin() {
            return costCoin;
        }

        public void setCostCoin(Integer costCoin) {
            this.costCoin = costCoin;
        }

        public Integer getCtime() {
            return ctime;
        }

        public void setCtime(Integer ctime) {
            this.ctime = ctime;
        }
    }


    public Integer getCoinBalance() {
        return coinBalance;
    }

    public void setCoinBalance(Integer coinBalance) {
        this.coinBalance = coinBalance;
    }

    public List<ResourceKeyConfigData.ResourceMeta> getResourceConfigList() {
        return resourceConfigList;
    }

    public void setResourceConfigList(List<ResourceKeyConfigData.ResourceMeta> resourceConfigList) {
        this.resourceConfigList = resourceConfigList;
    }

    public List<RollRecordData> getRollRecordList() {
        return rollRecordList;
    }

    public void setRollRecordList(List<RollRecordData> rollRecordList) {
        this.rollRecordList = rollRecordList;
    }

    public List<RollRecordData> getRecordList() {
        return recordList;
    }

    public void setRecordList(List<RollRecordData> recordList) {
        this.recordList = recordList;
    }

    public Integer getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(Integer nextUrl) {
        this.nextUrl = nextUrl;
    }

    public Integer getTodayEndTime() {
        return todayEndTime;
    }

    public void setTodayEndTime(Integer todayEndTime) {
        this.todayEndTime = todayEndTime;
    }

    public Integer getThisWeekEndTime() {
        return thisWeekEndTime;
    }

    public void setThisWeekEndTime(Integer thisWeekEndTime) {
        this.thisWeekEndTime = thisWeekEndTime;
    }

    public List<TurntableRank> getTodayTurntableRank() {
        return todayTurntableRank;
    }

    public void setTodayTurntableRank(List<TurntableRank> todayTurntableRank) {
        this.todayTurntableRank = todayTurntableRank;
    }

    public List<TurntableRank> getThisWeekTurntableRank() {
        return thisWeekTurntableRank;
    }

    public void setThisWeekTurntableRank(List<TurntableRank> thisWeekTurntableRank) {
        this.thisWeekTurntableRank = thisWeekTurntableRank;
    }

    public List<TurntableRank> getLastWeekTurntableRank() {
        return lastWeekTurntableRank;
    }

    public void setLastWeekTurntableRank(List<TurntableRank> lastWeekTurntableRank) {
        this.lastWeekTurntableRank = lastWeekTurntableRank;
    }
}
