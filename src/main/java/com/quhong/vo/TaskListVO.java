package com.quhong.vo;

import com.quhong.data.vo.HomeBanner;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/20
 */
public class TaskListVO {

    /**
     * 用户是否在房
     */
    private String inRoom;
    /**
     * 用户国家
     */
    private String country;
    /**
     * 金币余额
     */
    private Long coinBalance;

    /**
     * 抽奖卷数量
     */
    private Integer lotteryTicketNum;

    /**
     * banner展示
     */
    private List<HomeBanner> bannerList;

    /**
     * 新人任务
     */
    private TaskInfoVO newcomerTasks;

    /**
     * 每日任务
     */
    private TaskInfoVO dailyTasks;

    /**
     * 进阶任务
     */
    private TaskInfoVO advancedTasks;

    public static class Banner {

        private String title;
        private String preview;
        private String url;
        private int turnCode;
        private Integer atype;
        private int webType; // 0全屏 1或2 半屏
        private String room_id;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getPreview() {
            return preview;
        }

        public void setPreview(String preview) {
            this.preview = preview;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public int getTurnCode() {
            return turnCode;
        }

        public void setTurnCode(int turnCode) {
            this.turnCode = turnCode;
        }

        public Integer getAtype() {
            return atype;
        }

        public void setAtype(Integer atype) {
            this.atype = atype;
        }

        public String getRoom_id() {
            return room_id;
        }

        public void setRoom_id(String room_id) {
            this.room_id = room_id;
        }

        public int getWebType() {
            return webType;
        }

        public void setWebType(int webType) {
            this.webType = webType;
        }
    }

    public String getInRoom() {
        return inRoom;
    }

    public void setInRoom(String inRoom) {
        this.inRoom = inRoom;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Long getCoinBalance() {
        return coinBalance;
    }

    public void setCoinBalance(Long coinBalance) {
        this.coinBalance = coinBalance;
    }

    public Integer getLotteryTicketNum() {
        return lotteryTicketNum;
    }

    public void setLotteryTicketNum(Integer lotteryTicketNum) {
        this.lotteryTicketNum = lotteryTicketNum;
    }

    public List<HomeBanner> getBannerList() {
        return bannerList;
    }

    public void setBannerList(List<HomeBanner> bannerList) {
        this.bannerList = bannerList;
    }

    public TaskInfoVO getNewcomerTasks() {
        return newcomerTasks;
    }

    public void setNewcomerTasks(TaskInfoVO newcomerTasks) {
        this.newcomerTasks = newcomerTasks;
    }

    public TaskInfoVO getDailyTasks() {
        return dailyTasks;
    }

    public void setDailyTasks(TaskInfoVO dailyTasks) {
        this.dailyTasks = dailyTasks;
    }

    public TaskInfoVO getAdvancedTasks() {
        return advancedTasks;
    }

    public void setAdvancedTasks(TaskInfoVO advancedTasks) {
        this.advancedTasks = advancedTasks;
    }
}
