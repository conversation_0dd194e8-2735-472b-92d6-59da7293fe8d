package com.quhong.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/2
 */
public class LuckyBoxRecordVO {

    private List<GetBoxInfo> list;
    private BoxInfo box_info;
    private BoxOwnerInfo box_owner;

    public LuckyBoxRecordVO() {
    }

    public LuckyBoxRecordVO(List<GetBoxInfo> list, BoxInfo box_info, BoxOwnerInfo box_owner) {
        this.list = list;
        this.box_info = box_info;
        this.box_owner = box_owner;
    }

    public List<GetBoxInfo> getList() {
        return list;
    }

    public void setList(List<GetBoxInfo> list) {
        this.list = list;
    }

    public BoxInfo getBox_info() {
        return box_info;
    }

    public void setBox_info(BoxInfo box_info) {
        this.box_info = box_info;
    }

    public BoxOwnerInfo getBox_owner() {
        return box_owner;
    }

    public void setBox_owner(BoxOwnerInfo box_owner) {
        this.box_owner = box_owner;
    }

    public static class GetBoxInfo {
        private String name;
        private String head;
        private String micFrame;
        private int vip_level;
        private int beans;
        private int identify;

        public GetBoxInfo() {
        }

        public GetBoxInfo(String name, String head, int vip_level, int beans, int identify) {
            this.name = name;
            this.head = head;
            this.vip_level = vip_level;
            this.beans = beans;
            this.identify = identify;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getMicFrame() {
            return micFrame;
        }

        public void setMicFrame(String micFrame) {
            this.micFrame = micFrame;
        }

        public int getVip_level() {
            return vip_level;
        }

        public void setVip_level(int vip_level) {
            this.vip_level = vip_level;
        }

        public int getBeans() {
            return beans;
        }

        public void setBeans(int beans) {
            this.beans = beans;
        }

        public int getIdentify() {
            return identify;
        }

        public void setIdentify(int identify) {
            this.identify = identify;
        }
    }

    public static class BoxOwnerInfo {
        private String owner_name;
        private String owner_head;
        private String owner_micFrame;
        private int owner_vip_level;
        private int owner_identify;

        public BoxOwnerInfo() {
        }

        public BoxOwnerInfo(String owner_name, String owner_head, String owner_micFrame, int owner_vip_level, int owner_identify) {
            this.owner_name = owner_name;
            this.owner_head = owner_head;
            this.owner_micFrame = owner_micFrame;
            this.owner_vip_level = owner_vip_level;
            this.owner_identify = owner_identify;
        }

        public String getOwner_name() {
            return owner_name;
        }

        public void setOwner_name(String owner_name) {
            this.owner_name = owner_name;
        }

        public String getOwner_head() {
            return owner_head;
        }

        public void setOwner_head(String owner_head) {
            this.owner_head = owner_head;
        }

        public String getOwner_micFrame() {
            return owner_micFrame;
        }

        public void setOwner_micFrame(String owner_micFrame) {
            this.owner_micFrame = owner_micFrame;
        }

        public int getOwner_vip_level() {
            return owner_vip_level;
        }

        public void setOwner_vip_level(int owner_vip_level) {
            this.owner_vip_level = owner_vip_level;
        }

        public int getOwner_identify() {
            return owner_identify;
        }

        public void setOwner_identify(int owner_identify) {
            this.owner_identify = owner_identify;
        }
    }

    public static class BoxInfo {
        private String luck_box_msg;
        private int total_num;
        private int has_already_get_box_num;

        public BoxInfo() {
        }

        public BoxInfo(String luck_box_msg, int total_num, int has_already_get_box_num) {
            this.luck_box_msg = luck_box_msg;
            this.total_num = total_num;
            this.has_already_get_box_num = has_already_get_box_num;
        }

        public String getLuck_box_msg() {
            return luck_box_msg;
        }

        public void setLuck_box_msg(String luck_box_msg) {
            this.luck_box_msg = luck_box_msg;
        }

        public int getTotal_num() {
            return total_num;
        }

        public void setTotal_num(int total_num) {
            this.total_num = total_num;
        }

        public int getHas_already_get_box_num() {
            return has_already_get_box_num;
        }

        public void setHas_already_get_box_num(int has_already_get_box_num) {
            this.has_already_get_box_num = has_already_get_box_num;
        }
    }
}
