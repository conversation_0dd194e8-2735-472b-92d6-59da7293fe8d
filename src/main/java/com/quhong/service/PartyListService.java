package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.cache.CacheMap;
import com.quhong.constant.BackStageConfigConstant;
import com.quhong.constant.ConfigCenterKeyConstant;
import com.quhong.constant.ConfigCenterLabelKeyConstant;
import com.quhong.constant.RoomListConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.*;
import com.quhong.data.vo.GameListVO;
import com.quhong.data.vo.RoomEventVO;
import com.quhong.enums.RoomConstant;
import com.quhong.enums.RoomType;
import com.quhong.enums.TruthDareV2Constant;
import com.quhong.enums.VideoActionType;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.SudGamePlayerData;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.obj.RoomMicInfoObject;
import com.quhong.msg.obj.RoomMicUserObject;
import com.quhong.msg.room.RoomHotDevoteChangePushMsg;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.RoomEventData;
import com.quhong.mysql.data.RoomMicData;
import com.quhong.redis.*;
import com.quhong.room.OfficialRoom;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.RoomActionRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.room.redis.RoomRedis;
import com.quhong.task.RoomListTickData;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.ArithmeticUtils;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.RoomMicListVo;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class PartyListService {
    private static final Logger logger = LoggerFactory.getLogger(PartyListService.class);
    private static final List<String> NOT_SHOW_IN_POPULAR_LIST = new ArrayList<>();
    private static final RoomListTickData TICK_DATA = new RoomListTickData();
    // 缓存的列表
    private final Map<String, PartyListData> ALL_PARTY_DATA = new ConcurrentHashMap<>();
    private final Map<String, Integer> LAST_ONLINE = new ConcurrentHashMap<>();

    // 缓存的列表
    private final Map<String, PartyListData> GAME_PARTY_DATA = new ConcurrentHashMap<>();

    private final int MAX_SPACE = 10000000;
    Comparator<PartyListData> ALL_WEIGHT_COMP = Comparator.comparing(o -> {
        // 按照区域分, 分钟内房间人气*5% + 分钟内房间上麦人数*35% + 分钟内产出的礼物总价值*50% + 分钟内公屏聊天人数*15%
        if (o.getPwd() == 0) {
            return Integer.MAX_VALUE - MAX_SPACE
                    + o.getWeight() * 10 * 0.05
                    + o.getMicCount() * 200 * 0.35
                    + o.getDevote() / 200 * 0.5
                    + o.getMsgCount() * 100 * 0.15;
        }
        return o.getWeight();
    });

    private final Map<Integer, Integer> GAME_TYPE_MAP_COUNT = new ConcurrentHashMap<>();

    private static final long CACHE_TIME_MILLIS = 24 * 60 * 60 * 1000L;
    private final CacheMap<String, Integer> cacheNoticeMap = new CacheMap<>(CACHE_TIME_MILLIS);

    private final String RECOMMEND_MSG_EN = "Your room is currently recommended";

    private final String RECOMMEND_MSG_AR = "غرفتك موصى بها حاليًا";

    private final String KICK_MSG_EN = "kick user will cancel  room recommendation";

    private final String KICK_MSG_AR = "قد يؤدي طرد المستخدم إلى إلغاء التوصية به";

    private Set<String> recentlyActionRoom;

    private static final Map<Integer, Integer> GENDER_WEIGHT_MAP = new HashMap<Integer, Integer>() {
        {
            put(1, 100);
            put(2, 80);
            put(3, 50);
        }
    };

    @Resource
    private RoomRedis roomRedis;
    @Resource
    private TopRoomRedis topRoomRedis;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private TurntableGameRedis turntableGameRedis;
    @Resource
    private TruthOrDareRedis truthOrDareRedis;
    @Resource
    private TruthOrDareV2Redis truthOrDareV2Redis;
    @Resource
    private AreaBlackUserRedis areaBlackUserRedis;
    @Resource
    private RoomFrameRedis roomFrameRedis;
    @Resource
    private GiftRecordDao giftRecordDao;
    @Resource
    private RoomMessageDao roomMessageDao;
    @Resource
    private RoomActionRedis roomActionRedis;
    @Resource
    private RoomListRedis roomListRedis;
    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private RoomMicDao roomMicDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private RoomHotDevoteRedis roomHotDevoteRedis;
    @Resource
    protected RoomWebSender roomWebSender;
    @Resource
    private RoomEventDao roomEventDao;
    @Resource
    private BackstageConfigDao backstageConfigDao;
    @Resource
    private NewRookieRoomRedis newRookieRoomRedis;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private RecreationTagService recreationTagService;
    @Resource
    private ConfigCenterService configCenterService;
    @Resource
    private RoomLevelService roomLevelService;
    @Resource
    private GameRoomRedis gameRoomRedis;
    @Resource
    private RoomVisitorsRedis roomVisitorsRedis;
    @Resource
    private FamilyMemberDao familyMemberDao;
    @Autowired
    private OperationConfigRedis operationConfigRedis;


    @PostConstruct
    public void postInit() {
        if (ServerConfig.isProduct()) {
            NOT_SHOW_IN_POPULAR_LIST.add("r:5ac220d61bad4898c89dad7e");
        }
        cacheNoticeMap.start();
    }

    public PartyListData getPartyData(String roomId) {
        return ALL_PARTY_DATA.get(roomId);
    }

    /**
     * Popular列表排序及展示规则：
     * -列表Top4的国家展示平台最热门的国家，不以用户所在地区区分。
     * -从第5个房间开始，展示用户所在国家区域的房间，即GCC国家用户，优先展示GCC国家的房间，非GCC用户，优先展示非GCC国家的房间。
     * -各区域的房间人数少于20个人时，展示另一地区的房间。
     * 例如：GCC国家房间人数大于等于20个人的房间排完了之后，再把中东国家的房间和GCC低于20个人的房间排在后面。2个区域的房间都按房间人数和房间贡献值降序排列。
     * <p>
     * -海湾六国国家GCC：沙特阿拉伯、科威特、阿联酋、卡塔尔、阿曼、巴林、美国
     * -中东国家：除GCC国家以外的所有国家
     */
    public void makePartyList() {
        // 房间列表
        PartyData partyData = fillPartyList();
        // 普通列表
        doMakePartyList(partyData.getAllList(), partyData.isRefreshSaWeight());
        // 更新room表中的在线人数
        doUpdateRoomOnline(partyData.getAllList());
        // 游戏列表
        doMakeGameList(partyData.getGameList());
        // 847版本游戏列表
        doMakeAllGameList(partyData.getGameList());
        // 847版本新增房间活动列表
        doMakeEventList();
        // 847版本新增房间新创建房间列表
        doMakeActiveRoomList(partyData.getAllList());
        // 859仅房间人数排序
        doMakeOnlyActorCountRoomList(partyData.getAllList());
    }

    private void doUpdateRoomOnline(List<PartyListData> allList) {
        try {
            // 15*6秒执行一次
            if (!TICK_DATA.roomUpdateCheck(6) || allList.isEmpty()) {
                return;
            }
            long startTime = System.currentTimeMillis();
            BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, MongoRoomData.class);
            int updateCount = 0;
            for (PartyListData partyListData : allList) {
                Integer lastOnline = LAST_ONLINE.get(partyListData.getRoomId());
                if (null != lastOnline && partyListData.getRealOnline() == lastOnline) {
                    continue;
                }
                LAST_ONLINE.put(partyListData.getRoomId(), partyListData.getOnline());
                updateCount++;
                Update update = new Update();
                update.set("online", partyListData.getRealOnline());
                operations.updateOne(new Query(Criteria.where("rid").is(partyListData.getRoomId())), update);
            }
            if (updateCount > 0) {
                operations.execute();
            }
            logger.info("update room online size={} cost={}", updateCount, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            logger.error("doUpdateRoomOnline error size={} msg={}", allList.size(), e.getMessage(), e);
        }
    }

    private void doMakeGameList(List<GameListVO> gameList) {
        int k = 1;
        Map<String, String> gameTypeOnlineMap = new HashMap<>();
        long timeMillis = System.currentTimeMillis();
        Map<Integer, List<GameListVO>> gameTypeListMap = CollectionUtil.listToKeyListMap(gameList, GameListVO::getGame_type);
        for (Integer gameType : gameTypeListMap.keySet()) {
            List<GameListVO> gameListVOList = gameTypeListMap.get(gameType);
            gameListVOList.forEach(item -> {
                int myOnline = item.getOnline() * k;
                item.setOnline(myOnline);
                String strGameType = String.valueOf(gameType);
                gameTypeOnlineMap.put(strGameType, Integer.parseInt(gameTypeOnlineMap.getOrDefault(strGameType, "0")) + myOnline + "");
            });
            saveGameListToRedis(gameListVOList, gameType);
        }
        roomListRedis.deleteGameTypeDataAll();
        roomListRedis.setGameTypeDataAll(gameTypeOnlineMap);
        logger.info("makeGameList total={} totalType={} " +
                        "gameTypeOnlineMap={} cost={}", gameList.size(), gameTypeListMap.size()
                , gameTypeOnlineMap, System.currentTimeMillis() - timeMillis);
    }

    private void doMakeAllGameList(List<GameListVO> gameList) {
        long timeMillis = System.currentTimeMillis();
        // 20240909修改  房间id去重，同时存在幸运转盘和Ludo或umo游戏时，优先展示幸运转盘
        List<GameListVO> allGameList = new ArrayList<>();
        Map<String, List<GameListVO>> gameRoomIdMap = CollectionUtil.listToKeyListMap(gameList, GameListVO::getRoomId);
        for (Map.Entry<String, List<GameListVO>> entry : gameRoomIdMap.entrySet()) {
            List<GameListVO> roomList = entry.getValue();
            // 20240909修改  房间id去重，同时存在幸运转盘和Ludo或umo游戏时，优先展示幸运转盘
            // if (roomList.size() != 1) {
            //     roomList.sort(Comparator.comparingInt(GameListVO::getGame_type));
            // }
            // 按最新玩游戏展示
            if (roomList.size() != 1) {
                // logger.info("roomList:{}", JSONObject.toJSONString(roomList));
                roomList.sort(Comparator.comparingInt(GameListVO::getGameCtime).reversed());
            }
            GameListVO gameListVO = new GameListVO();
            BeanUtils.copyProperties(roomList.get(0), gameListVO);
            allGameList.add(gameListVO);
        }
        allGameList.sort(Comparator.comparing(GameListVO::getOnline).reversed());
        roomListRedis.saveAllGameList(allGameList);
        logger.info("makeAllGameList total={} cost={}", allGameList.size(), System.currentTimeMillis() - timeMillis);
    }

    private void doMakeEventList() {
        int nowTime = DateHelper.getNowSeconds();
//        Map<String, Integer> allMemberMap = familyMemberDao.getAllMemberFromCache();
        List<RoomEventData> roomEventList = roomEventDao.getRoomEventList(nowTime);
        List<RoomEventVO> partyingList = new ArrayList<>();
        List<RoomEventVO> comingSoonList = new ArrayList<>();
        List<RoomEventVO> comingSoonAndPartyList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roomEventList)) {
            int indexCount = 1;
            int partyIndexCount = 1;
            for (RoomEventData data : roomEventList) {
                if (whiteTestDao.isMemberByType(data.getRoomId(), WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
                    continue;
                }
                int onlineNum = LAST_ONLINE.getOrDefault(data.getRoomId(), 0);
                if (nowTime > data.getStartTime() && onlineNum <= 0) {
                    continue;
                }
                ActorData actorData = actorDao.getActorDataFromCache(data.getCreator());
                if (actorData == null) {
                    continue;
                }
                RoomEventVO vo = new RoomEventVO();
                vo.setEventId(data.getId());
                vo.setRoomId(data.getRoomId());
                vo.setName(data.getName());
                vo.setRidData(actorData.getRidData());
//                vo.setFamilyId(allMemberMap.getOrDefault(RoomUtils.getRoomHostId(data.getRoomId()), 0));
                vo.setFamilyId(0);
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(data.getRoomId());
                vo.setRoomName(roomData != null ? roomData.getName() : "");
                vo.setRoomHead(roomData != null ? ImageUrlGenerator.generateNormalUrl(roomData.getHead()) : "");
                vo.setType(data.getType());
                vo.setDescription(data.getDescription());
                JSONObject eventType = getEventType(data.getType());
                vo.setTypeIcon(eventType != null ? eventType.getString("icon") : "");
                vo.setTypeName(eventType != null ? eventType.getString("nameEn") : "");
                vo.setTypeNameAr(eventType != null ? eventType.getString("nameAr") : "");
                vo.setStartTime(data.getStartTime());
                vo.setEndTime(data.getEndTime());
                vo.setEventCoverUrl(ImageUrlGenerator.generateUrl(data.getEventCoverUrl(), 350, 200));
                vo.setOnline(onlineNum);

                PartyListData partyData = ALL_PARTY_DATA.get(data.getRoomId());
                if (partyData != null) {
                    vo.setVisitorNum(partyData.getVisitorNum());
                } else {
                    vo.setVisitorNum(roomVisitorsRedis.getRoomVisitorNum(data.getRoomId()));
                }
                if (data.getStartTime() > nowTime) {
                    vo.setWeight(MAX_SPACE - indexCount);
                    vo.setStatus(2);// 即将开始
                    comingSoonList.add(vo);
                    comingSoonAndPartyList.add(vo);
                } else {
                    vo.setWeight(MAX_SPACE + vo.getOnline());
                    vo.setStatus(1); // 正在进行
                    if (partyIndexCount < 10) {
                        partyingList.add(vo);
                    }
                    partyIndexCount++;
                    comingSoonAndPartyList.add(vo);
                }
                if (indexCount >= 20) {
                    break;
                }
                indexCount++;
            }
        }
        roomListRedis.saveEventList(RoomListConstant.EVENT_PARTYING, partyingList);
        roomListRedis.saveEventList(RoomListConstant.EVENT_COMING_SOON, comingSoonList);

//        List<RoomEventVO> allList = new ArrayList<>();
//        allList.addAll(partyingList);
//        allList.addAll(comingSoonList);
//        Map<Integer, List<RoomEventVO>> familyIdGroupMap = allList.stream().collect(Collectors.groupingBy(RoomEventVO::getFamilyId));
//        familyIdGroupMap.forEach((familyId, eventList) -> {
//            if (0 != familyId) {
//                roomListRedis.saveFamilyEventList(familyId, eventList);
//            }
//        });

        List<RoomEventVO> top20 = comingSoonAndPartyList.stream()
                .sorted(Comparator.comparing(RoomEventVO::getWeight).reversed())
                .limit(20)
                .collect(Collectors.toList());
        roomListRedis.saveEventList(RoomListConstant.EVENT_COMING_SOON_AND_PARTYING, top20);
    }

    private JSONObject getEventType(int eventType) {
        try {
            String strValue = backstageConfigDao.getConfigData(BackStageConfigConstant.ROOM_EVENT_CONFIG);
            JSONObject jsonObject = JSONObject.parseObject(strValue);
            if (jsonObject == null) {
                return null;
            }
            JSONObject eventTypeObject = jsonObject.getJSONObject("eventType");
            if (eventTypeObject == null) {
                return null;
            }
            return eventTypeObject.getJSONObject(eventType + "");
        } catch (Exception e) {
            logger.error("getEventType error. {}", e.getMessage(), e);
        }
        return null;
    }

    private void saveGameListToRedis(List<GameListVO> gameList, int gameType) {
        gameList.sort(Comparator.comparing(GameListVO::getGameWeight).reversed());
        roomListRedis.saveGameList(gameList, gameType);
    }

    /**
     * 生成列表
     */
    private void doMakePartyList(List<PartyListData> allList, boolean isRefreshSaWeight) {
        long startTime = System.currentTimeMillis();
        // popular列表
        makePopularList(allList, isRefreshSaWeight);
        long popularTime = System.currentTimeMillis();
        // country列表
        int countyCount = makeCountryList(allList);
        long countryTime = System.currentTimeMillis();
        // forYou列表
//        int forYouCount = makeForYouList(allList);
        // all-new 老用户
        int allNewCount = makeAllNewOldUserList(allList);
        long allNewTime = System.currentTimeMillis();
        // // 社交主页列表 all-new 新用户
        // int socialCount = doMakeSocialHomeRoomList(allList);
        // 社交主页列表 all-Recommend 推荐给你
        int recommendCount = doMakeAllRecommendRoomList(allList);
        long endTime = System.currentTimeMillis();


        logger.info("makePartyList totalCost={} popularCost={} countryCost={} " +
                        "allNewCost={} socialCost={} roomCount={} countryCount={} allNewCount={} recommendCount={}",
                endTime - startTime, popularTime - startTime, countryTime - popularTime,
                allNewTime - countryTime, endTime - allNewTime, allList.size(), countyCount, allNewCount, recommendCount);
    }

    /**
     * country列表
     */
    private int makeCountryList(List<PartyListData> allList) {
        Map<String, List<PartyListData>> countryCodeMap = allList.stream()
                .filter(p -> !StringUtils.isEmpty(p.getCountryCode()) && p.getPwd() != 1)
                .collect(Collectors.groupingBy(PartyListData::getCountryCode));
        for (String countryCode : countryCodeMap.keySet()) {
            List<PartyListData> countryListData = countryCodeMap.get(countryCode);
            countryListData.sort(Comparator.comparing(PartyListData::getOnline).reversed());
            roomListRedis.saveCountryList(countryListData, countryCode);
        }
        return countryCodeMap.size();
    }

    /**
     * <a href="https://www.tapd.cn/20792731/prong/stories/view/1120792731001004387">forYou</a>
     * 1、房间挂机行为：房间所有麦位每分钟上报一次是否有推流，连续5分钟无推流的房间不推荐，恢复推流进入推荐池。挂机维度本版本客户端可提前预埋，下一迭代增加该维度。
     * 2、上锁的房间不进入推荐池。解锁后根据推荐维度计算推荐排序
     * 3、所有麦位无人或者锁全麦的房间不进入推荐池。
     * 4、推荐公式：【新创建房间】* K1 +【国家/地区】* K2 +【邀请新用户上麦】* K3 +【新用户房间停留】* K4 +【闲置麦位】* K5 -【踢出房间】* D1 - 【房间在线人数】*D2
     * 5、列表排序：以总分值降序排列，每30秒更新一次。
     * 6、推荐列表房间数默认为30个房间。支持后台动态调整。
     * 7、房间低于20个时，可推荐平台20人以上的房间。
     */
    private int makeForYouList(List<PartyListData> allList) {
        // forYou列表15*2秒执行一次
        if (!TICK_DATA.addAndCheck(2) || allList.isEmpty()) {
            return -1;
        }
        JSONObject scoreWeight = roomListRedis.getForYouScoreWeight();
        List<PartyListData> forYouList = getForYouList(allList);
        Set<String> recentlyActionRoom = roomActionRedis.getRecentlyActionRoom();
        for (PartyListData partyListData : forYouList) {
            int weight = 0;
            weight += getCreateRoomWeight(partyListData.getRoomData().getCtime(), scoreWeight);
            weight += getOnlineUsersWeight(partyListData.getRealOnline(), scoreWeight);
            weight += getFreeMicWeight(partyListData, scoreWeight);
            if (recentlyActionRoom.contains(partyListData.getRoomId())) {
                weight += getInviteMicWeight(partyListData.getRoomId(), scoreWeight);
                weight += getStayRoomWeight(partyListData.getRoomId(), scoreWeight);
                weight += getRoomKickWeight(partyListData.getRoomId(), scoreWeight);
            }
            partyListData.setForYouWeight(weight);
        }
        List<PartyListData> normalList = forYouList.stream()
                .sorted(Comparator.comparing(PartyListData::getForYouWeight).reversed())
                .limit(RoomListConstant.FOR_YOU_SIZE).collect(Collectors.toList());
        roomListRedis.saveForYouList(normalList);
        return normalList.size();
    }

    private double getWeight(String key, JSONObject weight, double defaultValue) {
        try {
            if (null == weight) {
                return defaultValue;
            }
            Object value = weight.getOrDefault(key, defaultValue);
            if (value instanceof BigDecimal) {
                return ((BigDecimal) value).doubleValue();
            } else if (value instanceof Double) {
                return (double) value;
            } else if (value instanceof String) {
                return Double.parseDouble((String) value);
            } else if (value instanceof Integer) {
                return (double) value;
            }
            return defaultValue;
        } catch (Exception e) {
            logger.error("get weight error. key={}", key, e);
            return defaultValue;
        }
    }

    /**
     * 【降权维度】踢出房间占比80%
     * 踢0人：0
     * 踢1人：20
     * 踢2人：50
     * 踢3人：80
     * 踢4人及以上：100
     */
    private int getRoomKickWeight(String roomId, JSONObject scoreWeight) {
        int weight = 0;
        int kickCount = roomActionRedis.getKickCount(roomId);
        if (kickCount == 1) {
            weight = 20;
        } else if (kickCount == 2) {
            weight = 50;
        } else if (kickCount == 3) {
            weight = 80;
        } else if (kickCount >= 4) {
            weight = 100;
        }
        return (int) (-weight * getWeight("d1", scoreWeight, 0.8));
    }

    /**
     * 【降权维度】踢出房间占比80%
     * 踢0人：0
     * 踢1人：20
     * 踢2人：50
     * 踢3人：80
     * 踢4人及以上：100
     */
    private int getRoomKickWeightByKickCount(int kickCount, JSONObject scoreWeight) {
        int weight = 0;
        if (kickCount == 1) {
            weight = 20;
        } else if (kickCount == 2) {
            weight = 50;
        } else if (kickCount == 3) {
            weight = 80;
        } else if (kickCount >= 4) {
            weight = 100;
        }
        return (int) (-weight * getWeight("d1", scoreWeight, 1000));
    }

    /**
     * 【降权维度】房间在线人数占比40%
     * 3-5人：0
     * 6-8人：10
     * 9-12人：40
     * 13-16人：60
     * 17-20人：80
     * 20人以上：100
     */
    private int getOnlineUsersWeight(int realOnline, JSONObject scoreWeight) {
        int weight = 0;
        if (realOnline >= 6 && realOnline <= 8) {
            weight = 10;
        } else if (realOnline >= 9 && realOnline <= 12) {
            weight = 40;
        } else if (realOnline >= 13 && realOnline <= 16) {
            weight = 60;
        } else if (realOnline >= 17 && realOnline <= 20) {
            weight = 80;
        } else if (realOnline > 20) {
            weight = 100;
        }
        return (int) (-weight * getWeight("d2", scoreWeight, 0.4));
    }


    /**
     * 【降权维度】房间在线新用户人数占比100%
     * 3-5人：0
     * 6-8人：20
     * 9-11人：50
     * 12-15人：80
     * 16人以上：100
     */
    private int getOnlineNewUsersWeight(int realOnline, JSONObject scoreWeight) {
        int weight = 0;
        if (realOnline >= 6 && realOnline <= 8) {
            weight = 20;
        } else if (realOnline >= 9 && realOnline <= 11) {
            weight = 50;
        } else if (realOnline >= 12 && realOnline <= 15) {
            weight = 80;
        } else if (realOnline >= 16) {
            weight = 100;
        }
        return (int) (-weight * getWeight("d2", scoreWeight, 1));
    }

    /**
     * 新用户房间停留3分钟以上30%
     * 1-2人：10
     * 3-4人：30
     * 5-6人：50
     * 7-8人：70
     * 大于等于9人以上：100
     */
    private int getStayRoomWeight(String roomId, JSONObject scoreWeight) {
        int weight = 0;
        int stayRoomCount = roomActionRedis.getStayRoomCount(roomId);
        if (stayRoomCount >= 1 && stayRoomCount <= 2) {
            weight = 10;
        } else if (stayRoomCount >= 3 && stayRoomCount <= 4) {
            weight = 30;
        } else if (stayRoomCount >= 5 && stayRoomCount <= 6) {
            weight = 50;
        } else if (stayRoomCount >= 7 && stayRoomCount <= 8) {
            weight = 70;
        } else if (stayRoomCount >= 9) {
            weight = 100;
        }
        return (int) (weight * getWeight("k4", scoreWeight, 0.3));
    }

    private int getStayRoomWeightByStayCount(int stayRoomCount, JSONObject scoreWeight) {
        int weight = 0;
        if (stayRoomCount >= 1 && stayRoomCount <= 2) {
            weight = 10;
        } else if (stayRoomCount >= 3 && stayRoomCount <= 4) {
            weight = 30;
        } else if (stayRoomCount >= 5 && stayRoomCount <= 6) {
            weight = 50;
        } else if (stayRoomCount >= 7 && stayRoomCount <= 8) {
            weight = 70;
        } else if (stayRoomCount >= 9) {
            weight = 100;
        }
        return (int) (weight * getWeight("k4", scoreWeight, 0.3));
    }

    /**
     * 邀请新用户上麦50%
     * 最近10分钟无邀请：0
     * 最近10分钟邀请1个用户：40
     * 最近10分钟邀请2个用户：60
     * 最近10分钟邀请3个用户：80
     * 最近10分钟邀请4个及以上用户：100
     */
    private int getInviteMicWeight(String roomId, JSONObject scoreWeight) {
        int weight = 0;
        int inviteMicCount = roomActionRedis.getInviteMicCount(roomId);
        if (inviteMicCount == 1) {
            weight = 40;
        } else if (inviteMicCount == 2) {
            weight = 60;
        } else if (inviteMicCount == 3) {
            weight = 80;
        } else if (inviteMicCount >= 4) {
            weight = 100;
        }
        return (int) (weight * getWeight("k3", scoreWeight, 0.5));
    }

    private int getInviteMicWeightByInviteCount(int inviteMicCount, JSONObject scoreWeight) {
        int weight = 0;
        if (inviteMicCount == 1) {
            weight = 40;
        } else if (inviteMicCount == 2) {
            weight = 60;
        } else if (inviteMicCount == 3) {
            weight = 80;
        } else if (inviteMicCount >= 4) {
            weight = 100;
        }
        return (int) (weight * getWeight("k3", scoreWeight, 0.5));
    }

    /**
     * 闲置麦位占比30%
     * 1-2个可上麦位：20
     * 3-4个可上麦位：50
     * 5-6个可上麦位：70
     * 7个可上麦位：90
     * 8个可上麦位：0
     */
    private int getFreeMicWeight(PartyListData partyListData, JSONObject scoreWeight) {
        int freeMic;
        int micCount = partyListData.getMicCount();
        if (RoomType.COMP == partyListData.getRoomData().getCtime()) {
            freeMic = 3 - micCount;
        } else {
            freeMic = 8 - micCount;
        }
        int weight = 0;
        if (freeMic >= 1 && freeMic <= 2) {
            weight = 20;
        } else if (freeMic >= 3 && freeMic <= 4) {
            weight = 50;
        } else if (freeMic >= 5 && freeMic <= 6) {
            weight = 70;
        } else if (freeMic == 7) {
            weight = 90;
        }
        return (int) (weight * getWeight("k5", scoreWeight, 0.3));
    }

    /**
     * 闲置麦位占比30%
     * 1-2个可上麦位：20
     * 3-4个可上麦位：50
     * 5-6个可上麦位：80
     * 7个可上麦位：100
     * 8个可上麦位：0
     */
    private int getRookieRoomFreeMicWeight(int freeMic, JSONObject scoreWeight) {
//        int freeMic;
//        int micCount = partyListData.getMicCount();
//        if (RoomType.COMP == partyListData.getRoomData().getCtime()) {
//            freeMic = 3 - micCount;
//        } else {
//            freeMic = 8 - micCount;
//        }
        int weight = 0;
        if (freeMic >= 1 && freeMic <= 2) {
            weight = 20;
        } else if (freeMic >= 3 && freeMic <= 4) {
            weight = 50;
        } else if (freeMic >= 5 && freeMic <= 6) {
            weight = 80;
        } else if (freeMic == 7) {
            weight = 100;
        }
        return (int) (weight * getWeight("k5", scoreWeight, 0.3));
    }

    /**
     * 新创建房间占比15%
     * >7days：0
     * ≤7days:40
     * ≤5-6days:60
     * ≤3-4days:80
     * ≤1-2days:100
     */
    private int getCreateRoomWeight(int ctime, JSONObject scoreWeight) {
        int weight = 0;
        int duration = DateHelper.getNowSeconds() - ctime;
        if (duration <= TimeUnit.DAYS.toSeconds(2)) {
            weight = 100;
        } else if (duration <= TimeUnit.DAYS.toSeconds(4)) {
            weight = 80;
        } else if (duration <= TimeUnit.DAYS.toSeconds(6)) {
            weight = 60;
        } else if (duration <= TimeUnit.DAYS.toSeconds(7)) {
            weight = 40;
        }
        return (int) (weight * getWeight("k1", scoreWeight, 0.15));
    }


    /**
     * 同国家
     * 100
     */
    public int getSameCountryWeight(JSONObject scoreWeight) {
        int weight = 100;
        return (int) (weight * getWeight("k21", scoreWeight, 10));
    }

    /**
     * 同地区
     * 100
     */
    public int getSameAreaWeight(JSONObject scoreWeight) {
        int weight = 100;
        return (int) (weight * getWeight("k22", scoreWeight, 1));
    }

    private List<PartyListData> getForYouList(List<PartyListData> allList) {
        // 房间人数需要在[4,20]之间，房间未上锁
        List<PartyListData> forYouList = allList.stream()
                .filter(p -> p.getPwd() == 0 && (p.getRealOnline() <= 20 && p.getRealOnline() >= 4))
                .collect(Collectors.toList());
        // 房间不足时增加10个
        if (forYouList.size() < RoomListConstant.FOR_YOU_SIZE) {
            forYouList.addAll(allList.stream().filter(p -> p.getPwd() == 0 && p.getRealOnline() > 20)
                    .sorted(Comparator.comparing(PartyListData::getRealOnline)).limit(10)
                    .collect(Collectors.toList()));
        }
        // 过滤无人在麦
        forYouList.removeIf(forYou -> forYou.getMicCount() == 0);
        return forYouList;
    }

    /**
     * TOP4以外的房间排列规则：
     * -优先展示用户所在地区的房间（房间人数需大于20个人），再展示对立地区的房间+本地区的房间。
     * 例如：GCC国家用户，优先展示GCC国家的房间，非GCC用户，优先展示非GCC国家的房间。
     * -各区域的房间人数少于20个人时，展示另一地区的房间。
     * 例如：GCC国家房间人数大于等于20个人的房间排完了之后，再把中东国家的房间和GCC低于20个人的房间排在后面。2个区域的房间都按房间人数和房间贡献值降序排列。
     */
    private void makePopularList(List<PartyListData> allList, boolean isRefreshSaWeight) {
        List<PartyListData> top4 = allList.stream()
                .sorted(Comparator.comparing(PartyListData::getWeight).reversed())
//                .filter(item -> item.getWeight() > Integer.MAX_VALUE - MAX_SPACE)
                .limit(4)
                .collect(Collectors.toList());
        savePopularList(allList, top4, RoomListConstant.MAJOR_COUNTRY);
        savePopularList(allList, top4, RoomListConstant.OTHER_COUNTRY);
//        sortCommonPopularList(allList, top4);
//        PopularRecommendConfigData config = getPopularRecommendConfigData();
//        if (config.getIsOpenSaWeight() > 0) {
        if (isRefreshSaWeight) {
            saveSaPopularList(allList, "sa", 900);
            saveSaPopularList(allList, "iq", 900);
        }
//        }
    }

    private void savePopularList(List<PartyListData> allList, List<PartyListData> top4, int area) {
        List<PartyListData> result;
        if (!CollectionUtils.isEmpty(top4)) {
            // 优先加置顶的房间
            result = new ArrayList<>(top4);
        } else {
            result = new ArrayList<>();
        }
//        Comparator<PartyListData> areaComp = Comparator.comparing(o -> {
//            // https://www.tapd.cn/20792731/prong/stories/view/1120792731001015963
//            // 按照区域分, 分钟内房间人气*5% + 分钟内房间上麦人数*35% + 分钟内产出的礼物总价值*50% + 分钟内公屏聊天人数*15%
//            if (o.getArea() == area && o.getPwd() == 0) {
//                return Integer.MAX_VALUE - MAX_SPACE
//                        + o.getWeight() * 10 * 0.05
//                        + o.getMicCount() * 200 * 0.35
//                        + o.getDevote() / 200 * 0.5
//                        + o.getMsgCount() * 100 * 0.15;
//            }
//            return o.getWeight();
//        });
        PopularRecommendConfigData config = getPopularRecommendConfigData();
        // 迎新房（有配置的情况下）——> 房间热度分数TOP8房间（区分海湾国家用户和非海湾国家用户）——> 平台所有房间综合热度分数降序
        List<PartyListData> top8Rooms = allList.stream()
                .filter(o -> o.getArea() == area)
                .sorted(getComparator(area, config).reversed())
                .limit(8)
                .collect(Collectors.toList());
        for (PartyListData vo : top8Rooms) {
            if (!result.contains(vo)) {
                result.add(vo);
            }
        }
        allList.sort(getComparator(-1, config).reversed());
        for (PartyListData vo : allList) {
            if (!result.contains(vo)) {
                result.add(vo);
            }
        }
        doSavePopularList(result, area);
        result.clear();
    }

    private void doSavePopularList(List<PartyListData> result, int area) {
        List<PartyListData> pageList = new ArrayList<>(RoomListConstant.POPULAR_PAGE_SIZE);
        int page = 1;
        for (PartyListData vo : result) {
            pageList.add(vo);
            if (pageList.size() == RoomListConstant.POPULAR_PAGE_SIZE) {
                roomListRedis.savePopularList(pageList, area, page);
                pageList.clear();
                page += 1;
            }
        }
        roomListRedis.savePopularList(pageList, area, page);
        // 解决分页缓存残留问题
        int lastPageCount = roomListRedis.getPopularListPageCount(area);
        if (page < lastPageCount) {
            for (int i = page + 1; i <= lastPageCount; i++) {
                roomListRedis.deletePopularList(area, page);
            }
        }
        roomListRedis.setPopularListPageCount(area, page);
    }

    private void doSaveSaPopularList(List<PartyListData> result, int area) {
        List<PartyListData> pageList = new ArrayList<>(RoomListConstant.POPULAR_PAGE_SIZE);
        int page = 1;
        for (PartyListData vo : result) {
            pageList.add(vo);
            if (pageList.size() == RoomListConstant.POPULAR_PAGE_SIZE) {
                roomListRedis.savePopularList(pageList, area, page);
                pageList.clear();
                page += 1;
            }
        }
        roomListRedis.savePopularList(pageList, area, page);
        // 解决分页缓存残留问题
        int lastPageCount = roomListRedis.getPopularListPageCount(area);
        if (page < lastPageCount) {
            for (int i = page + 1; i <= lastPageCount; i++) {
                roomListRedis.deletePopularList(area, page);
            }
        }
        roomListRedis.setPopularListPageCount(area, page);
    }

    private void saveSaPopularList(List<PartyListData> allList, String countryCode, int limit) {
        int num = 0;
        List<PartyListData> result = new ArrayList<>();

        List<PartyListData> topList = allList.stream()
                .sorted(Comparator.comparing(PartyListData::getSaWeight).reversed())
                .filter(item -> item.getSaWeight() > Integer.MAX_VALUE - MAX_SPACE)
                .collect(Collectors.toList());

        // 排序置顶房间
        for (PartyListData vo : topList) {
            result.add(vo);
        }

        int myGccCode = RoomListConstant.GCC_SET.contains(countryCode) ?
                RoomListConstant.GCC_COUNTRY_AREA : RoomListConstant.UN_GCC_COUNTRY_AREA;
        Comparator<PartyListData> areaComp = Comparator.comparing(o -> {
            int roomGccCode = RoomListConstant.GCC_SET.contains(o.getCountryCode()) ?
                    RoomListConstant.GCC_COUNTRY_AREA : RoomListConstant.UN_GCC_COUNTRY_AREA;
            if (myGccCode == roomGccCode && o.getPwd() == 0) {
                return ArithmeticUtils.add(Integer.MAX_VALUE - MAX_SPACE, o.getSaWeight());
            }
            return o.getSaWeight();
        });

        if (myGccCode == RoomListConstant.GCC_COUNTRY_AREA) {
            List<PartyListData> topAreaList = allList.stream()
                    .filter(item -> item.getSaWeight() < Integer.MAX_VALUE - MAX_SPACE)
                    .sorted(areaComp.reversed())
                    .limit(30)
                    .collect(Collectors.toList());


            // 排序同地区房间，只取30个
            for (PartyListData vo : topAreaList) {
                result.add(vo);
            }

            // 置顶房间（有配置的情况下）——> 同地区的房间（沙特为TOP30）——> 平台所有房间综合热度分数降序
            allList.sort(Comparator.comparing(PartyListData::getSaWeight).reversed());
            for (PartyListData vo : allList) {
                if (!result.contains(vo)) {
                    result.add(vo);
                    num++;
                    if (num >= limit) {
                        break;
                    }
                }
            }

        } else {
            List<PartyListData> topAreaList = allList.stream()
                    .filter(item -> item.getSaWeight() < Integer.MAX_VALUE - MAX_SPACE)
                    .sorted(areaComp.reversed())
                    .collect(Collectors.toList());

            // 置顶房间（有配置的情况下）——> 同地区的房间（沙特为TOP30）——> 平台所有房间综合热度分数降序
            for (PartyListData vo : topAreaList) {
                result.add(vo);
                num++;
                if (num >= limit) {
                    break;
                }
            }
        }

        if (ServerConfig.isNotProduct()) {
            for (PartyListData vo : result) {
                logger.info("roomId:{} roomName:{} saWeight:{} countryCode:{} myGccCode:{}",
                        vo.getRoomId(), vo.getRoom_name(), vo.getSaWeight(), countryCode, myGccCode);
            }
        }
        doSaveSaPopularList(result, myGccCode);
        result.clear();
    }

    public PartyData fillPartyList() {
        long startTime = System.currentTimeMillis();
        PartyData partyData = new PartyData();
        Map<String, Integer> allTopRooms = topRoomRedis.getAllTopRooms();
        Set<String> allAreaBlackUser = areaBlackUserRedis.getAllAreaBlackUser();
        Map<String, String> allRoomFrames = roomFrameRedis.getAllRoomFrames();
        Set<String> roomSet = roomRedis.getRoomSet();
        List<TurntableGameInfo> allTurntableGame = turntableGameRedis.getAllTurntableGame();
        List<TruthOrDareInfo> allTruthDareGame = truthOrDareRedis.getAllTurntableGame();
        List<TruthOrDareV2Info> allTruthDareV2Game = truthOrDareV2Redis.getAllTruthDareV2Game();
        Map<String, TruthOrDareInfo> truthDareRoomMap = CollectionUtil.listToKeyMap(allTruthDareGame, TruthOrDareInfo::getRoom_id);
        Map<String, TruthOrDareV2Info> truthDareV2RoomMap = CollectionUtil.listToKeyMap(allTruthDareV2Game, TruthOrDareV2Info::getRoomId);
        Map<String, SudGameInfo> sudGameRoomMap = recreationTagService.getAllSudGameInfo();
        Map<String, TurntableGameInfo> turntableRoomMap = CollectionUtil.listToKeyMap(allTurntableGame, TurntableGameInfo::getRoomId);
        Set<String> roomEventActivitySet = recreationTagService.getAllCommonZSetValue();
        Map<String, Integer> roomMicCount = roomMicRedis.getMicActorCountMap();
        Map<String, Long> roomDevoteMap = giftRecordDao.getRoomDevoteMap(startTime - 60 * 1000);

        Map<String, Long> roomMsgCountMap = roomMessageDao.getRoomMsgCountMap((int) (startTime / 1000) - 60);
        if (recentlyActionRoom != null) {
            recentlyActionRoom.clear();
        }
        recentlyActionRoom = roomActionRedis.getRecentlyActionRoom();
        PopularRecommendConfigData config = getPopularRecommendConfigData();
        Map<String, Long> roomDevote10Map = Collections.emptyMap();
        boolean isRefreshSaWeight = false;
        if (TICK_DATA.roomListCheck(20)) {
            roomDevote10Map = giftRecordDao.getRoomDevoteMap(startTime - 10 * 60 * 1000);
            isRefreshSaWeight = true;
        }

        int voiceOnlineActor = 0;
        int liveOnlineActor = 0;
        int liveRoomCount = 0;
        int voiceRoomCount = 0;
        int videoRoomCount = 0;
        GAME_TYPE_MAP_COUNT.clear();
        for (String roomId : roomSet) {
            String hostId = RoomUtils.getRoomHostId(roomId);
            boolean isGameRoom = RoomUtils.isGameRoom(roomId);
            if (NOT_SHOW_IN_POPULAR_LIST.contains(roomId) || allAreaBlackUser.contains(hostId)) {
                continue;
            }
            // 房间用户数量
            int actorsCount = roomPlayerRedis.getRoomActorsCount(roomId);
            if (0 == actorsCount) {
                continue;
            }
            // 置顶房间如果麦上没人则不展示在首页
            if (allTopRooms.containsKey(roomId)) {
                if (roomMicCount.getOrDefault(roomId, 0) == 0) {
                    logger.info("nobody in mic skip top room roomId={}", roomId);
                    continue;
                }
            }
            MongoRoomData roomData = mongoRoomDao.findData(roomId);
            if (null == roomData) {
                continue;
            }

            if (whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
                continue;
            }
            if (!isGameRoom) {
                // 维护房间在线人数
                if (RoomType.COMP == roomData.getComp() || RoomConstant.LIVE_ROOM_MODE == roomData.getRoomMode()) {
                    liveOnlineActor = liveOnlineActor + actorsCount;
                    liveRoomCount++;
                } else {
                    voiceOnlineActor = voiceOnlineActor + actorsCount;
                    voiceRoomCount++;
                }
            }
            PartyListData vo = new PartyListData();
            vo.setRoomData(roomData);
            vo.setAid(hostId);
            vo.copyFromMongoRoomData(roomData);
            // 在线人数
            vo.setRealOnline(actorsCount);
            vo.setOnline(getFixOnline(actorsCount));
            vo.setVisitorNum(roomVisitorsRedis.getRoomVisitorNum(roomId));
            // 房间边框
            vo.setRoom_frame(allRoomFrames.get(roomId));
            // 房主vip
            vo.setViplevel(vipInfoDao.getIntVipLevelFromCache(hostId));
            vo.setMicCount(roomMicCount.getOrDefault(roomId, 0));
            // 设置权重值，用于排序
            vo.setWeight(getPopularRoomWeight(roomData, allTopRooms, actorsCount, vo.getMicCount(), config));
            // 设置沙特，伊拉克单独权重值，用于排序
            double saWeight = Optional.ofNullable(ALL_PARTY_DATA.get(roomId)).map(PartyListData::getSaWeight).orElse(0d);
            if (isRefreshSaWeight) {
                vo.setSaWeight(getPopularRoomSaWeight(roomData, allTopRooms, actorsCount,
                        saWeight, roomDevote10Map.getOrDefault(roomId, 0L)));
                partyData.setRefreshSaWeight(true);
            } else {
                vo.setSaWeight(saWeight);
                partyData.setRefreshSaWeight(false);
            }

            String countryCode = ActorUtils.getCountryCode(roomData.getCountry());
            vo.setCountryCode(countryCode);
            vo.setArea(RoomListConstant.MAJOR_COUNTRY_SET.contains(countryCode) ? RoomListConstant.MAJOR_COUNTRY : RoomListConstant.OTHER_COUNTRY);
            vo.setDevote(roomDevoteMap.getOrDefault(roomId, 0L));
            vo.setMsgCount(roomMsgCountMap.getOrDefault(roomId, 0L));
            // 房间状态
            boolean watchRoom = VideoActionType.OPEN == roomData.getVideo_switch();
            if (watchRoom) {
                videoRoomCount++;
                GAME_TYPE_MAP_COUNT.put(1051, actorsCount + GAME_TYPE_MAP_COUNT.getOrDefault(1051, 0));
            }
            if (!isGameRoom) {
                if (newRookieRoomRedis.isHangUpRoom(roomId)) {
                    vo.setIsHangUpRoom(1);
                }
                if (newRookieRoomRedis.isKickOutRoomRecord(roomId, 4)) {
                    vo.setIsKickOutRoom(1);
                }
                SudGameInfo sudGameInfo = sudGameRoomMap.get(roomData.getRid());
                if (sudGameInfo != null) {
                    // gameType目前取值10002-10006
//                    int voiceGameType = 10000 + sudGameInfo.getGameType();
//                    GAME_TYPE_MAP_COUNT.put(voiceGameType,
//                            actorsCount + GAME_TYPE_MAP_COUNT.getOrDefault(voiceGameType, 0));
                }
                boolean isTurntableRoom = turntableRoomMap.containsKey(roomData.getRid());
                vo.setRoomStatus(getRoomStatus(isTurntableRoom, watchRoom, RoomType.COMP == roomData.getComp()));
                vo.setRecreationTag(recreationTagService.getRecreationTag(roomData, turntableRoomMap, sudGameRoomMap, truthDareRoomMap, roomEventActivitySet, truthDareV2RoomMap));
                vo.setIsOfficial(OfficialRoom.ifOfficialRoom(roomData.getRid()) ? 1 : 0);
                fillGameList(partyData, vo, turntableRoomMap.get(roomId), sudGameRoomMap.get(roomId), truthDareRoomMap.get(roomId), truthDareV2RoomMap.get(roomId));
                if (recentlyActionRoom.contains(roomId)) {
                    int kickCount = roomActionRedis.getKickCount(roomId);
                    int inviteMicCount = roomActionRedis.getInviteMicCount(roomId);
                    int stayRoomCount = roomActionRedis.getStayRoomCount(roomId);
                    vo.setKickCount(kickCount);
                    vo.setInviteMicCount(inviteMicCount);
                    vo.setStayRoomCount(stayRoomCount);
                }
                partyData.getAllList().add(vo);
            } else {
                GAME_PARTY_DATA.put(roomId, vo);
                SudGameInfo sudGameInfo = sudGameRoomMap.get(roomData.getRid());
                if (sudGameInfo != null) {
                    // gameType目前取值2-6
                    GAME_TYPE_MAP_COUNT.put(sudGameInfo.getGameType(),
                            actorsCount + GAME_TYPE_MAP_COUNT.getOrDefault(sudGameInfo.getGameType(), 0));
                }

            }
            ALL_PARTY_DATA.put(roomId, vo);

        }
        GAME_TYPE_MAP_COUNT.put(1050, voiceOnlineActor);

        // 以下数据提供给其他模块使用
        Map<String, String> map = new HashMap<>();
        map.put("voiceOnlineActor", String.valueOf(voiceOnlineActor));
        map.put("liveOnlineActor", String.valueOf(liveOnlineActor));
        map.put("liveRoomCount", String.valueOf(liveRoomCount));
        map.put("voiceRoomCount", String.valueOf(voiceRoomCount));
        map.put("ludoRoomCount", String.valueOf((int) sudGameRoomMap.values().stream().filter(g -> g.getGameType() == 2).count()));
        map.put("videoRoomCount", String.valueOf(videoRoomCount));
        map.put("turntableRoomCount", String.valueOf(turntableRoomMap.size()));
        roomRedis.saveRoomStatData(map);
        ALL_PARTY_DATA.forEach((k, v) -> {
            if (!roomSet.contains(k)) {
                ALL_PARTY_DATA.remove(k);
            }
        });
        LAST_ONLINE.forEach((k, v) -> {
            if (!roomSet.contains(k)) {
                LAST_ONLINE.remove(k);
            }
        });

        GAME_PARTY_DATA.forEach((k, v) -> {
            if (!roomSet.contains(k)) {
                GAME_PARTY_DATA.remove(k);
                gameRoomRedis.removeGameType(k);
            }
        });

        GAME_TYPE_MAP_COUNT.forEach((k, v) -> {
            gameRoomRedis.updateGameOnline(String.valueOf(k), String.valueOf(v));
        });
        roomDevoteMap.clear();
        roomDevote10Map.clear();
        roomMsgCountMap.clear();
        logger.info("fillPartyList cost={} listSize={}", System.currentTimeMillis() - startTime, partyData.getAllList().size());
        return partyData;
    }

    private int getRecreationTag(MongoRoomData roomData, Map<String, TurntableGameInfo> turntableRoomMap,
                                 Map<String, SudGameInfo> sudGameRoomMap, Map<String, TruthOrDareInfo> truthDareRoomMap) {
        if (RoomConstant.LIVE_ROOM_MODE == roomData.getRoomMode()) {
            // 只要有一个麦位开启摄像头就显示LIVE标签
            List<RoomMicData> micList = roomMicDao.getDataListFromRedis(roomData.getRid());
            if (null != micList && micList.stream().anyMatch(micData -> !StringUtils.isEmpty(micData.getUid()) && micData.getVideoStatus() == 1)) {
                return RoomListConstant.LIVE_ROOM_MODE_TAG;
            }
        } else if (sudGameRoomMap.containsKey(roomData.getRid())) {
            return getSudGameTag(sudGameRoomMap.get(roomData.getRid()));
        } else if (turntableRoomMap.containsKey(roomData.getRid())) {
            return RoomListConstant.TURNTABLE_TAG;
        } else if (VideoActionType.OPEN == roomData.getVideo_switch()) {
            return RoomListConstant.YOUTUBE_TAG;
        } else if (truthDareRoomMap.containsKey(roomData.getRid())) {
            return RoomListConstant.TRUTH_DARE_TAG;
        }
        return 0;
    }

    private int getSudGameTag(SudGameInfo sudGameInfo) {
        if (sudGameInfo == null) {
            return 0;
        }
        if (sudGameInfo.getGameType() == 1) {
            return RoomListConstant.BUMPER_BLASTER_TAG;
        } else if (sudGameInfo.getGameType() == 2) {
            return RoomListConstant.LUDO_TAG;
        } else if (sudGameInfo.getGameType() > 0) {
            return 9 + sudGameInfo.getGameType();
        }
        return 0;
    }

    private void fillGameList(PartyData partyData, PartyListData vo, TurntableGameInfo turntableGame, SudGameInfo sudGame,
                              TruthOrDareInfo truthDareGame, TruthOrDareV2Info truthDareV2Game) {
        if (vo.getPwd() == 1) {
            return;
        }
        // sud小游戏
        if (null != sudGame && (sudGame.getStatus() == 1 || sudGame.getStatus() == 2)) {
            GameListVO gameListVO = new GameListVO();
            BeanUtils.copyProperties(vo, gameListVO);
            gameListVO.setGame_type(sudGame.getGameType());
            gameListVO.setShow_type(sudGame.getGameType());
            gameListVO.setCurrency_type(sudGame.getCurrencyType());
            gameListVO.setStatus(sudGame.getStatus());
            gameListVO.setFee(sudGame.getCurrency());
            gameListVO.setUser_heads(CollectionUtil.getPropertyList(sudGame.getPlayerList(), SudGamePlayerData::getHead, null));
            if (sudGame.getStatus() == 1) {
                gameListVO.setGameWeight(vo.getPwd() == 1 ? 1 : 35000 + vo.getOnline());
            } else {
                gameListVO.setGameWeight(vo.getPwd() == 1 ? 1 : 30000 + vo.getOnline());
            }
            gameListVO.setRoomLevel(roomLevelService.getRoomLevel(vo.getRoomId()));
            gameListVO.setGameCtime(sudGame.getCreateTime());
            partyData.getGameList().add(gameListVO);
        }
        // 幸运转盘
        if (null != turntableGame) {
            GameListVO gameListVO = new GameListVO();
            BeanUtils.copyProperties(vo, gameListVO);
            gameListVO.setGame_type(1);
            gameListVO.setShow_type(1);
            gameListVO.setStatus(turntableGame.getStatus());
            gameListVO.setFee(turntableGame.getJoinBeans());
            gameListVO.setCurrency_type(2);
            if (!CollectionUtils.isEmpty(turntableGame.getPlayers())) {
                gameListVO.setUser_heads(turntableGame.getPlayers().stream().map(actor -> {
                    ActorData actorData = actorDao.getActorDataFromCache(actor.get_id());
                    if (null == actorData) {
                        return null;
                    }
                    return ImageUrlGenerator.generateMiniUrl(actorData.getHead());
                }).collect(Collectors.toList()));
            } else {
                gameListVO.setUser_heads(Collections.emptyList());
            }
            if (turntableGame.getStatus() == 1) {
                gameListVO.setGameWeight(vo.getPwd() == 1 ? 1 : 25000 + vo.getOnline());
            } else {
                gameListVO.setGameWeight(vo.getPwd() == 1 ? 1 : 20000 + vo.getOnline());
            }
            gameListVO.setRoomLevel(roomLevelService.getRoomLevel(vo.getRoomId()));
            gameListVO.setGameCtime(turntableGame.getCtime());
            partyData.getGameList().add(gameListVO);
        }
        // 真心话大冒险
        if (null != truthDareGame) {
            GameListVO gameListVO = new GameListVO();
            BeanUtils.copyProperties(vo, gameListVO);
            gameListVO.setGame_type(1);
            gameListVO.setShow_type(0);
            gameListVO.setFee(truthDareGame.getBeans());
            gameListVO.setStatus(truthDareGame.getSstatus());
            gameListVO.setUser_heads(truthDareGame.getPlayers().stream().map(actor -> {
                ActorData actorData = actorDao.getActorDataFromCache(actor.getUid());
                if (null == actorData) {
                    return null;
                }
                return ImageUrlGenerator.generateMiniUrl(actorData.getHead());
            }).collect(Collectors.toList()));

            if (truthDareGame.getSstatus() == 1) {
                gameListVO.setGameWeight(vo.getPwd() == 1 ? 1 : 15000 + vo.getOnline());
            } else {
                gameListVO.setGameWeight(vo.getPwd() == 1 ? 1 : 10000 + vo.getOnline());
            }
            gameListVO.setRoomLevel(roomLevelService.getRoomLevel(vo.getRoomId()));
            gameListVO.setGameCtime(truthDareGame.getCtime());
            partyData.getGameList().add(gameListVO);
        }
        // 真心话大冒险V2
        if (null != truthDareV2Game) {
            GameListVO gameListVO = new GameListVO();
            BeanUtils.copyProperties(vo, gameListVO);
            gameListVO.setGame_type(TruthDareV2Constant.GAME_MATCH_TYPE);
            gameListVO.setShow_type(TruthDareV2Constant.GAME_MATCH_TYPE);
            gameListVO.setFee(0);
            gameListVO.setStatus(truthDareV2Game.getStatus());
            RoomMicListVo roomMicVO = roomMicRedis.getRoomMicFromRedis(vo.getRoomId());
            if (roomMicVO != null && !CollectionUtils.isEmpty(roomMicVO.getList())) {
                gameListVO.setUser_heads(roomMicVO.getList().stream().filter(item -> item.getStatus() == 1).map(item -> item.getUser().getHead()).collect(Collectors.toList()));
            }
            if (truthDareV2Game.getStatus() == 1) {
                gameListVO.setGameWeight(vo.getPwd() == 1 ? 1 : 15000 + vo.getOnline());
            } else {
                gameListVO.setGameWeight(vo.getPwd() == 1 ? 1 : 10000 + vo.getOnline());
            }
            gameListVO.setRoomLevel(roomLevelService.getRoomLevel(vo.getRoomId()));
            gameListVO.setGameCtime(new ObjectId(truthDareV2Game.getGameId()).getTimestamp());
            partyData.getGameList().add(gameListVO);
        }

    }

    /**
     * -房间类型分为Party、Live、Game、Watch
     * a. Party：即语音聊天，未开启ludo游戏/幸运转盘/看视频
     * b. Live：直播房（直播房内开启任意功能都显示Live类型）
     * c. Game: 开启Ludo、幸运转盘（同时又开启看视频功能时标记为Game）
     * d. Watch:开启看视频功能（仅开启看视频功能）
     */
    private int getRoomStatus(boolean turntableRoom, boolean watchRoom, boolean liveRoom) {
        if (liveRoom) {
            return RoomListConstant.LIVE_ROOM;
        } else if (turntableRoom) {
            return RoomListConstant.GAME_ROOM;
        } else if (watchRoom) {
            return RoomListConstant.WATCH_ROOM;
        } else {
            return RoomListConstant.PARTY_ROOM;
        }
    }

    private int getPopularRoomWeight(MongoRoomData roomData, Map<String, Integer> allTopRooms, int actorsCount,
                                     int micCount, PopularRecommendConfigData config) {
        // 密码房在最后面
        if (!StringUtils.isEmpty(roomData.getPwd())) {
            return -999999;
        }
        if (allTopRooms.containsKey(roomData.getRid())) {
            return Integer.MAX_VALUE - allTopRooms.get(roomData.getRid());
        }
        //  return actorsCount + devoteWeightRedis.getRoomDevoteWeight(roomData.getRid());
        int deducting = micCount == 0 ? (int) (config.getNoneMicToHot() * config.getNoneMicRate()) : 0;
        return actorsCount + deducting;
    }

    private double getPopularRoomSaWeight(MongoRoomData roomData, Map<String, Integer> allTopRooms, int actorsCount,
                                          double oldWeight, long roomDevote10) {

        // 密码房在最后面
        if (!StringUtils.isEmpty(roomData.getPwd())) {
            return -999999;
        }
        if (allTopRooms.containsKey(roomData.getRid())) {
            return Integer.MAX_VALUE - allTopRooms.get(roomData.getRid());
        }
        // 上一次为密码房或者置顶房间，但本次不是
        oldWeight = oldWeight < 0 || oldWeight > Integer.MAX_VALUE - MAX_SPACE ? 0 : oldWeight;
        double newWeight = ArithmeticUtils.add(
                ArithmeticUtils.add(ArithmeticUtils.div(oldWeight, 2, 3), actorsCount),
                ArithmeticUtils.div(roomDevote10, 1000, 3));
        return newWeight;
    }

    /**
     * 房间列表的人数显示增量规则：
     * 房间内在线人数小于5人，列表人数显示实际人数
     * 房间内在线人数5～7人，列表人数显示+1
     * 房间内在线人数8~10人，列表人数显示+2
     * 房间内在线人数11~15人，列表人数显示+3
     * 房间内在线人数16~25人，列表人数显示+4
     * 房间内在线人数26~49人，列表人数显示+5
     * 房间内在线人数50人以上，列表人数显示+6
     * 房间人数每增长50人，列表人数加6的基础上再+1
     */
    private int getFixOnline(int actorsCount) {
        if (actorsCount < 5) {
            return actorsCount;
        } else if (actorsCount < 7) {
            return actorsCount + 1;
        } else if (actorsCount < 10) {
            return actorsCount + 2;
        } else if (actorsCount < 15) {
            return actorsCount + 3;
        } else if (actorsCount < 25) {
            return actorsCount + 4;
        } else if (actorsCount < 50) {
            return actorsCount + 5;
        } else {
            return actorsCount + 6 + (actorsCount / 50);
        }
    }

    private void sortCommonPopularList(List<PartyListData> allList, List<PartyListData> top4) {
        List<String> nowHotList = new ArrayList<>();
        List<PartyListData> result = new ArrayList<>(top4);
        Comparator<PartyListData> weight = Comparator.comparing(o -> {
            // 1.优先展示房间人数大于15个人
            if (o.getOnline() >= 15 && o.getPwd() == 0) {
                return o.getWeight() + Integer.MAX_VALUE - 10000;
            }
            // 2.剩余房间按照权重值倒序
            return o.getWeight();
        });
        allList.sort(weight.reversed());
        for (PartyListData vo : allList) {
            if (!result.contains(vo)) {
                result.add(vo);
            }
        }
        int len = Math.min(result.size(), RoomHotDevoteRedis.HOT_CHANGE_SIZE);
        for (int i = 0; i < len; i++) {
            nowHotList.add(result.get(i).getRoomId());
        }
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                noticeChange(nowHotList);
            }
        });
    }

    private void noticeChange(List<String> nowHotList) {
        try {
            List<String> noticeRoomList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(nowHotList)) {
                List<String> oldHotList = roomHotDevoteRedis.getHotRankList();
                if (isSameList(nowHotList, oldHotList)) {
                    roomHotDevoteRedis.addExpireHotRankList();
                    logger.info("noticeChange hot list not change return size={}", nowHotList.size());
                    return;
                }
                for (int index = 0; index < nowHotList.size(); index++) {
                    String nowRoomId = nowHotList.get(index);
                    int oldIndex = oldHotList.indexOf(nowRoomId);
                    if (index != oldIndex) {
                        noticeRoomList.add(nowRoomId);
                    }
                }
                for (int index = 0; index < oldHotList.size(); index++) {
                    String nowRoomId = oldHotList.get(index);
                    int nowIndex = nowHotList.indexOf(nowRoomId);
                    if (index != nowIndex && !noticeRoomList.contains(nowRoomId)) {
                        noticeRoomList.add(nowRoomId);
                    }
                }
                if (!CollectionUtils.isEmpty(noticeRoomList)) {
                    int len = nowHotList.size();
                    for (String item : noticeRoomList) {
                        int nowIndex = nowHotList.indexOf(item);
                        int rank = nowIndex == -1 ? len < RoomHotDevoteRedis.HOT_CHANGE_SIZE ? len + 1 : RoomHotDevoteRedis.HOT_CHANGE_MAX_RANK : nowIndex + 1;
                        RoomHotDevoteChangePushMsg msg = new RoomHotDevoteChangePushMsg();
                        msg.setHotRank(String.valueOf(rank));
                        msg.setrType(0);
                        roomWebSender.sendRoomWebMsg(item, null, msg, false);
                    }
                    logger.info("noticeChange all success room size={} ", noticeRoomList.size());
                }
                roomHotDevoteRedis.saveHotRankList(nowHotList);
            } else {
                logger.info("noticeChange fail nowHotList is empty");
            }
        } catch (Exception e) {
            logger.error("noticeChange nowHotList={} msg={}", nowHotList, e.getMessage(), e);
        }
    }

    private boolean isSameList(List<String> nowList, List<String> oldList) {
        boolean isSame = true;
        if (nowList.size() == 0) {
            isSame = false;
        } else if (nowList.size() == oldList.size()) {
            for (int index = 0; index < nowList.size(); index++) {
                if (!nowList.get(index).equals(oldList.get(index))) {
                    isSame = false;
                    break;
                }
            }
        } else {
            isSame = false;
        }
        return isSame;
    }

    private int doMakeActiveRoomList(List<PartyListData> allList) {
        Set<String> allNewRookieRoom = newRookieRoomRedis.getAllNewRookieRoom();
        List<PartyListData> activeRoomList = getActiveRoomList(allList, allNewRookieRoom);
        List<PartyListData> officialRooms = new ArrayList<>();
        Iterator<PartyListData> iterator = activeRoomList.iterator();
        while (iterator.hasNext()) {
            PartyListData partyListData = iterator.next();
            if (partyListData.getIsOfficial() == 1) {
                officialRooms.add(partyListData);
                iterator.remove();
            }
            if (allNewRookieRoom.contains(partyListData.getRoomId())) {
                partyListData.setActiveRoomWeight(partyListData.getCtime() != 0 ? partyListData.getCtime() : 1000000000);
            } else {
                partyListData.setActiveRoomWeight(partyListData.getAliveTime() - 1000000000);
            }
        }
        List<PartyListData> normalList = activeRoomList.stream()
                .sorted(Comparator.comparing(PartyListData::getMicCount, Comparator.reverseOrder())
                        .thenComparing(Comparator.comparing(PartyListData::getActiveRoomWeight, Comparator.reverseOrder())))
                .limit(RoomListConstant.ACTIVE_ROOM_SIZE)
                .collect(Collectors.toList());
        if (officialRooms.isEmpty()) {
            roomListRedis.saveActiveRoomList(normalList, true);
            roomListRedis.saveActiveRoomList(normalList, false);
        } else {
            normalList.addAll(0, officialRooms);
            roomListRedis.saveActiveRoomList(normalList, true);
            normalList.subList(0, officialRooms.size()).clear();
            if (normalList.size() > 4) {
                normalList.addAll(4, officialRooms);
            } else {
                normalList.addAll(officialRooms);
            }
            roomListRedis.saveActiveRoomList(normalList, false);
        }
        return normalList.size();
    }

    private List<PartyListData> getActiveRoomList(List<PartyListData> allList, Set<String> allNewRookieRoom) {
        List<PartyListData> activeRoomList = allList.stream()
                .filter(p -> {
                    if (p.getPwd() == 1) {
                        return false;
                    }
                    if ((allNewRookieRoom.contains(p.getRoomId()) || p.getIsOfficial() == 1) && p.getMicCount() == 0) {
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());
        return activeRoomList;
    }


    private void doMakeOnlyActorCountRoomList(List<PartyListData> allList) {
        List<PartyListData> allRoom = allList.stream()
                .sorted(Comparator.comparing(PartyListData::getOnline).reversed())
                .limit(1000)
                .collect(Collectors.toList());
        roomListRedis.saveAllOnlyOnlineRoomList(allRoom);
    }


    private Comparator<PartyListData> getComparator(int area, PopularRecommendConfigData config) {
        Comparator<PartyListData> areaComp = Comparator.comparing(o -> {
            // https://www.tapd.cn/20792731/prong/stories/view/1120792731001015963

            // 按照区域分, 分钟内房间人气*5% + 分钟内房间上麦人数*35% + 分钟内产出的礼物总价值*50% + 分钟内公屏聊天人数*15%
            int deducting = o.getMicCount() == 0 ? (int) (config.getNoneMicToHot() * config.getNoneMicRate()) : 0;
            if (area < 0 && o.getPwd() == 0) {
                return Integer.MAX_VALUE - MAX_SPACE
                        + (double) (o.getRealOnline()) * config.getOnlineToHot() * config.getOnlineRate()
                        + o.getMicCount() * config.getMicToHot() * config.getMicRate()
                        + o.getDevote() / config.getOneHotToGiftDevote() * config.getOneHotToGiftRate()
                        + o.getMsgCount() * config.getMsgToHot() * config.getMsgRate()
                        + deducting;
            } else if (o.getArea() == area && o.getPwd() == 0) {
                return Integer.MAX_VALUE - MAX_SPACE
                        + (double) (o.getRealOnline()) * config.getOnlineToHot() * config.getOnlineRate()
                        + o.getMicCount() * config.getMicToHot() * config.getMicRate()
                        + o.getDevote() / config.getOneHotToGiftDevote() * config.getOneHotToGiftRate()
                        + o.getMsgCount() * config.getMsgToHot() * config.getMsgRate()
                        + deducting;
            }
            return o.getWeight();
        });
        return areaComp;
    }

    public PopularRecommendConfigData getPopularRecommendConfigData() {
        PopularRecommendConfigData config = configCenterService.getBeanByKey(ConfigCenterLabelKeyConstant.ROOM_LIST_SERVER,
                ConfigCenterKeyConstant.POPULAR_RECOMMEND_CONFIG, PopularRecommendConfigData.class);
        if (config == null) {
            config = new PopularRecommendConfigData();
        } else {
//            logger.info("get config PopularRecommendConfigData:{}", JSON.toJSONString(config));
        }
        if (config.getOnlineToHot() <= 0) {
            config.setOnlineToHot(10);
        }
        if (config.getMicToHot() <= 0) {
            config.setMicToHot(200);
        }
        if (config.getOneHotToGiftDevote() <= 0) {
            config.setOneHotToGiftDevote(200);
        }
        if (config.getMsgToHot() <= 0) {
            config.setMsgToHot(100);
        }
        if (config.getNoneMicToHot() > 0) {
            config.setNoneMicToHot(-500);
        }

        if (config.getOnlineRate() <= 0) {
            config.setOnlineRate(0.05f);
        }
        if (config.getMicRate() <= 0) {
            config.setMicRate(0.35f);
        }
        if (config.getOneHotToGiftRate() <= 0) {
            config.setOneHotToGiftRate(0.5f);
        }
        if (config.getMsgRate() <= 0) {
            config.setMsgRate(0.15f);
        }
        if (config.getNoneMicRate() <= 0) {
            config.setNoneMicRate(1f);
        }
        return config;
    }


    private int checkMicUser(PartyListData partyListData) {
        RoomMicListVo roomMicListVo = roomMicRedis.getRoomMicFromRedis(partyListData.getRoomId());
        int validMicCount = 0;
        if (roomMicListVo != null && !CollectionUtils.isEmpty(roomMicListVo.getList())) {
            List<RoomMicInfoObject> micList = roomMicListVo.getList();
            int index = 0;
            Set<String> onMicUser = new HashSet<>();
            for (RoomMicInfoObject item : micList) {
                if (item.getStatus() == 0) {
                    validMicCount++;
                }
                RoomMicUserObject micUser = item.getUser();
                if (micUser != null) {
                    ActorData actorData = actorDao.getActorDataFromCache(micUser.getAid());
                    if (index == 0) {
                        partyListData.setMicUserAid(micUser.getAid());// 推荐的麦位用户
                        partyListData.setMicUserGender(actorData.getFb_gender());// 推荐的麦位用户性别
                    }
                    if (index != 0 && actorData.getFb_gender() == 2) {
                        partyListData.setMicUserAid(micUser.getAid());// 推荐的麦位用户
                        partyListData.setMicUserGender(actorData.getFb_gender());
                    }
                    onMicUser.add(micUser.getAid());
                }
                index++;
            }
            partyListData.setValidMicCount(validMicCount);
            partyListData.setOnMicUserSet(onMicUser);
        }
        partyListData.setIsCheckMicList(1);
        return validMicCount;
    }

    /**
     * * 数据用于 8.59 星空首页推荐（推荐用户）（适用新,老用户策略），
     * * 8.59 星空首页（推荐用户）meet friend-all（适用新用户策略），
     * * 8.59 ALL-new（推荐房间）（适用新用户策略）
     * <p>
     * https://www.tapd.cn/20792731/prong/stories/view/1120792731001017157?from_iteration_id=1120792731001000356
     * <p>
     * ALL-new（推荐房间-迎新房排序）
     * https://www.tapd.cn/20792731/prong/stories/view/1120792731001017442
     * * 推荐公式=[闲置麦位]*K5+[同国家]*K21/[同地区]*K22+[新用户房间停留]*K4+[邀请新用户上麦]*K3
     * * -[踢出房间]*D1-[房间在线新用户人数]*D2
     *
     * @param allList
     * @return
     */
    private int doMakeSocialHomeRoomList(List<PartyListData> allList) {
        Set<String> allNewRookieRoom = newRookieRoomRedis.getAllNewRookieRoom();
        // 优质房间
        Set<String> allHighQualityRoom = operationConfigRedis.highQualityRoomList();
        // 大R房间
        Set<String> allBigRRoom = operationConfigRedis.bigRRoomList();
        List<PartyListData> activeRoomList = new ArrayList<>(allList);
        // new列表
//        List<PartyListData> newRoomList = new ArrayList<>();

        Iterator<PartyListData> iterator = activeRoomList.iterator();
//        JSONObject scoreWeightConfig = roomListRedis.getAllNewRookieRoomScoreWeight();
        JSONObject newRoomWeightConfig = roomListRedis.getAllNewRoomScoreWeight();
        while (iterator.hasNext()) {
            PartyListData partyListData = iterator.next();
            String roomId = partyListData.getRoomId();
            int micCount = partyListData.getMicCount();// 上麦人数
            // 上锁房不推荐
            if (partyListData.getPwd() == 1) {
//                logger.info("SocialHomeRoom 房间上锁 roomId:{} roomName:{} rm", partyListData.getRoomId(),partyListData.getRoom_name());
                iterator.remove();
                continue;
            }
            // 麦位满员
            if (micCount == 8) {
//                logger.info("SocialHomeRoom 麦位满员 roomId:{} roomName:{} rm", partyListData.getRoomId(),partyListData.getRoom_name());
                iterator.remove();
                continue;
            }
            // 挂机房不推荐
            if (partyListData.getIsHangUpRoom() == 1) {
                logger.info("SocialHomeRoom 挂机房不推荐 roomId:{} roomName:{} rm", partyListData.getRoomId(), partyListData.getRoom_name());
                iterator.remove();
                continue;
            }

            // 麦位相关检查
            int validMicCount = partyListData.getValidMicCount();
            if (partyListData.getIsCheckMicList() == 0) {
                validMicCount = checkMicUser(partyListData);
            }
            // 没有有效麦位了，不推荐
            if (validMicCount == 0) {
//                logger.info("SocialHomeRoom 没有有效麦位了不推荐 roomId:{} roomName:{} rm", partyListData.getRoomId(),partyListData.getRoom_name());
                iterator.remove();
                continue;
            }
            int onlineNewCount = 0;
            // 【需求详情】new列表推荐排序参数配置
            // https://www.tapd.cn/20792731/prong/stories/view/1120792731001017701
//            if (allNewRookieRoom.contains(roomId) || allHighQualityRoom.contains(roomId) || allBigRRoom.contains(roomId)) {
//                if (partyListData.getRealOnline() > 0 && micCount > 0) {
//                    PartyListData copy = new PartyListData();
//                    BeanUtils.copyProperties(partyListData, copy);
//                    Set<String> inRoomUserSet = roomPlayerRedis.getRoomActors(roomId);
//                    onlineNewCount = inRoomUserSet == null ? 0 : (int) inRoomUserSet.stream().filter(item -> {
//                        ActorData actorData = actorDao.getActorDataFromCache(item);
//                        return actorData != null && ActorUtils.isNewDeviceAccount(item, actorData.getFirstTnId());
//                    }).count();
//                    calcNewRoomWeight(copy, newRoomWeightConfig, onlineNewCount, allNewRookieRoom.contains(roomId) || allHighQualityRoom.contains(roomId));
//                    // 处理默认排序，借用socialWeight字段
//                    // 迎新房（房间内麦位无人或全锁麦状态下列表不展示该房间）>优质房（若无房间）>大R房（若无房间）>账号同国家的房间（若不足或无房间）>账号同地区房间
//                    copy.setSocialWeight(allNewRookieRoom.contains(roomId) ? 3 : allHighQualityRoom.contains(roomId) ? 2 : 1);
//                    newRoomList.add(copy);
//                }
//            }

            if (allNewRookieRoom.contains(partyListData.getRoomId()) || allHighQualityRoom.contains(roomId) || allBigRRoom.contains(roomId)) {
                // 迎新房没人上麦或者房间没人
                if (partyListData.getRealOnline() == 0 || micCount == 0) {
                    logger.info("SocialHomeRoom 迎新房麦位没人不推荐 roomId:{} roomName:{} rm", partyListData.getRoomId(), partyListData.getRoom_name());
                    iterator.remove();
                    continue;
                }

                Set<String> blacklistSet = newRookieRoomRedis.getRookieRoomBlacklist();
                // 最近60分钟内房间累计踢人≥3人，当天移出推荐池
                if (blacklistSet.contains(roomId)) {
                    logger.info("SocialHomeRoom 最近60分钟内房间累计踢人≥3人 roomId:{} roomName:{} rm", partyListData.getRoomId(), partyListData.getRoom_name());
                    iterator.remove();
                    continue;
                }

                Set<String> inRoomUserSet = roomPlayerRedis.getRoomActors(roomId);
                onlineNewCount = inRoomUserSet == null ? 0 : (int) inRoomUserSet.stream().filter(item -> {
                    ActorData actorData = actorDao.getActorDataFromCache(item);
                    return actorData != null && ActorUtils.isNewDeviceAccount(item, actorData.getFirstTnId());
                }).count();


                int onMicNewCount = 0;
                int onMicFemaleCount = 0; // 上麦的女用户数量

                Set<String> onMicUserSet = partyListData.getOnMicUserSet();
                if (onMicUserSet != null) {
//                    onMicNewCount = (int) onMicUserSet.stream().filter(item -> {
//                        ActorData actorData = actorDao.getActorDataFromCache(item);
//                        if (actorData != null && ActorUtils.isNewDeviceAccount(item, actorData.getFirstTnId())) {
//                            return true;
//                        }
//                        return false;
//                    }).count();

                    for (String item : onMicUserSet) {
                        ActorData actorData = actorDao.getActorDataFromCache(item);
                        if (actorData != null) {
                            if (ActorUtils.isNewDeviceAccount(item, actorData.getFirstTnId())) {
                                onMicNewCount++;
                            }
                            if (actorData.getFb_gender() == 2) {
                                onMicFemaleCount++;
                            }
                        }
                    }
                    onMicUserSet.clear();
                }

                if (onlineNewCount >= 5 && onMicNewCount == 0) {
                    logger.info("SocialHomeRoom 新用户大于5人,但是在麦新用户为0 roomId:{} rm", partyListData.getRoomId());
                    iterator.remove();
                    continue;
                }


//                if (recentlyActionRoom == null) {
//                    recentlyActionRoom = roomActionRedis.getRecentlyActionRoom();
//                }

//                int weight = 0;
//                weight += getRookieRoomFreeMicWeight(validMicCount, scoreWeightConfig);
//                if (recentlyActionRoom.contains(partyListData.getRoomId())) {
//                    weight += getInviteMicWeightByInviteCount(partyListData.getInviteMicCount(), scoreWeightConfig);
//                    weight += getStayRoomWeightByStayCount(partyListData.getStayRoomCount(), scoreWeightConfig);
//                    weight += getRoomKickWeightByKickCount(partyListData.getKickCount(), scoreWeightConfig);
//                }
//
//                weight += getOnlineNewUsersWeight((int) onlineNewCount, scoreWeightConfig);

                // 迎新房（房间内麦位无人或全锁麦状态下列表不展示该房间）>优质房（若无房间）>大R房（若无房间）>账号同国家的房间（若不足或无房间）>账号同地区房间
                partyListData.setSocialWeight(RoomListConstant.SOCIAL_NEW_ROOM_WEIGHT); //  迎新房，优质房，大R房都按这个权重
                partyListData.setRoomRecommendType(allNewRookieRoom.contains(roomId) ? RoomListConstant.TYPE_ROOKIE_ROOM
                        : allHighQualityRoom.contains(roomId) ? RoomListConstant.TYPE_HIGH_QUALITY_ROOM
                        : RoomListConstant.TYPE_BIG_R_ROOM);
                // setNewRookieRoomWeight ()
                int weight = calcNewRoomWeight(partyListData, newRoomWeightConfig, onlineNewCount, onMicFemaleCount, allNewRookieRoom.contains(roomId) || allHighQualityRoom.contains(roomId));
                partyListData.setNewRookieRoomWeight(weight);
                logger.info("redis-get count roomId:{} room_name:{} weight:{} validMicCount:{} onlineNewCount:{} " +
                                "kickCount:{} inviteMicCount:{} stayRoomCount:{}"
                        , partyListData.getRoomId(), partyListData.getRoom_name(), partyListData.getNewRookieRoomWeight(), validMicCount, onlineNewCount,
                        partyListData.getKickCount(), partyListData.getInviteMicCount(), partyListData.getStayRoomCount());

            } else {
                if (partyListData.getRealOnline() >= 10) {
//                    logger.info("SocialHomeRoom 真实用户大于10人 roomId:{} roomName:{} rm", partyListData.getRoomId(),partyListData.getRoom_name());
                    iterator.remove();
                    continue;
                }

                // 最近1小时内踢出房间的用户≥3个人的房间，移出推荐池4个小时
                if (partyListData.getIsKickOutRoom() == 1) {
//                    logger.info("SocialHomeRoom 踢人房间房不推荐 roomId:{} roomName:{} rm",partyListData.getRoomId(),partyListData.getRoom_name());
                    iterator.remove();
                    continue;
                }
                partyListData.setSocialWeight(partyListData.getRealOnline());
                partyListData.setNewRookieRoomWeight(0);
            }
        }

        List<PartyListData> normalList = activeRoomList.stream()
                .sorted(Comparator.comparing(PartyListData::getSocialWeight, Comparator.reverseOrder())
                        .thenComparing(PartyListData::getRoomRecommendType, Comparator.reverseOrder())
                        .thenComparing(PartyListData::getNewRookieRoomWeight, Comparator.reverseOrder()))
                .limit(RoomListConstant.SOCIAL_ROOM_MAX_SIZE)
                .collect(Collectors.toList());
        roomListRedis.saveSocialRoomList(normalList);

//        List<PartyListData> normalNewList = newRoomList.stream()
//                .sorted(Comparator.comparing(PartyListData::getSocialWeight, Comparator.reverseOrder())
//                        .thenComparing(PartyListData::getNewRookieRoomWeight, Comparator.reverseOrder()))
//                .limit(RoomListConstant.SOCIAL_ROOM_MAX_SIZE)
//                .collect(Collectors.toList());
//        roomListRedis.saveSocialNewRoomList(normalNewList);

        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                String dayStr = DateHelper.ARABIAN.formatDateInDay();
                activeRoomList.forEach(item -> {
                    if (item.getSocialWeight() == RoomListConstant.SOCIAL_NEW_ROOM_WEIGHT) {
                        noticeNewRoomJoinPoolOrKick(item.getRoomId(), dayStr, 1, item);
                        noticeNewRoomJoinPoolOrKick(item.getRoomId(), dayStr, 2, item);
                    }
                });
            }
        });
        return normalList.size();
    }


    /**
     * <a href="https://www.tapd.cn/tapd_fe/20792731/story/detail/1120792731001019515">...</a>
     * 同用户国家的官方迎新房(接口层处理) > 其他官方迎新房 > 运营活动筛选的新用户友好房间 > 同国家的房间、同地区的房间
     * 除官方迎新房以外的房间评分=K3*参考取值 + K4*参考取值 + K5*参考取值 + K21*参考取值 + K22*参考取值 - D1*参考取值 - D2*参考取值 - D3*参考取值
     * * 推荐公式=[闲置麦位]*K5+[同国家]*K21/[同地区]*K22+[新用户房间停留]*K4+[邀请新用户上麦]*K3-[踢出房间]*D1-[房间在线新用户人数]*D2
     *
     * @param allList : 所有房间
     * @return 整合房间的数量
     */
    private int doMakeAllRecommendRoomList(List<PartyListData> allList) {
        // 所有的官方迎新房
        Set<String> allOfficialWelcomeRoom = newRookieRoomRedis.getAllOfficialWelcomeRooms();
        // 所有的运营配置的迎新房
        Set<String> allNewRookieRoom = newRookieRoomRedis.getAllNewRookieRoom();

        List<PartyListData> activeRoomList = new ArrayList<>(allList);
        List<PartyListData> oldUserRecommendRoomList = new ArrayList<>();
        Iterator<PartyListData> iterator = activeRoomList.iterator();
        JSONObject newRoomWeightConfig = roomListRedis.getAllNewRoomScoreWeight();
        while (iterator.hasNext()) {
            PartyListData partyListData = iterator.next();
            String roomId = partyListData.getRoomId();
            int micCount = partyListData.getMicCount();// 上麦人数
            // 上锁房不推荐
            if (partyListData.getPwd() == 1) {
                // logger.info("SocialHomeRoom 房间上锁 roomId:{} roomName:{} rm", partyListData.getRoomId(),partyListData.getRoom_name());
                iterator.remove();
                continue;
            }

            // 麦位相关检查
            int validMicCount = partyListData.getValidMicCount();
            if (partyListData.getIsCheckMicList() == 0) {
                validMicCount = checkMicUser(partyListData);
            }
            // 没有有效麦位了，不推荐
            if (validMicCount == 0) {
                // logger.info("SocialHomeRoom 没有有效麦位了不推荐 roomId:{} roomName:{} rm", partyListData.getRoomId(),partyListData.getRoom_name());
                iterator.remove();
                continue;
            }

            int onlineNewCount = 0;    // 在房新设备新用户数量
            int onMicNewCount = 0;    // 在麦新设备新用户数量
            int onMicFemaleCount = 0; // 上麦的女用户数量
            // 在线新设备新用户数量
            Set<String> inRoomUserSet = roomPlayerRedis.getRoomActors(roomId);
            onlineNewCount = inRoomUserSet == null ? 0 : (int) inRoomUserSet.stream().filter(item -> {
                ActorData actorData = actorDao.getActorDataFromCache(item);
                return actorData != null && ActorUtils.isNewDeviceAccount(item, actorData.getFirstTnId());
            }).count();
            Set<String> onMicUserSet = partyListData.getOnMicUserSet();
            if (onMicUserSet != null) {
                for (String item : onMicUserSet) {
                    ActorData actorData = actorDao.getActorDataFromCache(item);
                    if (actorData != null) {
                        if (ActorUtils.isNewDeviceAccount(item, actorData.getFirstTnId())) {
                            onMicNewCount++;
                        }
                        if (actorData.getFb_gender() == 2) {
                            onMicFemaleCount++;
                        }
                    }
                }
                onMicUserSet.clear();
            }
            if (allOfficialWelcomeRoom.contains(roomId) || allNewRookieRoom.contains(roomId)) {
                // 没人上麦或者房间没人
                if (partyListData.getRealOnline() == 0 || micCount == 0) {
                    logger.info("SocialHomeRoom 迎新房麦位没人不推荐 roomId:{} roomName:{} rm", roomId, partyListData.getRoom_name());
                    iterator.remove();
                    continue;
                }

                Set<String> blacklistSet = newRookieRoomRedis.getRookieRoomBlacklist();
                // 最近60分钟内房间累计踢人≥3人，当天移出推荐池
                if (blacklistSet.contains(roomId)) {
                    logger.info("SocialHomeRoom 最近60分钟内房间累计踢人≥3人 roomId:{} roomName:{} rm", roomId, partyListData.getRoom_name());
                    iterator.remove();
                    continue;
                }

                if (onlineNewCount >= 5 && onMicNewCount == 0) {
                    logger.info("SocialHomeRoom 新用户大于5人,但是在麦新用户为0 roomId:{} rm", partyListData.getRoomId());
                    iterator.remove();
                    continue;
                }
                int socialWeight = 0;
                // 人气值
                if (allOfficialWelcomeRoom.contains(roomId)) {
                    partyListData.setPopularWeight(partyListData.getRealOnline());
                    socialWeight = RoomListConstant.SOCIAL_OFFICIAL_ROOM_WEIGHT;
                } else {
                    socialWeight = RoomListConstant.SOCIAL_NEW_ROOM_WEIGHT;
                }
                partyListData.setSocialWeight(socialWeight); // 官方迎新房按这个权重
            }
            // 评价体系评分
            int rateWeight = calcNewRoomWeight(partyListData, newRoomWeightConfig, onlineNewCount, onMicFemaleCount, allNewRookieRoom.contains(roomId));
            partyListData.setRateWeight(rateWeight);
            if (!allOfficialWelcomeRoom.contains(roomId) && !allNewRookieRoom.contains(roomId)) {
                oldUserRecommendRoomList.add(partyListData);
            }
        }

        List<PartyListData> newUserNormalList = activeRoomList.stream()
                .sorted(Comparator.comparing(PartyListData::getSocialWeight, Comparator.reverseOrder())
                        .thenComparing(PartyListData::getPopularWeight, Comparator.reverseOrder())
                        .thenComparing(PartyListData::getRateWeight, Comparator.reverseOrder()))
                .limit(RoomListConstant.SOCIAL_ROOM_MAX_SIZE)
                .collect(Collectors.toList());
        roomListRedis.saveSocialRoomList(newUserNormalList);

        List<PartyListData> oldUserNormalList = oldUserRecommendRoomList.stream().
                sorted(Comparator.comparing(PartyListData::getRateWeight, Comparator.reverseOrder()))
                .limit(RoomListConstant.SOCIAL_ROOM_MAX_SIZE)
                .collect(Collectors.toList());
        roomListRedis.saveAllRecommendOldUserList(oldUserNormalList);
        return newUserNormalList.size();
    }


    private int calcNewRoomWeight(PartyListData partyListData, JSONObject config, int onlineNewCount, int onMicFemaleCount,
                                  boolean reduce) {
        int weight = 0;
        int freeMicWeight = 0;
        int inviteMicWeight = 0;
        int stayRoomWeight = 0;
        int onMicFemaleWeight = 0;
        int hostActorFemaleWeight = 0;
        // 房间空麦，后续麦位可能扩容
        int freeMicCount = partyListData.getValidMicCount();
        if (freeMicCount > 0 && 1 == config.getIntValue("freeMicEnable")) {
            // 1-2,3-4,5-6,7,8
            int key = freeMicCount <= 2 ? 2 : freeMicCount <= 4 ? 4 : freeMicCount <= 6 ? 6 : freeMicCount == 7 ? 7 : 8;
            freeMicWeight = config.getIntValue("freeMic" + key) * config.getIntValue("freeMicWeight") / 100;
        }
        // 过去时间段内邀请新用户上麦
        int inviteMicCount = partyListData.getInviteMicCount();
        if (inviteMicCount > 0 && 1 == config.getIntValue("inviteMicEnable")) {
            // 1,2,3,>=4
            int key = inviteMicCount == 1 ? 1 : inviteMicCount == 2 ? 2 : inviteMicCount == 3 ? 3 : 4;
            inviteMicWeight = config.getIntValue("inviteMic" + key) * config.getIntValue("inviteMicWeight") / 100;
        }
        // 过去时间段内有效停留新用户
        int stayRoomCount = partyListData.getStayRoomCount();
        if (stayRoomCount > 0 && 1 == config.getIntValue("stayRoomEnable")) {
            // 1-2,3-4,5-6,7-8,>=9
            int key = stayRoomCount <= 2 ? 2 : stayRoomCount <= 4 ? 4 : stayRoomCount <= 6 ? 6 : stayRoomCount <= 8 ? 8 : 9;
            stayRoomWeight = config.getIntValue("stayRoom" + key) * config.getIntValue("stayRoomWeight") / 100;
        }

        // 女性用户在麦位数量权重
        if (onMicFemaleCount > 0) {
            onMicFemaleCount = Math.min(onMicFemaleCount, 3);
            onMicFemaleWeight = GENDER_WEIGHT_MAP.getOrDefault(onMicFemaleCount, 50) * 80 / 100;
        }

        // 房主是女性权重
        ActorData hostActorData = actorDao.getActorDataFromCache(partyListData.getAid());
        if (hostActorData != null && hostActorData.getFb_gender() == 2) {
            hostActorFemaleWeight = 100 * 80 / 100;
        }
        weight = freeMicWeight + inviteMicWeight + stayRoomWeight + onMicFemaleWeight + hostActorFemaleWeight;
        int kickWeight = 0;
        int onlineNewWeight = 0;
        if (reduce) {
            // [迎新房、优质房]降权规则，踢用户
            int kickCount = partyListData.getKickCount();
            if (kickCount > 0 && 1 == config.getIntValue("kickEnable")) {
                // 1,2,3,>=4
                int key = kickCount == 1 ? 1 : kickCount == 2 ? 2 : kickCount == 3 ? 3 : 4;
                kickWeight = config.getIntValue("kick" + key) * config.getIntValue("kickWeight") / 100;
            }
            // [迎新房、优质房]降权规则，房间同时在线新用户数
            if (onlineNewCount >= 3 && 1 == config.getIntValue("onlineNewEnable")) {
                // 3-5,6-8,9-11,12-15,>=16
                int key = onlineNewCount <= 5 ? 5 : onlineNewCount <= 8 ? 8 : onlineNewCount <= 11 ? 11 : onlineNewCount <= 15 ? 15 : 16;
                onlineNewWeight = config.getIntValue("onlineNew" + key) * config.getIntValue("onlineNewWeight") / 100;
            }
        }
        weight = weight - kickWeight - onlineNewWeight;
        // logger.info("calcNewRoomWeight roomId:{} weight:{} freeMicWeight:{} inviteMicWeight:{} stayRoomWeight:{} onMicFemaleWeight:{} hostActorFemaleWeight:{} kickWeight:{} onlineNewWeight:{}",
        //         partyListData.getRoomId(), weight, freeMicWeight, inviteMicWeight, stayRoomWeight, onMicFemaleWeight, hostActorFemaleWeight, kickWeight, onlineNewWeight);
        return weight;
    }

    /**
     * https://www.tapd.cn/20792731/prong/stories/view/1120792731001017270
     * 推荐公式=[闲置麦位]*K5+[同国家/同地区]*K21/K22+[新用户房间停留]*K4+[邀请新用户上麦]*K3+[新创建房间]*K1
     * -[踢出出房间]*D1-[房间在线人数]*D2
     *
     * @param allList
     * @return
     */
    private int makeAllNewOldUserList(List<PartyListData> allList) {
//        if (recentlyActionRoom != null) {
//            recentlyActionRoom.clear();
//        }
//        recentlyActionRoom = roomActionRedis.getRecentlyActionRoom();
        // forYou列表15*2秒执行一次
        if (!TICK_DATA.addAndCheck(2) || allList.isEmpty()) {
//            List<PartyListData> activeRoomList = new ArrayList<>(allList);
//            Iterator<PartyListData> iterator = activeRoomList.iterator();
//            while (iterator.hasNext()) {
//                PartyListData partyListData = iterator.next();
//                String roomId = partyListData.getRoomId();
//                if (recentlyActionRoom.contains(partyListData.getRoomId())) {
//                    int kickCount = roomActionRedis.getKickCount(roomId);
//                    int inviteMicCount = roomActionRedis.getInviteMicCount(roomId);
//                    int stayRoomCount = roomActionRedis.getStayRoomCount(roomId);
//
//                    partyListData.setKickCount(kickCount);
//                    partyListData.setInviteMicCount(inviteMicCount);
//                    partyListData.setStayRoomCount(stayRoomCount);
//                }
//            }
            return -1;
        }

        JSONObject allNewScoreWeight = roomListRedis.getAllNewOldUserScoreWeight();
        JSONObject AllNewOldMeetScoreWeight = roomListRedis.getAllNewOldUserMeetScoreWeight();
        List<PartyListData> activeRoomList = new ArrayList<>(allList);
        Iterator<PartyListData> iterator = activeRoomList.iterator();
        while (iterator.hasNext()) {
            PartyListData partyListData = iterator.next();
//            String roomId = partyListData.getRoomId();
//            if (recentlyActionRoom.contains(partyListData.getRoomId())) {
//                int kickCount = roomActionRedis.getKickCount(roomId);
//                int inviteMicCount = roomActionRedis.getInviteMicCount(roomId);
//                int stayRoomCount = roomActionRedis.getStayRoomCount(roomId);
//
//                partyListData.setKickCount(kickCount);
//                partyListData.setInviteMicCount(inviteMicCount);
//                partyListData.setStayRoomCount(stayRoomCount);
//            }
            // 上锁房不推荐
            if (partyListData.getPwd() == 1) {
                iterator.remove();
                continue;
            }
            // 麦位无人不推荐
            if (partyListData.getMicCount() == 0) {
                iterator.remove();
                continue;
            }
            // 挂机房不推荐
            if (partyListData.getIsHangUpRoom() == 1) {
                logger.info("AllNewOldUserList 挂机房不推荐 roomId:{} roomName:{} rm", partyListData.getRoomId(), partyListData.getRoom_name());
                iterator.remove();
                continue;
            }

            int validMicCount = partyListData.getValidMicCount();
            if (partyListData.getIsCheckMicList() == 0) {
                validMicCount = checkMicUser(partyListData);
            }
            // 没有有效麦位了，不推荐
            if (validMicCount == 0) {
//                logger.info("AllNewOldUserList 没有有效麦位不推荐 roomId:{} rm",partyListData.getRoomId());
                iterator.remove();
                continue;
            }

            int weight = 0;
            int weightMeet = 0;

            weight += getCreateRoomWeight(partyListData.getRoomData().getCtime(), allNewScoreWeight);
//            weight += getOnlineUsersWeight(partyListData.getRealOnline(), allNewScoreWeight);
            weight += getFreeMicWeight(partyListData, allNewScoreWeight);

            weightMeet += getCreateRoomWeight(partyListData.getRoomData().getCtime(), AllNewOldMeetScoreWeight);
//            weightMeet += getOnlineUsersWeight(partyListData.getRealOnline(), AllNewOldMeetScoreWeight);
            weightMeet += getFreeMicWeight(partyListData, AllNewOldMeetScoreWeight);

            if (recentlyActionRoom.contains(partyListData.getRoomId())) {
                weight += getInviteMicWeightByInviteCount(partyListData.getInviteMicCount(), allNewScoreWeight);
                weight += getStayRoomWeightByStayCount(partyListData.getStayRoomCount(), allNewScoreWeight);
                weight += getRoomKickWeightByKickCount(partyListData.getKickCount(), allNewScoreWeight);

                weightMeet += getInviteMicWeightByInviteCount(partyListData.getInviteMicCount(), AllNewOldMeetScoreWeight);
                weightMeet += getStayRoomWeightByStayCount(partyListData.getStayRoomCount(), AllNewOldMeetScoreWeight);
                weightMeet += getRoomKickWeightByKickCount(partyListData.getKickCount(), AllNewOldMeetScoreWeight);

            }
            partyListData.setAllNewWeight(weight); // all-new 老用户
            partyListData.setAllNewMeetWeight(weightMeet);
            partyListData.setForYouWeight(weight); // 兼容老的for you ,可能还有请求量
        }

        List<PartyListData> meetNormalList = activeRoomList.stream()
                .sorted(Comparator.comparing(PartyListData::getAllNewMeetWeight).reversed())
                .limit(RoomListConstant.SOCIAL_ROOM_MAX_SIZE).collect(Collectors.toList());
        roomListRedis.saveAllNewOldUserList(meetNormalList, true);

        List<PartyListData> normalList = activeRoomList.stream()
                .sorted(Comparator.comparing(PartyListData::getAllNewWeight).reversed())
                .limit(RoomListConstant.SOCIAL_ROOM_MAX_SIZE).collect(Collectors.toList());
        roomListRedis.saveAllNewOldUserList(normalList, false);

        // 存原来的for you
        List<PartyListData> forYoulList = activeRoomList.stream()
                .sorted(Comparator.comparing(PartyListData::getForYouWeight).reversed())
                .limit(RoomListConstant.FOR_YOU_SIZE).collect(Collectors.toList());
        roomListRedis.saveForYouList(forYoulList);

        return normalList.size();
    }


    private void noticeNewRoomJoinPoolOrKick(String roomId, String dayStr, int type, PartyListData item) {
        String cacheKey = getCacheNoticeKey(roomId, dayStr, type);
        Integer noticeCount = cacheNoticeMap.getData(cacheKey);
        noticeCount = noticeCount == null ? 0 : noticeCount;
        int kickOut = item.getKickCount();
//        logger.info("noticeNewRoomJoinPoolOrKick check type:{} roomId:{} noticeCount:{} kickOut:{}"
//                , type, roomId, noticeCount, kickOut);
        if (type == 1) {
            if (noticeCount != 1) {
                sendNoticeHostMsg(type, roomId, item);
                cacheNoticeMap.cacheData(cacheKey, 1);
            }
        } else if (type == 2) {
            if (kickOut >= 1 && noticeCount >= 0 && noticeCount < 3) {
                // noticeCount 取值为0,1,2
                if (kickOut > noticeCount) {
                    sendNoticeHostMsg(type, roomId, item);
                    cacheNoticeMap.cacheData(cacheKey, kickOut);
                }
            }
        }
    }

    private void sendNoticeHostMsg(int type, String roomId, PartyListData item) {
        RoomNotificationMsg msg = new RoomNotificationMsg();
        String aid = RoomUtils.getRoomHostId(roomId);
        msg.setUid(aid);
        msg.setUser_name(item.getRoom_name());
        msg.setUser_head("");
        msg.setHide_head(1);
        if (type == 1) {
            msg.setText(RECOMMEND_MSG_EN);
            msg.setText_ar(RECOMMEND_MSG_AR);
        } else if (type == 2) {
            msg.setText(KICK_MSG_EN);
            msg.setText_ar(KICK_MSG_AR);
        }
        List<HighlightTextObject> list = new ArrayList<>();
//        HighlightTextObject object = new HighlightTextObject();
//        object.setText(actorData.getName());
//        object.setHighlightColor("#FFE200");
//        list.add(object);
        msg.setHighlight_text(list);
        msg.setHighlight_text_ar(list);
//        if (RoomUtils.isVoiceRoom(game.getRoomId())) {
//            msg.setHide_head(1);
//        }
//        int onlineVersion = sudGameConfig.getSudGameInfoMap().get(game.getGameType()).getOnlineVersion();
//        msg.setGame_type(GAME_TYPE_JOIN_SUD_GAME);
        roomWebSender.sendPlayerWebMsg(roomId, aid, aid, msg, true);
        logger.info("noticeNewRoomJoinPoolOrKick-->sendNoticeHostMsg success type:{} roomId:{}", type, roomId);
    }

    /**
     * @param roomId
     * @param dayStr
     * @param type   1 进入推荐池通知 2 房间踢人通知
     * @return
     */
    private String getCacheNoticeKey(String roomId, String dayStr, int type) {
        // DateHelper.ARABIAN.formatDateInDay()
        return String.format("cacheNoticeKey:%s:%s:%s", roomId, dayStr, type);
    }
}
