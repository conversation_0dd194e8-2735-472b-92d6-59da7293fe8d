package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.vo.ActiveBadgeVO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.mongo.dao.ActorConfigDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户活跃等级勋章
 */
@Service
public class ActiveBadgeService extends OtherActivityService implements TaskMsgHandler {

    private static final Logger logger = LoggerFactory.getLogger(ActiveBadgeService.class);
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static List<ActiveBadgeVO.BadgeInfo> BADGE_INFO_LIST = new ArrayList<>();
    static {
        BADGE_INFO_LIST.add(new ActiveBadgeVO.BadgeInfo(3, 3342, "https://cdn3.qmovies.tv/badge/op_1749199037_top1_c_600.webp", 15, 0));
        BADGE_INFO_LIST.add(new ActiveBadgeVO.BadgeInfo(7, 3343, "https://cdn3.qmovies.tv/badge/op_1749199037_top1_c_600.webp", 20, 0));
        BADGE_INFO_LIST.add(new ActiveBadgeVO.BadgeInfo(15, 3344, "https://cdn3.qmovies.tv/badge/op_1749199037_top1_c_600.webp", 25, 0));
        BADGE_INFO_LIST.add(new ActiveBadgeVO.BadgeInfo(30, 3345, "https://cdn3.qmovies.tv/badge/op_1749199037_top1_c_600.webp", 30, 0));
        BADGE_INFO_LIST.add(new ActiveBadgeVO.BadgeInfo(60, 3346, "https://cdn3.qmovies.tv/badge/op_1749199037_top1_c_600.webp", 30, 0));
        BADGE_INFO_LIST.add(new ActiveBadgeVO.BadgeInfo(90, 3347, "https://cdn3.qmovies.tv/badge/op_1749199037_top1_c_600.webp", 30, 0));
    }



    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ActorConfigDao actorConfigDao;


    public ActiveBadgeVO getActiveBadgeInfo(String uid) {
        ActiveBadgeVO vo = new ActiveBadgeVO();

        int activeDay = (int) actorConfigDao.getLongUserConfig(uid, ActorConfigDao.ACTIVE_BADGE_DAY, 0);
        vo.setActiveDay(activeDay);


        return vo;
    }



    @Override
    public void taskMsgProcess(CommonMqTopicData data) {

        String item = data.getItem();
        if (!CommonMqTaskConstant.ON_MIC_TIME.equals(item)) {
            return;
        }
        String fromUid = data.getUid();

    }
}
