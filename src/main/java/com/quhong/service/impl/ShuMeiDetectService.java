package com.quhong.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.core.web.HttpResponseData;
import com.quhong.data.DetectFromData;
import com.quhong.data.DetectToData;
import com.quhong.data.ShuMeiImageData;
import com.quhong.data.ShuMeiTextData;
import com.quhong.enums.MonitorWarnName;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mysql.dao.ThirdDetectLogDao;
import com.quhong.service.AbstractDetectService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;


@Service
public class ShuMeiDetectService extends AbstractDetectService {
    private final static Logger logger = LoggerFactory.getLogger(ShuMeiDetectService.class);
    //    private final static String ACCESS_KEY = "yrLK7bdbbC6wHLjCtK9p";
//    private final static String APP_ID = "rtc_3393354996";
    private final static String ACCESS_KEY = "91t98oL1NOATYUjAZsK7";
    private final static String APP_ID = "youstar";
    private final static String DETECT_TEXT_URL = "http://api-text-xjp.fengkongcloud.com/text/v4"; // 新加坡
    private final static long DETECT_TEXT_TIME = 3000;

    private final static String DETECT_IMG_URL = "http://api-img-xjp.fengkongcloud.com/image/v4"; // 新加坡
    private final static long DETECT_IMG_TIME = 20000;
    public static final String COMMON_MESSAGE = "commonMessage";       //  区分明细见 DetectOriginConstant

    public static final Set<String> UNDER_AGE_SET = new LinkedHashSet<String>() {{//未成年标签
        add("yinger"); // 婴儿
        add("ertong"); // 儿童
        add("shaonian");// 少年
    }};

    public static final Map<String, List<List<String>>> MATCH_IMAGE_TEXT_MAP = new HashMap<String, List<List<String>>>() {
        {
            put("SnapChat", Arrays.asList(Arrays.asList("youstar.official", "Following"), Arrays.asList("youstar.official", "تمت الإضافة"))); //
            put("InStagram", Arrays.asList(Arrays.asList("youstar.official", "Follo..."), Arrays.asList("youstar.official", "أتابع"))); //
            put("TikTok", Arrays.asList(Arrays.asList("youstar.official", "Message"), Arrays.asList("youstar.official", "مراسلة"))); //
        }
    };

    @Resource
    private ThirdDetectLogDao thirdDetectLogDao;

    @Override
    public DetectToData detect(DetectFromData detectFromData) {
        if (!StringUtils.isEmpty(detectFromData.getText())) {
            return detectText(detectFromData);
        } else if (!StringUtils.isEmpty(detectFromData.getImageUrl())) {
            return detectImage(detectFromData);
        } else {
            logger.info("not support source detectFromData:{}", detectFromData);
            return null;
        }
    }


    private DetectToData detectText(DetectFromData detectFromData) {
        DetectToData detectToData = new DetectToData();
        int isSafe = 1;
        try {
            JSONObject jsonObject = new JSONObject();
            JSONObject dataObject = new JSONObject();

            String tokenId = !StringUtils.isEmpty(detectFromData.getFromUid())
                    ? detectFromData.getFromUid() : detectFromData.getRequestId();
            dataObject.put("text", detectFromData.getText());
            dataObject.put("tokenId", tokenId);
            dataObject.put("lang", "auto");

            jsonObject.put("accessKey", ACCESS_KEY);
            jsonObject.put("appId", APP_ID);
            jsonObject.put("eventId", COMMON_MESSAGE);
//            jsonObject.put("eventId", "message");
            jsonObject.put("type", "TEXTRISK");
            jsonObject.put("data", dataObject);

            //  发起请求
            String reqJson = jsonObject.toJSONString();
            long timeMillis = System.currentTimeMillis();
            HttpResponseData<String> responseData = webClient.sendRestfulPost(DETECT_TEXT_URL, reqJson, null);
            long costTime = System.currentTimeMillis() - timeMillis;
            logger.info("detectText from shumei Api textMd5={} eventId={} text={} tokenId={} costTime={}",
                    detectFromData.getTextMD5(), detectFromData.getOrigin(),
                    detectFromData.getText(), tokenId, costTime);
            if (costTime >= DETECT_TEXT_TIME) {
                String warnDetail = String.format("调用数美文本检测接口大于%s ms, 耗时: %s ms, 检测文本: %s", DETECT_TEXT_TIME, costTime, detectFromData.getText());
                monitorSender.info("ustar_java_exception", "数美检测", warnDetail);
            }

            // 解析结果
            String body = responseData.getBody();
            if (responseData.getStatus() == 200) {
                logger.info("shumei response body:{} ", body);
                ShuMeiTextData shuMeiTextData = JSON.parseObject(body, ShuMeiTextData.class);
                logger.info("shumei response textMd5={} eventId={} text={} tokenId={} detectText: {}",
                        detectFromData.getTextMD5(), detectFromData.getOrigin(), detectFromData.getText(),
                        tokenId, shuMeiTextData);
                if (shuMeiTextData.getCode() != 1100) {
                    monitorSender.info("ustar_java_exception", "数美api请求异常",
                            "数美文本api请求业务code非1100 code:" + shuMeiTextData.getCode() +
                                    "数美响应body:" + body);
                    detectToData.setIsSafe(isSafe);
                    return detectToData;
                }
                String riskLevel = shuMeiTextData.getRiskLevel();
                switch (riskLevel) {
                    case "PASS":
                        break;
                    case "REVIEW":
                    case "REJECT":
                        isSafe = 0;
                        break;
                }
                detectToData.setScore(1.0);
                String resultData = JSON.toJSONString(shuMeiTextData);
                detectToData.setResultData(resultData);
                int finalIsSafe = isSafe;
//                BaseTaskFactory.getFactory().addSlow(new Task() {
//                    @Override
//                    protected void execute() {
//                        ThirdDetectLogData log = new ThirdDetectLogData();
//                        log.setReqId(shuMeiTextData.getRequestId());
//                        log.setContentType(DetectConstant.CONTENT_TYPE_TEXT);
//                        log.setContent(detectFromData.getText());
//                        log.setContentMd5(detectFromData.getTextMD5());
//                        log.setScore("1.0");
//                        log.setIsSafe(finalIsSafe);
//                        log.setResultData(resultData);
//                        log.setCtime(DateHelper.getNowSeconds());
//                        thirdDetectLogDao.addThirdLog(log);
//                    }
//                });
            } else {
                monitorSender.info("ustar_java_exception", "数美文本api请求异常",
                        "数美文本api请求 status返回非200 status:" + responseData.getStatus() +
                                "数美响应body:" + body);
            }
            detectToData.setIsSafe(isSafe);
            return detectToData;
        } catch (Exception e) {
            logger.error("shumei text detection error={}", e.getMessage(), e);
            monitorSender.info("ustar_java_exception", "数美文本api请求异常",
                    "数美文本api请求异常 error msg:" + e.getMessage());
            detectToData.setIsSafe(isSafe);
            return detectToData;
        }
    }


    private DetectToData detectImage(DetectFromData detectFromData) {
        DetectToData detectToData = new DetectToData();
        int isSafe = 1;
        try {
            // 构造请求
//            Map<String, String> headerMap = new HashMap<>();

            JSONObject jsonObject = new JSONObject();
            JSONObject dataObject = new JSONObject();

            String tokenId = !StringUtils.isEmpty(detectFromData.getFromUid())
                    ? detectFromData.getFromUid() : detectFromData.getRequestId();
            String simpleUrl = ImageUrlGenerator.generateUrlByMode(detectFromData.getImageUrl(),
                    true, ImageUrlGenerator.MODE_500);
            dataObject.put("img", simpleUrl);
            dataObject.put("tokenId", tokenId);
            dataObject.put("lang", "ar");

            jsonObject.put("accessKey", ACCESS_KEY);
            jsonObject.put("appId", APP_ID);
//            jsonObject.put("eventId", "message");
            jsonObject.put("eventId", COMMON_MESSAGE);
            jsonObject.put("type", "POLITY_EROTIC_VIOLENT_IMGTEXTRISK");
            jsonObject.put("data", dataObject);
            jsonObject.put("businessType", "AGE_FACEDETECTION");

            //  发起请求
            String reqJson = jsonObject.toJSONString();
            long timeMillis = System.currentTimeMillis();
            HttpResponseData<String> responseData = webClient.sendRestfulPost(DETECT_IMG_URL, reqJson, null);
            long costTime = System.currentTimeMillis() - timeMillis;
            logger.info("shumei detectImage from Api urlMd5={} eventId={} simpleUrl={} tokenId={} costTime={}",
                    detectFromData.getUrlMD5(),
                    detectFromData.getOrigin(), simpleUrl, tokenId, costTime);
            if (costTime >= DETECT_IMG_TIME) {
                String warnDetail = String.format("调用数美图片检测接口大于%s ms, 耗时: %s ms, 检测图片: %s", DETECT_IMG_TIME, costTime, detectFromData.getImageUrl());
                monitorSender.info("ustar_java_exception", "数美检测", warnDetail);
            }

            // 解析结果
            String body = responseData.getBody();
            if (responseData.getStatus() == 200) {
                logger.info("shumei response body:{}  ", body);
                ShuMeiImageData shuMeiImageData = JSON.parseObject(body, ShuMeiImageData.class);
                logger.info("shumei urlMd5={} eventId={} simpleUrl={} tokenId={} detectImage: {}",
                        detectFromData.getUrlMD5(),
                        detectFromData.getOrigin(), simpleUrl, tokenId,
                        shuMeiImageData);
                if (shuMeiImageData.getCode() != 1100) {
                    monitorSender.info(MonitorWarnName.WARN_SHUMEI_CHECK, "数美api请求异常",
                            "数美图片api请求业务code非1100 code:" + shuMeiImageData.getCode() +
                                    "数美响应body:" + body);
                    logger.error("数美图片api请求业务code非1100 code:{} 数美响应body:{}"
                            , shuMeiImageData.getCode(), body);
                    detectToData.setIsSafe(isSafe);
                    return detectToData;
                }

                String riskLevel = shuMeiImageData.getRiskLevel();

                switch (riskLevel) {
                    case "PASS":
                        break;
                    case "REVIEW":
                    case "REJECT":
                        isSafe = 0;
                        break;
                }

                detectToData.setScore(1.0);
                String resultData = JSON.toJSONString(shuMeiImageData);
                detectToData.setResultData(resultData);

                if (isSafe == 1 && !ObjectUtils.isEmpty(shuMeiImageData.getBusinessLabels())) {
                    AtomicReference<String> ageType = new AtomicReference<>("");
                    shuMeiImageData.getBusinessLabels().stream()
                            .filter(businessInfo -> StringUtils.hasLength(businessInfo.getBusinessLabel2()))
                            .filter(businessInfo -> "nianling".equalsIgnoreCase(businessInfo.getBusinessLabel2()))
                            .findFirst().ifPresent(businessInfo -> ageType.set(businessInfo.getBusinessLabel3()));

                    if (UNDER_AGE_SET.contains(ageType.get())) {
//                        logger.info("ageType contians:{}",ageType.get());
                        isSafe = 0;
                    }
                }
                if (!ObjectUtils.isEmpty(shuMeiImageData.getRiskDetail())
                        && DetectOriginConstant.ACTIVITY_SHARING_OFFICER_HEAD.equals(detectFromData.getOrigin())) {
                    detectToData.setHitText(getHitMatchText(shuMeiImageData.getRiskDetail()));
                }
//                int finalIsSafe = isSafe;
//                BaseTaskFactory.getFactory().addSlow(new Task() {
//                    @Override
//                    protected void execute() {
//                        ThirdDetectLogData log = new ThirdDetectLogData();
//                        log.setReqId(shuMeiImageData.getRequestId());
//                        log.setContentType(DetectConstant.CONTENT_TYPE_IMG);
//                        log.setContent(detectFromData.getImageUrl());
//                        log.setContentMd5(detectFromData.getUrlMD5());
//                        log.setScore("1.0");
//                        log.setIsSafe(finalIsSafe);
//                        log.setResultData(resultData);
//                        log.setCtime(DateHelper.getNowSeconds());
//                        thirdDetectLogDao.addThirdLog(log);
//                    }
//                });
            } else {
                monitorSender.info("ustar_java_exception", "数美图片api请求异常",
                        "数美图片api请求 status返回非200 status:" + responseData.getStatus() +
                                "数美响应body:" + body);
            }
            detectToData.setIsSafe(isSafe);

            return detectToData;

        } catch (Exception e) {
            logger.error("image detection error={}", e.getMessage());
            detectToData.setIsSafe(isSafe);
            return detectToData;
        }
    }


    private String getHitMatchText(String riskdetail) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, List<List<String>>> entry : MATCH_IMAGE_TEXT_MAP.entrySet()) {
            for (List<String> itemList : entry.getValue()) {
                if (itemList == null || itemList.isEmpty()) {
                    continue;
                }
                // 使用 Set 提高查找效率
                Set<String> itemSet = new HashSet<>(itemList);
                int size = itemSet.size();
//                logger.info("key:{}--->size:{} itemSet:{}", entry.getKey(), size, itemSet);
                int count = 0;
                for (String item : itemSet) {
                    // 这里对大小写敏感，因为这里有英语，阿语混排
                    if (riskdetail.contains(item)) {
                        count++;
                    }
                    if (count == size) {
                        sb.append(entry.getKey()).append(",");
                    }
                }
            }
        }
        return sb.toString();
    }

}
