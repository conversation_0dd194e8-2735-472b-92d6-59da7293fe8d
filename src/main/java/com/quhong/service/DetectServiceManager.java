package com.quhong.service;

import com.quhong.constant.DetectConstant;
import com.quhong.service.impl.BiGoDetectService;
import com.quhong.service.impl.ShuMeiDetectService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
public class DetectServiceManager {
    private static final Logger logger = LoggerFactory.getLogger(DetectServiceManager.class);
    private static final Map<String, AbstractDetectService> detectServiceMap = new HashMap<>();

    @Resource
    private ShuMeiDetectService shuMeiDetectService;
    @Resource
    private BiGoDetectService biGoDetectService;

    @PostConstruct
    public void postInit() {
        detectServiceMap.put(DetectConstant.SHU_MEI, shuMeiDetectService);
        detectServiceMap.put(DetectConstant.BI_GO, biGoDetectService);
    }

    public AbstractDetectService getDetectService(String type) {
        return detectServiceMap.get(type);
    }
}
