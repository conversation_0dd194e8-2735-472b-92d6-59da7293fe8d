package com.quhong.service;

import com.quhong.data.OfficialMsgReq;
import com.quhong.data.vo.OfficialMsgVo;
import com.quhong.enums.ApiResult;
import com.quhong.mongo.dao.NoticeNewDao;
import com.quhong.mongo.dao.OfficialDao;
import com.quhong.mongo.data.OfficialData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/23
 */
@Component
public class OfficialMsgService {

    private static final Logger logger = LoggerFactory.getLogger(OfficialMsgService.class);

    private static final Integer PAGE_SIZE = 6;  // 官方消息分页

    @Resource
    private NoticeNewDao noticeNewDao;
    @Resource
    private OfficialDao officialDao;

    /**
     * 获取官方通知  1 notification  2 activity
     *
     * @param reqData
     * @return
     */
    public ApiResult<Object> getOfficialMsg(OfficialMsgReq reqData) {
        ApiResult<Object> result = new ApiResult<>();
        OfficialMsgVo vo = new OfficialMsgVo();
        int page = reqData.getPage() != 0 ? reqData.getPage() : 1;
        int start = (reqData.getPage() - 1) * PAGE_SIZE;
        String uid = reqData.getUid();
        List<OfficialData> officialDataList;
        if (reqData.getNtype() == 1) {
            officialDataList = officialDao.findNotificationPage(uid, start, PAGE_SIZE);
        } else {
            officialDataList = officialDao.findActivityPage(uid, start, PAGE_SIZE);
        }
        if (CollectionUtils.isEmpty(officialDataList)) {
            vo.setList(Collections.emptyList());
            vo.setNextUrl("");
            cleanUnread(uid, reqData.getNtype());
            return result.ok(vo);
        }
        List<OfficialMsgVo.OfficialMsg> list = new ArrayList<>();
        for (OfficialData data : officialDataList) {
            OfficialMsgVo.OfficialMsg msg = new OfficialMsgVo.OfficialMsg();
            msg.setTitle(data.getTitle());
            msg.setSubTitle(data.getSubTitle());
            msg.setPicture(data.getPicture());
            msg.setBody(data.getBody());
            msg.setAct(data.getAct());
            msg.setMtime(data.getMtime());
            msg.setUrl(buildUrl(uid, data.getUrl()));
            msg.setTo_uid(data.getTo_uid());
            msg.setCtime(data.getCtime());
            msg.setNews_type(data.getNews_type());
            msg.setStatus(data.getStatus());
            msg.setNews_id(data.get_id().toString());
            msg.setAtype(data.getAtype());
            msg.setAward_type(data.getAward_type());
            msg.setAward_num(data.getAward_num());
            msg.setAward_icon(data.getAward_icon());
            msg.setRoom_id(data.getRoom_id());
            msg.setActionValue(data.getRoom_id());
            msg.setWidth(data.getWidth());
            msg.setHeight(data.getHeight());
            msg.setAward_list(data.getAward_list());
            list.add(msg);
        }
        cleanUnread(uid, reqData.getNtype());
        vo.setList(list);
        vo.setNextUrl(CollectionUtils.isEmpty(list) ? "" : String.valueOf(page + 1));
        return result.ok(vo);
    }

    private void cleanUnread(String uid, int ntype) {
        if (ntype == 1) {
            noticeNewDao.cleanNotificationUnread(uid);
        } else {
            noticeNewDao.cleanActivityUnread(uid);
        }
    }

    private String buildUrl(String uid, String url) {
        if (StringUtils.isEmpty(url)) {
            return "";
        }
        try {
            UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);
            urlBuilder.queryParam("uid", uid);
            return urlBuilder.build(false).encode().toUriString();
        } catch (Exception e) {
            logger.error("{} is not a valid HTTP URL {}", url, e.getMessage(), e);
        }
        return "";
    }
}
