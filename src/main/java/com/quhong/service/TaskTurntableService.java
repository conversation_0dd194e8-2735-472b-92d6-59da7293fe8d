package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.DrawPrizeRecordEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.TaskTurntableEvent;
import com.quhong.config.TaskTurntableConfig;
import com.quhong.constant.ActivityConstant;
import com.quhong.constant.MoneyTypeConstant;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.dto.TaskTurntableDTO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.UserHttpCode;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.dao.TaskTurntableDao;
import com.quhong.mysql.data.TaskTurntableData;
import com.quhong.redis.ComponentRedis;
import com.quhong.redis.TaskTurntableRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.TaskTurntableDrawVO;
import com.quhong.vo.TaskTurntableRecordVO;
import com.quhong.vo.TaskTurntableVO;
import com.quhong.vo.WebTurntableVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class TaskTurntableService {

    private static final Logger logger = LoggerFactory.getLogger(TaskTurntableService.class);
    private static final List<Integer> AMOUNT_LIST = Arrays.asList(1, 10);
    private static final List<Integer> NUMBER_RES_TYPE = Arrays.asList(-3, -2, 4, 13, 14, 16);
    private static final int LIMIT_INIT_POOL = 30;
    private static final int ZERO_INIT_POOL = 0;
    private static final String TURNTABLE_PRIMARY = "primary";
    private static final String TURNTABLE_ADVANCED = "advanced";
    private static final String GAME_COST_TITLE = "Task Turntable Fee";
    private static final String GAME_COST_REMARK = "Task Turntable Fee";
    private static final String GAME_AWARD_TITLE = "Task Turntable Award";
    private static final String GAME_AWARD_REMARK = "Task Turntable Award";
    private static final String REWARD_NAME_NUMBER_EN = "x%s";
    private static final String REWARD_NAME_NUMBER_AR = "x%s";
    private static final String REWARD_NAME_DAY_EN = "%s day(s)";
    private static final String REWARD_NAME_DAY_AR = "%s أيام";
    private static final List<String> TURNTABLE_TYPE = Arrays.asList(TURNTABLE_PRIMARY, TURNTABLE_ADVANCED);
    private static final List<String> TURNTABLE_RES_KEY_LIST = Arrays.asList("taskTurntablePrimary", "taskTurntableAdvanced");
    private static final String WEB_TURNTABLE_RES_KEY = "taskTurntableWeb";
    private static final Integer RECORD_PAGE_SIZE = 10;
    private static final String WEB_TASK_TURNTABLE_URL = ServerConfig.isProduct()? "https://static.youstar.live/daily_rewards/" : "https://test2.qmovies.tv/daily_rewards/";

    // private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private TaskTurntableRedis taskTurntableRedis;
    @Resource
    protected UserLotteryTicketsService userLotteryTicketsService;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private TaskTurntableDao taskTurntableDao;
    @Resource
    private ActorDao actorDao;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private RoomWebSender roomWebSender;



    public TaskTurntableVO taskTurntableConfig(String uid) {

        TaskTurntableVO vo = new TaskTurntableVO();
        Map<String, ResourceKeyConfigData> resConfigDataMap = resourceKeyConfigDao.findListByKeyList(TURNTABLE_RES_KEY_LIST).stream().collect(Collectors.toMap(ResourceKeyConfigData::getKey, Function.identity()));
        ResourceKeyConfigData primaryResConfigData = resConfigDataMap.get(TURNTABLE_RES_KEY_LIST.get(0));
        ResourceKeyConfigData advancedResConfigData = resConfigDataMap.get(TURNTABLE_RES_KEY_LIST.get(1));
        if (primaryResConfigData == null || advancedResConfigData == null){
            throw new CommonH5Exception(UserHttpCode.SERVER_ERROR.getCode(), "ResourceKey not Config");
        }

        vo.setPrimaryTurntableList(fillTaskTurntableConfig(primaryResConfigData));
        vo.setAdvancedTurntableList(fillTaskTurntableConfig(advancedResConfigData));
        vo.setTodayFreeDraw(taskTurntableRedis.getTodayDrawScore(uid));

        // 设置初级滚动消息
        Map<String, ResourceKeyConfigData.ResourceMeta> primaryMap =  primaryResConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
        List<String> primaryDrawList = taskTurntableRedis.getRollRecordList(TURNTABLE_PRIMARY);
        List<TaskTurntableVO.RollRecord> primaryRollList = new ArrayList<>();
        for (String drawKey : primaryDrawList) {
            TaskTurntableVO.RollRecord rollRecord = new TaskTurntableVO.RollRecord();
            String[] split = drawKey.split("_");
            ActorData actorData = actorDao.getActorDataFromCache(split[0]);
            if(actorData == null){
                continue;
            }

            ResourceKeyConfigData.ResourceMeta resourceMeta = primaryMap.get(split[1]);
            if(resourceMeta == null){
                continue;
            }
            TaskTurntableConfig.CommonAwardConfig commonAwardConfig = commonAwardConfigCopyResourceMeta(resourceMeta);
            BeanUtils.copyProperties(commonAwardConfig, rollRecord);
            rollRecord.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            rollRecord.setName(actorData.getName());
            primaryRollList.add(rollRecord);
        }
        vo.setPrimaryRollList(primaryRollList);

        // 设置高级滚动消息
        Map<String, ResourceKeyConfigData.ResourceMeta> advancedMap =  advancedResConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
        List<String> advancedDrawList = taskTurntableRedis.getRollRecordList(TURNTABLE_ADVANCED);
        List<TaskTurntableVO.RollRecord> advancedRollList = new ArrayList<>();
        for (String drawKey : advancedDrawList) {

            TaskTurntableVO.RollRecord rollRecord = new TaskTurntableVO.RollRecord();
            String[] split = drawKey.split("_");
            ActorData actorData = actorDao.getActorDataFromCache(split[0]);
            if(actorData == null){
                continue;
            }

            ResourceKeyConfigData.ResourceMeta resourceMeta = advancedMap.get(split[1]);
            if(resourceMeta == null){
                continue;
            }
            TaskTurntableConfig.CommonAwardConfig commonAwardConfig = commonAwardConfigCopyResourceMeta(resourceMeta);
            BeanUtils.copyProperties(commonAwardConfig, rollRecord);
            rollRecord.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            rollRecord.setName(actorData.getName());
            advancedRollList.add(rollRecord);
        }
        vo.setAdvancedRollList(advancedRollList);

        ActorData actorData = actorDao.getActorData(uid);
        vo.setCouponBalance(userLotteryTicketsService.getUserTicketsNum(uid));
        vo.setCoinBalance(actorData.getHeartGot());
        return vo;
    }

    private TaskTurntableConfig.CommonAwardConfig commonAwardConfigCopyResourceMeta(ResourceKeyConfigData.ResourceMeta resourceMeta){
        TaskTurntableConfig.CommonAwardConfig commonAwardConfig = new TaskTurntableConfig.CommonAwardConfig();
        commonAwardConfig.setDrawType(resourceMeta.getMetaId());
        commonAwardConfig.setIconEn(resourceMeta.getResourceIcon());
        commonAwardConfig.setIconAr(resourceMeta.getResourceIcon());
        commonAwardConfig.setNameEn(resourceMeta.getResourceNameEn());
        commonAwardConfig.setNameAr(resourceMeta.getResourceNameAr());
        commonAwardConfig.setNumDayEn(NUMBER_RES_TYPE.contains(resourceMeta.getResourceType()) ? String.format(REWARD_NAME_NUMBER_EN, resourceMeta.getResourceNumber()) : String.format(REWARD_NAME_DAY_EN, resourceMeta.getResourceTime()));
        commonAwardConfig.setNumDayAr(NUMBER_RES_TYPE.contains(resourceMeta.getResourceType()) ? String.format(REWARD_NAME_NUMBER_AR, resourceMeta.getResourceNumber()) : String.format(REWARD_NAME_DAY_AR, resourceMeta.getResourceTime()));
        commonAwardConfig.setRewardTime(resourceMeta.getResourceTime());
        commonAwardConfig.setRewardNum(resourceMeta.getResourceNumber());
        commonAwardConfig.setScreen(resourceMeta.getAllRoomScreen());
        return commonAwardConfig;
    }

    private List<TaskTurntableConfig.CommonAwardConfig> fillTaskTurntableConfig(ResourceKeyConfigData resConfigData){
        List<TaskTurntableConfig.CommonAwardConfig> turntableList = new ArrayList<>();
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resConfigData.getResourceMetaList()) {
            turntableList.add(commonAwardConfigCopyResourceMeta(resourceMeta));
        }
        return turntableList;
    }

    private void taskTurntableCost(String uid, int changed, String turntableType) {
        if(TURNTABLE_PRIMARY.equals(turntableType)){
            boolean heartFlag = heartRecordDao.changeHeart(uid, -changed, GAME_COST_TITLE, GAME_COST_REMARK);
            if (!heartFlag) {
                throw new CommonH5Exception(UserHttpCode.INSUFFICIENT_NUMBER);
            }
        }else {
            try {
                userLotteryTicketsService.reduceTicketsNum(uid, changed, GAME_COST_REMARK);
            }catch (CommonException e){
                if (e.getHttpCode().getCode() == 1){
                    throw new CommonH5Exception(UserHttpCode.INSUFFICIENT_TICKET);
                }else {
                    throw new CommonH5Exception(e.getHttpCode());
                }
            }
        }

    }


    private void initPoolSize(String turntableType){

        int poolSize = taskTurntableRedis.getPoolSize(turntableType);
        if (poolSize > ZERO_INIT_POOL && poolSize <= LIMIT_INIT_POOL){
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    initTaskTurntableSize(turntableType);
                }
            });
        }else if(poolSize <= ZERO_INIT_POOL){
            initTaskTurntableSize(turntableType);
        }
    }

    public void initTaskTurntableSize(String turntableType){
        List<String> poolList = new ArrayList<>();
        Map<String, ResourceKeyConfigData> resConfigDataMap = resourceKeyConfigDao.findListByKeyList(TURNTABLE_RES_KEY_LIST).stream().collect(Collectors.toMap(ResourceKeyConfigData::getKey, Function.identity()));
        ResourceKeyConfigData resourceKeyConfigData = TURNTABLE_PRIMARY.equals(turntableType) ? resConfigDataMap.get(TURNTABLE_RES_KEY_LIST.get(0)) : resConfigDataMap.get(TURNTABLE_RES_KEY_LIST.get(1));
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceKeyConfigData.getResourceMetaList()) {
            int prizeSize = Integer.parseInt(resourceMeta.getRateNumber());
            String poolKey = resourceMeta.getMetaId();
            for (int i = 0; i < prizeSize; i++) {
                poolList.add(poolKey);
            }
        }
        Collections.shuffle(poolList);
        taskTurntableRedis.initPoolSize(poolList, turntableType);

    }

    private void taskTurntableRewardDistribute(String uid, String turntableType, int changed,  List<ResourceKeyConfigData.ResourceMeta> rewardMetaList){

        int currentTime = DateHelper.getNowSeconds();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : rewardMetaList) {
            resourceKeyHandlerService.sendOneResourceDataNoBroadcast(actorData, resourceMeta, MoneyTypeConstant.TASK_TURNTABLE_TYPE, MoneyTypeConstant.TASK_TURNTABLE_TITLE, MoneyTypeConstant.TASK_TURNTABLE_DESC, 0);
            TaskTurntableData taskTurntableData = new TaskTurntableData();
            taskTurntableData.setDrawKey(resourceMeta.getMetaId());
            taskTurntableData.setUid(uid);
            taskTurntableData.setTurntableType(turntableType);
            taskTurntableData.setAmount(changed);
            taskTurntableData.setCtime(currentTime);
            taskTurntableDao.insert(taskTurntableData);

            TaskTurntableEvent event = new TaskTurntableEvent();
            event.setUid(uid);
            event.setTask_turntable_rewards_desc(resourceMeta.getResourceNameEn());
            event.setTask_turntable_type(turntableType);
            event.setTask_turntable_cost_amount(changed);
            event.setCtime(currentTime);
            eventReport.track(new EventDTO(event));
        }

    }

    private String getTaskTurntableLockKey(String uid) {
        return "taskTurntableLock:" + uid;
    }


    public TaskTurntableDrawVO taskTurntableDraw(TaskTurntableDTO dto){

        String turntableType = dto.getTurntableType();
        String uid = dto.getUid();
        int amount = dto.getAmount();

        if(!AMOUNT_LIST.contains(amount) || !TURNTABLE_TYPE.contains(turntableType)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        try (DistributeLock lock = new DistributeLock(getTaskTurntableLockKey(uid))) {
            lock.lock();

            // 1、扣费
            // 初级转盘每天有一次免费抽奖
            int changed;
            int recordAmount;
            Map<String, ResourceKeyConfigData> resConfigDataMap = resourceKeyConfigDao.findListByKeyList(TURNTABLE_RES_KEY_LIST).stream().collect(Collectors.toMap(ResourceKeyConfigData::getKey, Function.identity()));
            ResourceKeyConfigData resourceKeyConfigData = null;
            if(TURNTABLE_PRIMARY.equals(turntableType)){
                changed = amount == 1 ? 1000 : 10000;
                recordAmount = 1000;
                resourceKeyConfigData = resConfigDataMap.get(TURNTABLE_RES_KEY_LIST.get(0));
                if(amount == 1){
                    int todayDraw = taskTurntableRedis.getTodayDrawScore(uid);
                    if(todayDraw == 0){
                        changed = 0;
                        recordAmount = 0;
                        taskTurntableRedis.incrTodayDrawScore(uid, 1);
                    }
                }
            }else {
                changed = amount == 1 ? 10 : 100;
                recordAmount = 10;
                resourceKeyConfigData = resConfigDataMap.get(TURNTABLE_RES_KEY_LIST.get(1));
            }

            if (resourceKeyConfigData == null){
                throw new CommonH5Exception(UserHttpCode.SERVER_ERROR.getCode(), "resourceKey not config");
            }

            if(changed > 0){
                taskTurntableCost(uid, changed, turntableType);
            }

            // 2、抽奖
            TaskTurntableDrawVO vo = new TaskTurntableDrawVO();
            List<TaskTurntableConfig.CommonAwardConfig> drawList = new ArrayList<>();
            List<ResourceKeyConfigData.ResourceMeta> rewardMetaList = new ArrayList<>();
            Map<String, ResourceKeyConfigData.ResourceMeta> taskTurntableConfigMap =  resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));

            for (int i = 0; i < amount; i++) {
                String drawKey = null;
                initPoolSize(turntableType);
                drawKey = taskTurntableRedis.drawCardKey(turntableType);
                ResourceKeyConfigData.ResourceMeta resourceMeta = taskTurntableConfigMap.get(drawKey);
                if(resourceMeta == null){
                    logger.error("taskTurntableDraw not find config :{}", drawKey);
                    continue;
                }
                rewardMetaList.add(resourceMeta);
                TaskTurntableConfig.CommonAwardConfig drawConfig = commonAwardConfigCopyResourceMeta(resourceMeta);
                drawList.add(drawConfig);
                if(drawConfig.getScreen() >= 1){
                    taskTurntableRedis.addRollRecord(turntableType, uid, drawKey);
                }
            }

            // 3、奖励下发
            int finalRecordAmount = recordAmount;
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    taskTurntableRewardDistribute(uid, turntableType, finalRecordAmount, rewardMetaList);
                }
            });

            ActorData actorData = actorDao.getActorData(uid);
            vo.setCouponBalance(userLotteryTicketsService.getUserTicketsNum(uid));
            vo.setCoinBalance(actorData.getHeartGot());
            vo.setDrawList(drawList);
            return vo;
        }
    }


    public TaskTurntableRecordVO taskTurntableRecord(String uid, int page, String turntableType) {
        TaskTurntableRecordVO recordVO = new TaskTurntableRecordVO();
        int pageSize = 20;
        if (page < 1) {
            page = 1;
        }

        int currentTime = DateHelper.getNowSeconds();
        int startTime = currentTime - 10 * 24 * 3600;
        List<TaskTurntableData> dataList = taskTurntableDao.getRecords(uid, page, pageSize, startTime, currentTime, turntableType);

        List<TaskTurntableRecordVO.Record> recordList = new ArrayList<>();
        Map<String, ResourceKeyConfigData> resConfigDataMap = resourceKeyConfigDao.findListByKeyList(TURNTABLE_RES_KEY_LIST).stream().collect(Collectors.toMap(ResourceKeyConfigData::getKey, Function.identity()));
        ResourceKeyConfigData resourceKeyConfigData = TURNTABLE_PRIMARY.equals(turntableType) ? resConfigDataMap.get(TURNTABLE_RES_KEY_LIST.get(0)) : resConfigDataMap.get(TURNTABLE_RES_KEY_LIST.get(1));
        Map<String, ResourceKeyConfigData.ResourceMeta> taskTurntableConfigMap =  resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));

        for (TaskTurntableData data : dataList) {
            TaskTurntableRecordVO.Record record = new TaskTurntableRecordVO.Record();
            String drawKey = data.getDrawKey();
            ResourceKeyConfigData.ResourceMeta resourceMeta = taskTurntableConfigMap.get(data.getDrawKey());
            if(resourceMeta == null){
                logger.error("taskTurntableRecord not find config :{}", drawKey);
                continue;
            }
            TaskTurntableConfig.CommonAwardConfig commonAwardConfig = commonAwardConfigCopyResourceMeta(resourceMeta);
            BeanUtils.copyProperties(commonAwardConfig, record);
            record.setAmount(data.getAmount());
            record.setCtime(data.getCtime());
            recordList.add(record);
        }

        recordVO.setRecordList(recordList);
        if (dataList.size() < pageSize) {
            recordVO.setNextUrl(0);
        } else {
            recordVO.setNextUrl(page + 1);
        }
        return recordVO;
    }

    /**
     * web版抽奖转盘
     */
    public WebTurntableVO webTurntableConfig(String uid) {
        WebTurntableVO vo = new WebTurntableVO();
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyHandlerService.getConfigData(WEB_TURNTABLE_RES_KEY);
        if (resourceKeyConfigData == null){
            throw new CommonH5Exception(UserHttpCode.SERVER_ERROR.getCode(), "ResourceKey not Config");
        }
        vo.setResourceConfigList(resourceKeyConfigData.getResourceMetaList());
        // 设置抽奖roll
        List<String> drawRecordList = taskTurntableRedis.getWebRollRecordList();
        List<WebTurntableVO.RollRecordData> rollRecordList = new ArrayList<>();
        for (String record : drawRecordList) {
            WebTurntableVO.RollRecordData rollRecordData = JSONObject.parseObject(record, WebTurntableVO.RollRecordData.class);
            ActorData actorData = actorDao.getActorDataFromCache(rollRecordData.getUid());
            rollRecordData.setName(actorData.getName());
            rollRecordData.setHead(actorData.getHead());
            rollRecordList.add(rollRecordData);
        }
        vo.setRollRecordList(rollRecordList);

        // 设置当天榜单
        vo.setTodayEndTime((int) (DateHelper.ARABIAN.getTodayStartTime() / 1000) + 24 * 60 * 60 - 1);
        vo.setThisWeekEndTime(DateHelper.ARABIAN.getWeekEndDateTime());
        vo.setTodayTurntableRank(getRankByRankDate(DateHelper.ARABIAN.formatDateInDay()));
        vo.setThisWeekTurntableRank(getRankByRankDate(DateHelper.ARABIAN.getWeekStartDate()));
        vo.setLastWeekTurntableRank(getRankByRankDate(DateHelper.ARABIAN.getPreWeekStartDate()));
        ActorData actorData = actorDao.getActorData(uid);
        vo.setCoinBalance(actorData.getHeartGot());
        return vo;
    }

    private List<WebTurntableVO.TurntableRank> getRankByRankDate(String rankDate) {
        Map<String, Integer> todayRankMap = taskTurntableRedis.getWebDrawRankMap(rankDate, 30);
        int rank = 1;
        List<WebTurntableVO.TurntableRank> turntableRankList = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : todayRankMap.entrySet()) {
            WebTurntableVO.TurntableRank turntableRank = new WebTurntableVO.TurntableRank();
            turntableRank.setScoreStr(String.valueOf(entry.getValue()));
            ActorData rankActor = actorDao.getActorDataFromCache(entry.getKey());
            if (null == rankActor) {
                logger.error("can not find actor. uid={}", entry.getKey());
                continue;
            }
            turntableRank.setRidData(rankActor.getRidData());
            turntableRank.setUid(entry.getKey());
            turntableRank.setName(rankActor.getName());
            turntableRank.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            turntableRank.setRank(rank);
            turntableRankList.add(turntableRank);
            rank += 1;
        }
        return turntableRankList;
    }

    public WebTurntableVO webTurntableDraw(TaskTurntableDTO dto){
        String uid = dto.getUid();
        int amount = dto.getAmount();
        if(!AMOUNT_LIST.contains(amount)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorData(uid);
        int currentTime = DateHelper.getNowSeconds();
        try (DistributeLock lock = new DistributeLock(getTaskTurntableLockKey(uid))) {
            lock.lock();
            // 1、扣费
            // 初级转盘每天有一次免费抽奖
            int changed;
            ResourceKeyConfigData resKeyData = resourceKeyConfigDao.findByKey(WEB_TURNTABLE_RES_KEY);
            Map<String, ResourceKeyConfigData.ResourceMeta> taskTurntableConfigMap = resKeyData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
            changed = amount == 1 ? 1000 : 10000;
            taskTurntableCost(uid, changed, TURNTABLE_PRIMARY);

            // 2、抽奖
            WebTurntableVO vo = new WebTurntableVO();
            List<ResourceKeyConfigData.ResourceMeta> rewardMetaList = new ArrayList<>();
            for (int i = 0; i < amount; i++) {
                String drawKey = null;
                initWebPoolSize(resKeyData);
                drawKey = taskTurntableRedis.drawWebCardKey();
                ResourceKeyConfigData.ResourceMeta resourceMeta = taskTurntableConfigMap.get(drawKey);
                if(resourceMeta == null){
                    logger.error("taskTurntableDraw not find config :{}", drawKey);
                    continue;
                }
                rewardMetaList.add(resourceMeta);
                WebTurntableVO.RollRecordData rollRecordData = new WebTurntableVO.RollRecordData();
                BeanUtils.copyProperties(resourceMeta, rollRecordData);
                rollRecordData.setUid(uid);
                rollRecordData.setCtime(currentTime);
                rollRecordData.setCostCoin(1000);
                String record = JSONObject.toJSONString(rollRecordData);
                taskTurntableRedis.addWebRollRecord(record);
                taskTurntableRedis.addWebUserRecord(uid, record);
            }
            String currentDay = DateHelper.ARABIAN.formatDateInDay();
            String currentWeek = DateHelper.ARABIAN.getWeekStartDate();
            taskTurntableRedis.incrWebDrawRankScore(uid, currentDay, changed);
            taskTurntableRedis.incrWebDrawRankScore(uid, currentWeek, changed);

            // 3、奖励下发
            doDrawPrizeRecordEvent(uid, amount, changed, rewardMetaList);
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    webTaskTurntableDistribute(uid, rewardMetaList);
                }
            });

            vo.setCoinBalance(actorData.getHeartGot());
            vo.setResourceConfigList(rewardMetaList);
            return vo;
        }
    }

    private void initWebPoolSize(ResourceKeyConfigData resKeyData){
        int poolSize = taskTurntableRedis.getWebPoolSize();
        if (poolSize > ZERO_INIT_POOL && poolSize <= LIMIT_INIT_POOL){
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    initWebTaskTurntableSize(resKeyData);
                }
            });
        }else if(poolSize <= ZERO_INIT_POOL){
            initWebTaskTurntableSize(resKeyData);
        }
    }

    public void initWebTaskTurntableSize(ResourceKeyConfigData resKeyData){
        List<String> poolList = new ArrayList<>();
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resKeyData.getResourceMetaList()) {
            int prizeSize = Integer.parseInt(resourceMeta.getRateNumber());
            String poolKey = resourceMeta.getMetaId();
            for (int i = 0; i < prizeSize; i++) {
                poolList.add(poolKey);
            }
        }
        Collections.shuffle(poolList);
        taskTurntableRedis.initWebPoolSize(poolList);
    }

    private void webTaskTurntableDistribute(String uid, List<ResourceKeyConfigData.ResourceMeta> rewardMetaList){
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : rewardMetaList) {
            resourceKeyHandlerService.sendOneResourceDataNoBroadcast(actorData, resourceMeta, MoneyTypeConstant.TASK_TURNTABLE_TYPE, MoneyTypeConstant.TASK_TURNTABLE_TITLE, MoneyTypeConstant.TASK_TURNTABLE_DESC, 0);
            if (resourceMeta.getAllRoomScreen() > 0){
                List<HighlightTextObject> list = new ArrayList<>();
                HighlightTextObject object = new HighlightTextObject();
                object.setText(actorData.getName());
                object.setHighlightColor("#FFE200");
                list.add(object);
                object = new HighlightTextObject();
                object.setText(resourceMeta.getResourceNameEn());
                object.setHighlightColor("#FFE200");
                list.add(object);
                object = new HighlightTextObject();
                object.setText(resourceMeta.getResourceNameAr());
                object.setHighlightColor("#FFE200");
                list.add(object);
                object = new HighlightTextObject();
                object.setText("GO>>");
                object.setHighlightColor("#FFE200");
                list.add(object);
                object = new HighlightTextObject();
                object.setText("هيا بنا>>");
                object.setHighlightColor("#FFE200");
                list.add(object);

                RoomNotificationMsg msg = new RoomNotificationMsg();
                msg.setUid(uid);
                msg.setUser_name(actorData.getName());
                msg.setUser_head(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                msg.setText(String.format("Congratulations to %s for winning %s in the daily activity", actorData.getName(), resourceMeta.getResourceNameEn()));
                msg.setText_ar(String.format("مبروك لـ %s لفوزه بــ %s في الالنشطة اليومي.", actorData.getName(), resourceMeta.getResourceNameAr()));
                msg.setHighlight_text(list);
                msg.setHighlight_text_ar(list);
                msg.setWeb_url(WEB_TASK_TURNTABLE_URL);
                msg.setWeb_type(1);
                roomWebSender.sendRoomWebMsg(RoomWebSender.ALL_ROOM, null, msg, false, 8621);
            }

        }
    }

    private void doDrawPrizeRecordEvent(String uid, int drawNum, int changed, List<ResourceKeyConfigData.ResourceMeta> resourceMetaList) {
        DrawPrizeRecordEvent event = new DrawPrizeRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence("Daily Task Turntable");
        event.setTicket_type(1);
        event.setCost_ticket(changed);
        event.setDraw_nums(drawNum);
        event.setDraw_success_nums(drawNum);
        event.setDraw_result(JSONObject.toJSONString(resourceMetaList));
        eventReport.track(new EventDTO(event));
    }

    /**
     * 历史记录
     */
    public WebTurntableVO webTurntableRecord(String uid, int page) {
        WebTurntableVO vo = new WebTurntableVO();
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;

        List<String> pageRecordList = taskTurntableRedis.getWebUserPageRecord(uid, start, end);
        List<WebTurntableVO.RollRecordData> recordList = new ArrayList<>();
        for (String record : pageRecordList) {
            WebTurntableVO.RollRecordData rollRecordData = JSONObject.parseObject(record, WebTurntableVO.RollRecordData.class);
            ActorData actorData = actorDao.getActorDataFromCache(rollRecordData.getUid());
            rollRecordData.setName(actorData.getName());
            rollRecordData.setHead(actorData.getHead());
            recordList.add(rollRecordData);
        }
        vo.setRecordList(recordList);
        if (pageRecordList.size() < RECORD_PAGE_SIZE) {
            vo.setNextUrl(0);
        } else {
            vo.setNextUrl(page + 1);
        }
        return vo;
    }



}
