package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.MiniTaskV2VO;
import com.quhong.data.vo.PrizeConfigVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.ActivityCommonRedis;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * miniTask
 */
@Service
public class MiniTaskService extends OtherActivityService implements TaskMsgHandler {

    private static final Logger logger = LoggerFactory.getLogger(MiniTaskService.class);
    private static final String ACTIVITY_NAME = "miniTaskV3";
    private static final String ACTIVITY_TITLE = "Mini Activity";
    private static final String ACTIVITY_ID = "6842954df9d1884dc35cd7ac"; // 67aafc8a4ce8030f9b062eee
    public static final String ALL_TASK_STATUS_KEY = "status";
    public static final String SPEND_DIAMOND_GIFT = "spend_diamond_gift";
    public static final String SEND_ROSE_GIFT = "send_ring_gift"; // 送斋月灯（斋月家族之王活动礼物）给10个用户
    public static final String RECEIVE_ROSE_GIFT = "receive_ring_gift"; // 收到斋月灯（斋月家族之王活动礼物）来自5个用户
    public static final String SEND_FANOOS_GIFT = "send_butterfly_gift"; // 送斋月灯笼竞赛活动礼物(3钻)给10个用户

    public static final List<Integer> ALL_ROUND_NUM = Arrays.asList(1, 2, 3, 4, 5);
    private static int ROUND_1_ROSE_ID = 696;
    private static int ROUND_3_RAMADAN_ID = 697;


    public static final String SEND_DATA_GIFT = "send_data_gift"; // Beating heart礼物给3个用户
    public static final String RECEIVE_DATA_GIFT = "receive_data_gift"; // 收到Beating heart礼物来自1个用户
    public static final String SEND_COFFEE_GIFT = "send_coffee_gift"; // 给5位女性用户发送mini tea礼物，ID670
//    public static final String SEND_POMEGRANATE_GIFT = "send_pomegranate_gift"; // 送石榴水果礼物给3个用户
//    public static final String RECEIVE_POMEGRANATE_GIFT = "receive_pomegranate_gift"; // 收到石榴水果礼物来自1个用户
    public static final String SEND_GOLDEN_GIFT = "send_golden_gift"; // 送红玫瑰礼物给10个用户
//    public static final String SEND_MINT_TEA_GIFT = "send_mint_tea_gift"; // 给3个用户发送薄荷茶礼物

    private static int ROUND_1_DATA_ID = 25;
    private static int ROUND_2_COFFEE_ID = 670;
//    private static int ROUND_3_POMEGRANATE_ID = 1112;
    private static int ROUND_4_GOLDEN_ID = 110;
//    private static int ROUND_5_MINT_TEA_ID = 670;

    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/MiniTask3/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/MiniTask3/?activityId=%s", ACTIVITY_ID);
//    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/RamadanMiniTask3/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/RamadanMiniTask3/?activityId=%s", ACTIVITY_ID);


    /**
     * 专属奖励-斋月版本
     */
//    public static final Map<Integer, String> ROUND_REWARD_MAIN_KEY_MAP = new HashMap<Integer, String>() {
//        {
//            put(1, "RamadanMiniActivityTask1");// MiniActivityTask1
//            put(2, "RamadanMiniActivityTask2");
//            put(3, "RamadanMiniActivityTask3");
//            put(4, "RamadanMiniActivityTask4");
//            put(5, "RamadanMiniActivityTask5");
//            put(6, "RamadanMiniActivityLantern5");
//        }
//    };

    /**
     * 专属奖励-普通版本
     */
    public static final Map<Integer, String> ROUND_REWARD_MAIN_KEY_MAP = new HashMap<Integer, String>() {
        {
            put(1, "MiniActivityTask1");// MiniActivityTask1
            put(2, "MiniActivityTask2");
            put(3, "MiniActivityTask3");
            put(4, "MiniActivityTask4");
            put(5, "MiniActivityTask5");
        }
    };
    /**
     * icon
     */
    public static final Map<Integer, String> ROUND_ICON_MAP = new HashMap<Integer, String>() {
        {
            put(1, "https://cdn3.qmovies.tv/youstar/op_1744019716_01.webp"); // https://cdn3.qmovies.tv/youstar/op_1744256540_yulan.webp
            put(2, "https://cdn3.qmovies.tv/youstar/op_1744019716_02.webp"); // https://cdn3.qmovies.tv/youstar/op_1744256410_yulan.mp4
            put(3, "https://cdn3.qmovies.tv/youstar/op_1744019716_03.webp"); // https://cdn3.qmovies.tv/youstar/op_1744256445_yulan.webp
            put(4, "https://cdn3.qmovies.tv/youstar/op_1744019716_05.webp"); // https://cdn3.qmovies.tv/youstar/op_1744254502_op_1694745751_goldenrose.png
            put(5, "https://cdn3.qmovies.tv/youstar/op_1744019716_04.webp"); // https://cdn3.qmovies.tv/youstar/op_1744254371_yulan.mp4
        }
    };

    /**
     * icon_d 动图
     */
    public static final Map<Integer, String> ROUND_ICON_D_MAP = new HashMap<Integer, String>() {
        {
            put(1, "https://cdn3.qmovies.tv/youstar/op_1744256540_yulan.webp"); //
            put(2, "https://cdn3.qmovies.tv/youstar/op_1744256410_yulan.mp4"); //
            put(3, "https://cdn3.qmovies.tv/youstar/op_1744256445_yulan.webp"); //
            put(4, "https://cdn3.qmovies.tv/youstar/op_1744254502_op_1694745751_goldenrose.png"); //
            put(5, "https://cdn3.qmovies.tv/youstar/op_1744254371_yulan.mp4"); //
        }
    };

    private static Map<String, String> ROUND_TIME_MAP = new HashMap<String, String>() {
        {
            put("1", "2025-06-10&2025-06-11");
            put("2", "2025-06-13&2025-06-13");
            put("3", "2025-06-14&2025-06-15");
            put("4", "2025-06-17&2025-06-17");
            put("5", "2025-06-18&2025-06-19");
        }
    };

//    private static Map<String, String> ROUND_TIME_MAP = new HashMap<String, String>() {
//        {
//            put("1", "2025-05-08&2025-05-09");
//            put("2", "2025-05-08&2025-05-09");
//            put("3", "2025-05-08&2025-05-09");
//            put("4", "2025-05-08&2025-05-09");
//            put("5", "2025-05-08&2025-05-09");
//        }
//    };

    public static final String MINI_TASK_ROUND_ICON_KEY = "MiniTaskRoundIcon"; //
    // 去重任务
    private static final List<String> TASK_SET_LIST = Arrays.asList(SEND_DATA_GIFT, RECEIVE_DATA_GIFT, SPEND_DIAMOND_GIFT,
            CommonMqTaskConstant.COMMENT_MOMENT, CommonMqTaskConstant.ADD_FRIEND,
            CommonMqTaskConstant.LIKE_MOMENT, SEND_COFFEE_GIFT,
            SEND_GOLDEN_GIFT, CommonMqTaskConstant.INVITE_USER_ON_MIC_ALL);

    private static final List<String> TASK_ALL_LIST = Arrays.asList(
            CommonMqTaskConstant.ON_MIC_TIME, // 1-5
            CommonMqTaskConstant.COMMENT_MOMENT, // 1,3
            CommonMqTaskConstant.PLAY_UMO, // 3
            CommonMqTaskConstant.ADD_FRIEND, // 1,3
            CommonMqTaskConstant.WATCH_VIDEO_TIME, // 2
            CommonMqTaskConstant.POST_MOMENT, // 2
            CommonMqTaskConstant.LIKE_MOMENT, // 2
            CommonMqTaskConstant.INVITE_USER_ON_MIC_ALL, // 4
            CommonMqTaskConstant.SEND_MOMENT_GIFT, // 4,5
            CommonMqTaskConstant.PLAY_MONSTER_CRUSH // 5
    );

    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private MiniTaskService miniTaskService;
    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private WhiteTestDao whiteTestDao;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            ROUND_1_ROSE_ID = 110;
            ROUND_3_RAMADAN_ID = 847;

            ROUND_1_DATA_ID = 110;
            ROUND_2_COFFEE_ID = 253;
//            ROUND_3_POMEGRANATE_ID = 847;
            ROUND_4_GOLDEN_ID = 834;
//            ROUND_5_MINT_TEA_ID = 748;

            ROUND_TIME_MAP = new HashMap<String, String>() {
                {
                    put("1", "2025-06-06&2025-06-07");
                    put("2", "2025-06-06&2025-06-07");
                    put("3", "2025-06-06&2025-06-07");
                    put("4", "2025-06-06&2025-06-07");
                    put("5", "2025-06-06&2025-06-07");
                }
            };
        }
    }

    private String getMiniTaskConfigKey(String activityId) {
        return String.format("miniTaskConfig:%s", activityId);
    }

    private String getDeviceSetKey(String activityId, Integer roundNum) {
        return String.format("miniTaskDevice:%s:%s", activityId, roundNum);
    }

    private String getMiniTaskUserDataKey(String activityId, String uid, Integer roundNum) {
        return String.format("miniTaskData:%s:%s:%s", activityId, uid, roundNum);
    }

    private String getMiniTaskSetDataKey(String activityId, String uid, int roundNum, String taskKey) {
        return String.format("miniTaskSet:%s:%s:%s:%s", activityId, uid, roundNum, taskKey);
    }

    private String getMiniTaskSpendDiamondKey(String activityId, String uid, Integer roundNum) {
        return String.format("miniTaskSpendDiamond:%s:%s:%s", activityId, uid, roundNum);
    }


    @Cacheable(value = "getMiniTaskConfigMap", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public Map<String, String> getMiniTaskConfigMap(String activityId) {
        return activityCommonRedis.getCommonHashAllMapStr(getMiniTaskConfigKey(activityId));
    }

    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();

        if (!TASK_ALL_LIST.contains(item)) {
            return;
        }
        handleTaskMqMsg(data);

    }

    public MiniTaskV2VO miniTaskConfig(String uid, String activityId) {
        MiniTaskV2VO vo = new MiniTaskV2VO();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null || StringUtils.isEmpty(actorData.getTn_id())) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        String tnId = actorData.getTn_id();

        List<MiniTaskV2VO.MiniTask> miniTaskVOList = new ArrayList<>();
        List<ActivityCommonConfig.TaskConfig> miniTaskList = activityCommonConfig.getMiniTaskList();
        Map<Integer, List<ActivityCommonConfig.TaskConfig>> miniTaskGroupMap = miniTaskList.stream().collect(Collectors.groupingBy(ActivityCommonConfig.TaskConfig::getRoundNum));
//        Map<String, String> miniTaskConfigMap = miniTaskService.getMiniTaskConfigMap(activityId);


        for (Map.Entry<Integer, List<ActivityCommonConfig.TaskConfig>> entry : miniTaskGroupMap.entrySet()) {
            Integer roundNum = entry.getKey();
            MiniTaskV2VO.MiniTask miniTask = new MiniTaskV2VO.MiniTask();
            // 设置时间
//            String timeConfig = miniTaskConfigMap.getOrDefault(String.valueOf(roundNum), "");
            String timeConfig = ROUND_TIME_MAP.getOrDefault(String.valueOf(roundNum), "");
            if (StringUtils.isEmpty(timeConfig)) {
                continue;
            }
            String[] splitArray = timeConfig.split("&");
            miniTask.setStartTime(DateHelper.DEFAULT.stringDateToStampSecond(splitArray[0])); // yyyy-MM-dd
            miniTask.setEndTime((int) (DateHelper.DEFAULT.stringDateToStampSecond(splitArray[1]) + TimeUnit.DAYS.toSeconds(1)));
            miniTask.setStartStrTime(splitArray[0]);
            miniTask.setEndStrTime(splitArray[1]);
//            miniTask.setEndStrTime(DateHelper.DEFAULT.formatDateInDay(new Date((miniTask.getEndTime() - 1) * 1000L)));
            // 设置当前设备是否已领取奖励
            String roundDeviceKey = getDeviceSetKey(activityId, roundNum);
            miniTask.setDeviceAward(activityCommonRedis.isCommonSetData(roundDeviceKey, tnId));

            // 设置任务完成进度
            List<ActivityCommonConfig.TaskConfig> roundTaskList = entry.getValue();
            List<ActivityCommonConfig.TaskConfig> userTaskConfigList = new ArrayList<>();
            int finishNum = 0;
            for (ActivityCommonConfig.TaskConfig taskConfig : roundTaskList) {
                ActivityCommonConfig.TaskConfig userTaskConfig = new ActivityCommonConfig.TaskConfig();
                BeanUtils.copyProperties(taskConfig, userTaskConfig);

                String userDataKey = getMiniTaskUserDataKey(activityId, uid, roundNum);
                Map<String, Integer> userTaskMap = activityCommonRedis.getCommonHashAll(userDataKey);
                userTaskConfig.setCurrentProcess(Math.min(userTaskMap.getOrDefault(taskConfig.getTaskKey(), 0), taskConfig.getTotalProcess()));
                userTaskConfigList.add(userTaskConfig);
                if (userTaskConfig.getCurrentProcess() >= userTaskConfig.getTotalProcess()) {
                    finishNum += 1;
                }
            }

            if (finishNum >= roundTaskList.size()) {
                miniTask.setAllTaskFinish(1);
            }

            miniTask.setTaskList(userTaskConfigList);


            Map<Integer, ResourceKeyConfigData.ResourceMeta> roundMap = new HashMap<>();
            ResourceKeyConfigData resourceKeyConfigData = resourceKeyHandlerService.getConfigData(MINI_TASK_ROUND_ICON_KEY);
            if (resourceKeyConfigData != null && !CollectionUtils.isEmpty(resourceKeyConfigData.getResourceMetaList())) {
                int count = 1;
                for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceKeyConfigData.getResourceMetaList()) {
                    roundMap.put(count, resourceMeta);
                    count++;
                }
            }
            ResourceKeyConfigData.ResourceMeta itemResourceMeta = roundMap.get(roundNum);
            if (itemResourceMeta != null) {
                miniTask.setRoundNumIcon(itemResourceMeta.getResourceIcon());
                miniTask.setRoundNumDIcon(itemResourceMeta.getResourcePreview());
//                miniTask.setRoundNumIcon(ROUND_ICON_MAP.getOrDefault(roundNum, ""));
//                miniTask.setRoundNumDIcon(ROUND_ICON_D_MAP.getOrDefault(roundNum, ""));
            }
            miniTaskVOList.add(miniTask);
        }

        vo.setMiniTaskList(miniTaskVOList);
        return vo;

    }


    public void distributionMiniTask(String userDataKey, String uid, int roundNum) {
        Map<String, Integer> miniTaskMap = activityCommonRedis.getCommonHashAll(userDataKey);
        int status = miniTaskMap.getOrDefault(ALL_TASK_STATUS_KEY, 0);
        if (status > 0) {
            return;
        }

        Map<Integer, List<ActivityCommonConfig.TaskConfig>> miniTaskGroupMap = activityCommonConfig.getMiniTaskList().stream().collect(Collectors.groupingBy(ActivityCommonConfig.TaskConfig::getRoundNum));
        List<ActivityCommonConfig.TaskConfig> taskConfigList = miniTaskGroupMap.get(roundNum);
        if (taskConfigList == null) {
            return;
        }

        Map<String, Integer> userTaskMap = activityCommonRedis.getCommonHashAll(userDataKey);
        for (ActivityCommonConfig.TaskConfig config : taskConfigList) {
            int finishProcess = userTaskMap.getOrDefault(config.getTaskKey(), 0);
            if (finishProcess < config.getTotalProcess()) {
                return;
            }
        }

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null || StringUtils.isEmpty(actorData.getTn_id())) {
            logger.info("distributionMiniTask not find user or tn_id empty: {}", uid);
            return;
        }


        // 同一设备检测
        String tnId = actorData.getTn_id();
        String roundDeviceKey = getDeviceSetKey(ACTIVITY_ID, roundNum);
        if (activityCommonRedis.isCommonSetData(roundDeviceKey, tnId) >= 1) {
            logger.info("distributionMiniTask tn_id is get: {}", tnId);
            return;
        }

        activityCommonRedis.setCommonHashNum(userDataKey, ALL_TASK_STATUS_KEY, 1);
        activityCommonRedis.addCommonSetData(roundDeviceKey, tnId);

        String resKey = ROUND_REWARD_MAIN_KEY_MAP.getOrDefault(roundNum, "");
        handleRes(uid, resKey, 1);

//        if (ServerConfig.isProduct()) {
//            String resKey = ROUND_REWARD_MAIN_KEY_MAP.getOrDefault(roundNum, "");
//            handleRes(uid,resKey,1);
//            switch (roundNum) {
//                case 1:
//                case 3:
//                case 5:
//                    distributionService.sendRewardResource(uid, 567, ActivityRewardTypeEnum.getEnumByName("mic"), 3, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
//                    distributionService.sendRewardResource(uid, 219, ActivityRewardTypeEnum.getEnumByName("buddle"), 3, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
//                    distributionService.sendRewardResource(uid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 100, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
//                    break;
//                case 2:
//                    distributionService.sendRewardResource(uid, 317, ActivityRewardTypeEnum.getEnumByName("background"), 3, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
//                    distributionService.sendRewardResource(uid, 253, ActivityRewardTypeEnum.getEnumByName("gift"), 3, 1, ACTIVITY_NAME, ACTIVITY_NAME, 0);
//                    distributionService.sendRewardResource(uid, 90, ActivityRewardTypeEnum.getEnumByName("gift"), 3, 1, ACTIVITY_NAME, ACTIVITY_NAME, 0);
//                    break;
//                case 4:
//                    distributionService.sendRewardResource(uid, 317, ActivityRewardTypeEnum.getEnumByName("background"), 3, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
//                    distributionService.sendRewardResource(uid, 670, ActivityRewardTypeEnum.getEnumByName("gift"), 3, 1, ACTIVITY_NAME, ACTIVITY_NAME, 0);
//                    distributionService.sendRewardResource(uid, 672, ActivityRewardTypeEnum.getEnumByName("gift"), 3, 1, ACTIVITY_NAME, ACTIVITY_NAME, 0);
//                    break;
//            }
//        }


        // 大奖下发
//        int totalNum = 0;
//        for (Integer num : ALL_ROUND_NUM) {
//            String roundDataKey = getMiniTaskUserDataKey(ACTIVITY_ID, uid, num);
//            Map<String, Integer> roundTaskMap = activityCommonRedis.getCommonHashAll(roundDataKey);
//            totalNum += roundTaskMap.getOrDefault(ALL_TASK_STATUS_KEY, 0);
//        }

//        if (totalNum == 5) {
//            distributionService.sendRewardResource(uid, 699, ActivityRewardTypeEnum.getEnumByName("gift"), 15, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
//            resKey = ROUND_REWARD_MAIN_KEY_MAP.getOrDefault(6, "");
//            handleRes(uid, resKey, 1);
//        }

    }

    public void handleMiniTaskMap(String uid, String roomId, String targetId, String
            taskKey, ActivityCommonConfig.TaskConfig taskConfig, int incNum, boolean setFlag) {

        synchronized (stringPool.intern(ACTIVITY_NAME + uid)) {
            int roundNum = taskConfig.getRoundNum();
            String userDataKey = getMiniTaskUserDataKey(ACTIVITY_ID, uid, roundNum);

            // 1、某些任务逻判断
            // 视频任务要在自己的房间
            if (CommonMqTaskConstant.WATCH_VIDEO_TIME.equals(taskKey) && !RoomUtils.formatRoomId(uid).equals(roomId)) {
                return;
            }

            if (CommonMqTaskConstant.SEND_ROOM_MSG.equals(taskKey) || SPEND_DIAMOND_GIFT.equals(taskKey)) {
                targetId = roomId;
            }

            // 2、该任务完成状态
            Map<String, Integer> miniTaskMap = activityCommonRedis.getCommonHashAll(userDataKey);
            Integer status = miniTaskMap.getOrDefault(ALL_TASK_STATUS_KEY, 0);
            if (status > 0) {
                return;
            }

            // 3、更新任务进度
            int totalProcess = taskConfig.getTotalProcess();
            int currentProcess = miniTaskMap.getOrDefault(taskKey, 0);
            if (currentProcess >= totalProcess) {
                return;
            }

            if (SPEND_DIAMOND_GIFT.equals(taskKey) && !StringUtils.isEmpty(roomId)) {
                String spendDiamondHashKey = getMiniTaskSpendDiamondKey(ACTIVITY_ID, uid, roundNum);
                int afterNum = activityCommonRedis.incCommonHashNum(spendDiamondHashKey, roomId, incNum);
                if (afterNum < 100) {
                    return;
                }
                incNum = 1;
            }

            // 4、任务去重状态
            if (setFlag) {
                String userTaskSetKey = getMiniTaskSetDataKey(ACTIVITY_ID, uid, roundNum, taskKey);
                int setStatus = activityCommonRedis.isCommonSetData(userTaskSetKey, targetId);
                if (setStatus > 0) {
                    return;
                }
                activityCommonRedis.addCommonSetData(userTaskSetKey, targetId);
            }


            int afterProcess = currentProcess + incNum;
            activityCommonRedis.setCommonHashNum(userDataKey, taskKey, afterProcess);

            // 5、 奖励下发
            distributionMiniTask(userDataKey, uid, roundNum);
        }
    }

    public void handleTaskMqMsg(CommonMqTopicData mqTopicData) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (activityData == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }

        String uid = mqTopicData.getUid();
        String roomId = mqTopicData.getRoomId();
        String aid = mqTopicData.getAid();
        String item = mqTopicData.getItem();
        int value = mqTopicData.getValue();

        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTestUser = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTestUser) {
                // 灰度测试,只统计测试用户的
                return;
            }
        }

        int currentTime = DateHelper.getNowSeconds();
//        Map<String, String> miniTaskConfigMap = miniTaskService.getMiniTaskConfigMap(ACTIVITY_ID);
        List<ActivityCommonConfig.TaskConfig> miniTaskList = activityCommonConfig.getMiniTaskList();
        for (ActivityCommonConfig.TaskConfig taskConfig : miniTaskList) {
            if (!taskConfig.getTaskKey().equals(item)) {
                continue;
            }


            // 1、时间范围判断
            int roundNum = taskConfig.getRoundNum();
//            String timeConfig = miniTaskConfigMap.getOrDefault(String.valueOf(roundNum), "");
//            if (StringUtils.isEmpty(timeConfig)) {
//                continue;
//            }
//            String[] splitArray = timeConfig.split("-");
//            int startTime = Integer.parseInt(splitArray[0]);
//            int endTime = Integer.parseInt(splitArray[1]);

            String timeConfig = ROUND_TIME_MAP.getOrDefault(String.valueOf(roundNum), "");
            if (StringUtils.isEmpty(timeConfig)) {
                continue;
            }
            String[] splitArray = timeConfig.split("&");
            int startTime = DateHelper.DEFAULT.stringDateToStampSecond(splitArray[0]); // yyyy-MM-dd
            int endTime = (int) (DateHelper.DEFAULT.stringDateToStampSecond(splitArray[1]) + TimeUnit.DAYS.toSeconds(1));
            if (currentTime < startTime || currentTime > endTime) {
                continue;
            }

            boolean setFlag = TASK_SET_LIST.contains(item);
            String targetId = CommonMqTaskConstant.COMMENT_MOMENT.equals(item) || CommonMqTaskConstant.LIKE_MOMENT.equals(item)
                    || CommonMqTaskConstant.ADD_FRIEND.equals(item) || CommonMqTaskConstant.INVITE_USER_ON_MIC_ALL.equals(item)
                    ? mqTopicData.getHandleId() : aid;
            miniTaskService.handleMiniTaskMap(uid, roomId, targetId, item, taskConfig, value, setFlag);

        }

    }


    // LlluminateYouStarSendGiftHandler 调用
    public void handleGiftMqMsg(SendGiftData giftData) {
//        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
//        if (activityData == null) {
//            return;
//        }

        String fromUid = giftData.getFrom_uid();
        String roomId = giftData.getRoomId();
        int giftId = giftData.getGid();
        int totalNumber = giftData.getNumber() * giftData.getAid_list().size();
        int totalBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
        if (StringUtils.isEmpty(roomId)) {
            return;
        }

        // 消耗钻石数
        handleTaskMqMsg(new CommonMqTopicData(fromUid, roomId, "", "", SPEND_DIAMOND_GIFT, totalBeans));

        if (ROUND_1_DATA_ID == giftId) {
            for (String aid : giftData.getAid_list()) {
                handleTaskMqMsg(new CommonMqTopicData(fromUid, roomId, aid, "", SEND_DATA_GIFT, 1));
                handleTaskMqMsg(new CommonMqTopicData(aid, roomId, fromUid, "", RECEIVE_DATA_GIFT, 1));
            }
        } else if (ROUND_2_COFFEE_ID == giftId) {
            for (String aid : giftData.getAid_list()) {
                ActorData aidData = actorDao.getActorDataFromCache(aid);
                if (aidData.getFb_gender() == 2) {
                    handleTaskMqMsg(new CommonMqTopicData(fromUid, roomId, aid, "", SEND_COFFEE_GIFT, 1));
                }
            }
        }
//        else if (ROUND_3_POMEGRANATE_ID == giftId) {
//            for (String aid : giftData.getAid_list()) {
//                handleTaskMqMsg(new CommonMqTopicData(fromUid, roomId, aid, "", SEND_POMEGRANATE_GIFT, 1));
//                handleTaskMqMsg(new CommonMqTopicData(aid, roomId, fromUid, "", RECEIVE_POMEGRANATE_GIFT, 1));
//            }
//        }
        else if (ROUND_4_GOLDEN_ID == giftId) {
            for (String aid : giftData.getAid_list()) {
                handleTaskMqMsg(new CommonMqTopicData(fromUid, roomId, aid, "", SEND_GOLDEN_GIFT, 1));
            }
        }
//        else if (ROUND_5_MINT_TEA_ID == giftId) {
//            for (String aid : giftData.getAid_list()) {
//                handleTaskMqMsg(new CommonMqTopicData(fromUid, roomId, aid, "", SEND_MINT_TEA_GIFT, 1));
//            }
//        }


    }


    private void handleRes(String aid, String resKey, int type) {
        String eventTitle = ACTIVITY_TITLE;
        resourceKeyHandlerService.sendResourceData(aid, resKey,
                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
    }
}
