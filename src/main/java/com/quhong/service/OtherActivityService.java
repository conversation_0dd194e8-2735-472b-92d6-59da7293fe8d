package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventReport;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityConstant;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.data.dto.SendChatMsgDTO;
import com.quhong.data.vo.*;
import com.quhong.datas.HttpResult;
import com.quhong.dto.InnerPublishMomentDTO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.MsgType;
import com.quhong.enums.ResTypeEnum;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMomentService;
import com.quhong.feign.IMsgService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.mq.MqSenderService;
import com.quhong.mq.ResourceDeliveryService;
import com.quhong.msg.chat.OfficialPushMsg;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.room.RoomCommonScrollMsg;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.redis.ActivityCommonRedis;
import com.quhong.redis.ActivityOtherRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.GiftsMqVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;


@Component
public class OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(OtherActivityService.class);

    // 5cb6fc80644f8e002a90cfc2 是测试服账号
    private static final List<String> ALL_LOOK_USER = Arrays.asList("5ddad30b0ca307a634ec01cb", "5cdf784961d047a4adf44064", "5cb6fc80644f8e002a90cfc2");
    private static final Interner<String> stringPool = Interners.newWeakInterner();


    @Resource
    protected OtherRankingActivityDao otherRankingActivityDao;
    @Resource
    protected OtherActivityService otherActivityService;
    @Resource
    protected ActivityOtherRedis activityOtherRedis;
    @Resource
    protected MongoRoomDao mongoRoomDao;
    @Resource
    protected ActorDao actorDao;
    @Resource
    protected HeartRecordDao heartRecordDao;
    @Resource
    private ResourceDeliveryService resourceDeliveryService;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    protected OfficialDao officialDao;
    @Resource
    protected NoticeNewDao noticeNewDao;
    @Autowired
    protected RoomWebSender roomWebSender;
    @Resource
    protected ActivityUtilService activityUtilService;
    @Resource
    private IMomentService iMomentService;
    @Resource
    protected ActivityCommonRedis activityCommonRedis;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    protected BadgeDao badgeDao;
    @Resource
    protected VipInfoDao vipInfoDao;
    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    protected IMsgService iMsgService;
    @Resource
    protected EventReport eventReport;
    @Resource
    protected ResourceKeyConfigDao resourceKeyConfigDao;

    /**
     * 获取正在运行的冲榜活动
     */
    public List<OtherRankingActivityData> getOtherRankingActivities() {
        int nowSeconds = DateHelper.getNowSeconds();
        List<OtherRankingActivityData> activityList = otherActivityService.getOtherRankingActivitiesFromCache();
        return activityList.stream().filter(a -> a.getEndTime() > nowSeconds && a.getStartTime() <= nowSeconds).collect(Collectors.toList());
    }

    @Cacheable(value = "getOtherRankingActivityFromCache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<OtherRankingActivityData> getOtherRankingActivitiesFromCache() {
        List<OtherRankingActivityData> activityList = otherRankingActivityDao.getRankingActivities();
        logger.info("getOtherRankingActivityFromCache size={}", activityList.size());
        return activityList;
    }

    @Cacheable(value = "otherRankActivity", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public OtherRankingActivityData getOtherRankingActivity(String activityId) {
        if (StringUtils.isEmpty(activityId)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        OtherRankingActivityData data = otherRankingActivityDao.findData(activityId);
        if (null == data) {
            logger.error("cannot find ranking activity activityId={}", activityId);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return data;
    }

    @Cacheable(value = "otherRankActivityList", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<OtherRankingActivityData> getOtherRankingActivityList(List<String> activityIdList) {
        if (CollectionUtils.isEmpty(activityIdList)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        List<OtherRankingActivityData> dataList = otherRankingActivityDao.findDataList(activityIdList);
        if (CollectionUtils.isEmpty(dataList)) {
            logger.error("cannot find ranking activity activityId={}", activityIdList);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return dataList;
    }

    @Cacheable(value = "getOtherRankingActivityNull", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public OtherRankingActivityData getOtherRankingActivityNull(String activityId) {
        if (StringUtils.isEmpty(activityId)) {
            return null;
        }
        return otherRankingActivityDao.findData(activityId);
    }

    public OtherRankingActivityData checkActivityTime(String activityId) {
        int curTime = DateHelper.getNowSeconds();
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        int startTime = activityData.getStartTime();
        int endTime = activityData.getEndTime();
        if (curTime < startTime || curTime > endTime) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }
        return activityData;
    }

    public boolean inActivityTime(String activityId) {
        int curTime = DateHelper.getNowSeconds();
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        int startTime = activityData.getStartTime();
        int endTime = activityData.getEndTime();
        return curTime >= startTime && curTime < endTime;
    }

    /**
     * 获取排行榜
     */
    protected List<OtherRankingListVO> getOtherRankingListVO(String activityId, int rankType, int rankNum, int roundNum, int gloryInfo) {
        List<OtherRankingListVO> rankingList = new ArrayList<>();

        Map<String, Integer> rankingMap = activityOtherRedis.getOtherRankingMap(activityId, rankType, rankNum, roundNum);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO rankingListVO = new OtherRankingListVO();
            rankingListVO.setScore(entry.getValue());
            if (ActivityConstant.ROOM_RANK == rankType) {
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(entry.getKey());
                rankingListVO.setName(roomData.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
                rankingListVO.setRoomId(entry.getKey());
                ActorData rankActor = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(entry.getKey()));
                if (null == rankActor) {
                    logger.error("can not find actor. uid={}", entry.getKey());
                    continue;
                }
                rankingListVO.setRidData(rankActor.getRidData());
            } else {
                ActorData rankActor = actorDao.getActorDataFromCache(entry.getKey());
                if (null == rankActor) {
                    logger.error("can not find actor. uid={}", entry.getKey());
                    continue;
                }
                rankingListVO.setRidData(rankActor.getRidData());
                rankingListVO.setUid(entry.getKey());
                rankingListVO.setName(rankActor.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
                if (gloryInfo > 0) {
                    rankingListVO.setBadgeList(badgeDao.getBadgeList(entry.getKey()));
                    rankingListVO.setVipLevel(vipInfoDao.getIntVipLevel(entry.getKey()));
                }

            }
            rankingListVO.setRank(rank);
            rankingList.add(rankingListVO);
            rank += 1;
        }
        return rankingList;
    }

    /**
     * 获取每日排行榜
     */
    private List<OtherRankingListVO> getOtherRankingDailyListVO(String activityId, int rankType, int rankNum, String dailyNum, int roundNum) {
        List<OtherRankingListVO> rankingList = new ArrayList<>();

        Map<String, Integer> rankingMap = activityOtherRedis.getOtherRankingDailyMap(activityId, rankType, rankNum, dailyNum, roundNum);
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO rankingListVO = new OtherRankingListVO();
            rankingListVO.setScore(entry.getValue());
            if (ActivityConstant.ROOM_RANK == rankType) {
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(entry.getKey());
                rankingListVO.setName(roomData.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
                rankingListVO.setRoomId(entry.getKey());
            } else {
                ActorData rankActor = actorDao.getActorDataFromCache(entry.getKey());
                if (null == rankActor) {
                    logger.error("can not find actor. uid={}", entry.getKey());
                    continue;
                }
                rankingListVO.setName(rankActor.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            }
            rankingList.add(rankingListVO);
        }
        return rankingList;
    }

    /**
     * 获取隐藏排行榜
     */
    private List<HiddenRankingListVO> getHiddenRankingListVO(String uid, String activityId, int rankType, int roundNum) {
        Map<Integer, HiddenRankingListVO> hiddenRankingMap = new HashMap<>();
        Map<String, Integer> scoreMap = new HashMap<>();
        String matchId = rankType == ActivityConstant.ROOM_RANK ? "r:" + uid : uid;


        Map<String, Integer> rankingMap = activityOtherRedis.getOtherRankingMap(activityId, rankType, 10, roundNum);
        int rank = 1;
        int flag = -1;

        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            HiddenRankingListVO rankingListVO = new HiddenRankingListVO();
            rankingListVO.setScore(entry.getValue().toString());
            rankingListVO.setUid(entry.getKey());
            if (ActivityConstant.ROOM_RANK == rankType) {
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(entry.getKey());
                rankingListVO.setName(roomData.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
            } else {
                ActorData rankActor = actorDao.getActorDataFromCache(entry.getKey());
                if (null == rankActor) {
                    logger.error("can not find actor. uid={}", entry.getKey());
                    continue;
                }
                rankingListVO.setName(rankActor.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            }

            if (entry.getKey().equals(matchId)) {
                flag = rank;

            } else {
                if (!ALL_LOOK_USER.contains(uid)) {
                    rankingListVO.setScore("*****");
                }


            }

            hiddenRankingMap.put(rank, rankingListVO);
            scoreMap.put(entry.getKey(), entry.getValue());

            rank += 1;

        }

        if (flag != -1) {
            HiddenRankingListVO beforeMap = hiddenRankingMap.get(flag - 1);
            if (beforeMap != null) {
                beforeMap.setScore(scoreMap.get(beforeMap.getUid()).toString());
            }

            HiddenRankingListVO afterMap = hiddenRankingMap.get(flag + 1);
            if (afterMap != null) {
                afterMap.setScore(scoreMap.get(afterMap.getUid()).toString());
            }
        }

        return new ArrayList<>(hiddenRankingMap.values());
    }


    /**
     * 排行榜个人数据
     */
    protected OtherMyRankVO getOtherMyRank(String activityId, String uid, int rankType, int roundNum, int gloryInfo) {
        OtherMyRankVO myRank = new OtherMyRankVO();
        if (ActivityConstant.ROOM_RANK == rankType) {
            MongoRoomData data = mongoRoomDao.getDataFromCache(RoomUtils.formatRoomId(uid));
            if (null == data) {
                logger.error("can not find room. uid={}", uid);
                return myRank;
            }
            myRank.setName(data.getName());
            myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(data.getHead()));
            myRank.setRoomId(data.getRid());
            fillOtherMyRank(myRank, activityId, data.getRid(), rankType, roundNum);
        } else {
            ActorData actor = actorDao.getActorDataFromCache(uid);
            if (null == actor) {
                logger.error("can not find actor. uid={}", uid);
                return myRank;
            }
            myRank.setUid(uid);
            myRank.setName(actor.getName());
            myRank.setGender(actor.getFb_gender());
            myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actor.getHead()));
            myRank.setCountry(actor.getCountry());
            myRank.setRidData(actor.getRidData());
            if (gloryInfo > 0) {
                myRank.setBadgeList(badgeDao.getBadgeList(uid));
                myRank.setVipLevel(vipInfoDao.getIntVipLevel(uid));
            }
            fillOtherMyRank(myRank, activityId, uid, rankType, roundNum);
        }
        return myRank;
    }

    private void fillOtherMyRank(OtherMyRankVO myRank, String activityId, String uid, int rankType, int roundNum) {

        if (rankType == ActivityConstant.SEND_RANK) {
            myRank.setSendingScore(activityOtherRedis.getOtherRankingScore(activityId, uid, rankType, roundNum));
            myRank.setReceiveScore(activityOtherRedis.getOtherRankingScore(activityId, uid, ActivityConstant.RECEIVE_RANK, roundNum));
            myRank.setRank(activityOtherRedis.getOtherRank(activityId, uid, rankType, roundNum));
        } else if (rankType == ActivityConstant.RECEIVE_RANK) {
            myRank.setReceiveScore(activityOtherRedis.getOtherRankingScore(activityId, uid, rankType, roundNum));
            myRank.setSendingScore(activityOtherRedis.getOtherRankingScore(activityId, uid, ActivityConstant.SEND_RANK, roundNum));
            myRank.setRank(activityOtherRedis.getOtherRank(activityId, uid, rankType, roundNum));
        } else if (rankType == ActivityConstant.ROOM_RANK) {
            myRank.setRoomScore(activityOtherRedis.getOtherRankingScore(activityId, uid, rankType, roundNum));
            myRank.setRank(activityOtherRedis.getOtherRank(activityId, uid, rankType, roundNum));
        } else if (rankType == ActivityConstant.SEND_RECEIVE_RANK) {
            myRank.setSendingScore(activityOtherRedis.getOtherRankingScore(activityId, uid, rankType, roundNum));
            myRank.setRank(activityOtherRedis.getOtherRank(activityId, uid, rankType, roundNum));
        }

    }


    /**
     * 排行榜每日个人数据
     */
    protected OtherMyRankVO getOtherDailyMyRank(String activityId, String uid, int rankType, String dailyNum, int roundNum) {
        OtherMyRankVO myRank = new OtherMyRankVO();
        if (ActivityConstant.ROOM_RANK == rankType) {
            MongoRoomData data = mongoRoomDao.getDataFromCache(RoomUtils.formatRoomId(uid));
            if (null == data) {
                logger.error("can not find room. uid={}", uid);
                return myRank;
            }
            myRank.setName(data.getName());
            myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(data.getHead()));
            fillOtherDailyMyRank(myRank, activityId, data.getRid(), rankType, dailyNum, roundNum);
        } else {
            ActorData actor = actorDao.getActorDataFromCache(uid);
            if (null == actor) {
                logger.error("can not find actor. uid={}", uid);
                return myRank;
            }
            myRank.setName(actor.getName());
            myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actor.getHead()));
            fillOtherDailyMyRank(myRank, activityId, uid, rankType, dailyNum, roundNum);
        }
        return myRank;
    }

    private void fillOtherDailyMyRank(OtherMyRankVO myRank, String activityId, String uid, int rankType, String dailyNum, int roundNum) {

        if (rankType == ActivityConstant.SEND_RANK) {
            myRank.setSendingScore(activityOtherRedis.getOtherRankingDailyScore(activityId, uid, rankType, dailyNum, roundNum));
            myRank.setReceiveScore(activityOtherRedis.getOtherRankingDailyScore(activityId, uid, ActivityConstant.RECEIVE_RANK, dailyNum, roundNum));
            myRank.setRank(activityOtherRedis.getOtherDailyRank(activityId, uid, rankType, dailyNum, roundNum));
        } else if (rankType == ActivityConstant.RECEIVE_RANK) {
            myRank.setReceiveScore(activityOtherRedis.getOtherRankingDailyScore(activityId, uid, rankType, dailyNum, roundNum));
            myRank.setSendingScore(activityOtherRedis.getOtherRankingDailyScore(activityId, uid, ActivityConstant.SEND_RANK, dailyNum, roundNum));
            myRank.setRank(activityOtherRedis.getOtherDailyRank(activityId, uid, rankType, dailyNum, roundNum));
        } else if (rankType == ActivityConstant.ROOM_RANK) {
            myRank.setRoomScore(activityOtherRedis.getOtherRankingDailyScore(activityId, uid, rankType, dailyNum, roundNum));
            myRank.setRank(activityOtherRedis.getOtherDailyRank(activityId, uid, rankType, dailyNum, roundNum));
        }

    }


    public OtherRankConfigVO otherRankConfig(String uid, String activityId) {
        OtherRankConfigVO vo = new OtherRankConfigVO();
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        return vo;
    }

    public OtherRankingVO otherRanking(String uid, String activityId, int rankType, int rankNum, int gloryInfo) {
        if (rankNum > 30) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        OtherRankingVO vo = new OtherRankingVO();
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        int roundNum = activity.getRoundNum();
        for (OtherRankingActivityData.RankingConfig rankingConfig : activity.getRankingRewardList()) {
            if (rankingConfig.getRankType() != rankType) {
                continue;
            }
            vo.setRankingList(getOtherRankingListVO(activityId, rankType, rankNum, roundNum, gloryInfo));
            vo.setMyRank(getOtherMyRank(activityId, uid, rankType, roundNum, gloryInfo));
        }
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        return vo;
    }

    // 每日排行榜
    public OtherRankingVO otherDailyRanking(String uid, String activityId, int rankType, int rankNum) {
        if (rankNum > 30) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        String dailyNum = DateSupport.ARABIAN.yyyyMMdd();
        OtherRankingVO vo = new OtherRankingVO();
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        int roundNum = activity.getRoundNum();
        for (OtherRankingActivityData.RankingConfig rankingConfig : activity.getRankingRewardList()) {
            if (rankingConfig.getRankType() != rankType) {
                continue;
            }
            vo.setRankingList(getOtherRankingDailyListVO(activityId, rankType, rankNum, dailyNum, roundNum));
            vo.setMyRank(getOtherDailyMyRank(activityId, uid, rankType, dailyNum, roundNum));
        }
        return vo;
    }

    // 隐藏榜单的排行榜
    public HiddenRankingVO hiddenRanking(String uid, String activityId, int rankType) {
        HiddenRankingVO vo = new HiddenRankingVO();
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        int roundNum = activity.getRoundNum();
        for (OtherRankingActivityData.RankingConfig rankingConfig : activity.getRankingRewardList()) {
            if (rankingConfig.getRankType() != rankType) {
                continue;
            }
            List<HiddenRankingListVO> hiddenRankingList = getHiddenRankingListVO(uid, activityId, rankType, roundNum);

            vo.setRankingList(hiddenRankingList);
            vo.setMyRank(getOtherMyRank(activityId, uid, rankType, roundNum, 0));
        }
        return vo;
    }

    // 高低分排行榜
    private List<OtherRankingListVO> getBaseRankingList(String activityId, int rankType, int roundNum) {
        List<OtherRankingListVO> rankingList = new ArrayList<>();

        Map<String, Integer> rankingMap = activityOtherRedis.getOtherRankingMapByScore(activityId, rankType, 10, 0, 99999, roundNum);
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO rankingListVO = new OtherRankingListVO();
            String rankUid = entry.getKey();
            rankingListVO.setScore(entry.getValue());
            if (ActivityConstant.ROOM_RANK == rankType) {
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(entry.getKey());
                rankingListVO.setName(roomData.getName());
                rankingListVO.setRoomId(rankUid);
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
            } else {
                ActorData rankActor = actorDao.getActorDataFromCache(entry.getKey());
                if (null == rankActor) {
                    logger.error("can not find actor. uid={}", entry.getKey());
                    continue;
                }
                rankingListVO.setUid(rankUid);
                rankingListVO.setName(rankActor.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            }
            rankingList.add(rankingListVO);
        }
        return rankingList;
    }

    private List<OtherRankingListVO> getHighRankingList(String activityId, int rankType, int roundNum) {
        List<OtherRankingListVO> highRankingList = new ArrayList<>();
        Map<String, Integer> rankingMap = activityOtherRedis.getOtherRankingMapByScore(activityId, rankType, 100000, 10000000, roundNum);
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO rankingListVO = new OtherRankingListVO();
            String rankUid = entry.getKey();
            int curScore = entry.getValue();
            rankingListVO.setScore(curScore);
            if (ActivityConstant.ROOM_RANK == rankType) {
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(entry.getKey());
                rankingListVO.setRoomId(rankUid);
                rankingListVO.setName(roomData.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
            } else {
                ActorData rankActor = actorDao.getActorDataFromCache(rankUid);
                if (null == rankActor) {
                    logger.error("can not find actor. uid={}", rankUid);
                    continue;
                }
                rankingListVO.setUid(rankUid);
                rankingListVO.setName(rankActor.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            }
            highRankingList.add(rankingListVO);
        }
        return highRankingList;
    }


    public BaseHighRankingVO baseHighRanking(String uid, String activityId, int rankType) {
        BaseHighRankingVO vo = new BaseHighRankingVO();
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        int roundNum = activity.getRoundNum();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        for (OtherRankingActivityData.RankingConfig rankingConfig : activity.getRankingRewardList()) {
            if (rankingConfig.getRankType() != rankType) {
                continue;
            }
            List<OtherRankingListVO> baseRankingList = getBaseRankingList(activityId, rankType, roundNum);
            List<OtherRankingListVO> highRankingList = getHighRankingList(activityId, rankType, roundNum);

            vo.setBaseRankingList(baseRankingList);
            vo.setHighRankingList(highRankingList);
            vo.setMyRank(getOtherMyRank(activityId, uid, rankType, roundNum, 0));
        }
        return vo;
    }

    public OtherReachingVO otherRankReaching(String uid, String activityId, int rankType) {
        OtherReachingVO vo = new OtherReachingVO();
        otherActivityService.getOtherRankingActivity(activityId);
        ActorData actorData = actorDao.getActorDataFromCache(uid);

        if (rankType == ActivityConstant.ROOM_RANK) {
            String roomId = RoomUtils.formatRoomId(uid);
            vo.setScore(activityOtherRedis.getOtherReachingScore(activityId, roomId, rankType, 0));

            MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
            if (roomData == null) {
                vo.setName(actorData.getName());
                vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                vo.setUid(uid);
            } else {
                vo.setName(roomData.getName());
                vo.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
                vo.setUid(roomId);
            }
        } else {
            vo.setScore(activityOtherRedis.getOtherReachingScore(activityId, uid, rankType, 0));
            vo.setName(actorData.getName());
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            vo.setUid(uid);
        }
        return vo;
    }

    public void chargeDiamonds(String uid, int changed, String title, String detailDesc) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(905);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(title);
        moneyDetailReq.setDesc(detailDesc);
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }


    public void distributeReward(String uid, String rewardType, int sourceId, int rewardNum, int rewardTime,
                                 String recordTitle, String recordDesc) {
        switch (rewardType) {
            case ResourceConstant.THANKS:
                break;
            case ResourceConstant.HEART:
                heartRecordDao.changeHeart(uid, rewardNum, recordTitle, recordDesc);
                break;
            case ResourceConstant.DIAMOND:
                chargeDiamonds(uid, rewardNum, recordTitle, recordDesc);
                break;
            default:
                // 使用py下发资源
//                resourceDeliveryService.sendGiftToMq(new GiftsMqVo(uid, rewardType, sourceId, rewardTime, rewardNum));
                activityUtilService.handleResources(new GiftsMqVo(uid, rewardType, sourceId, rewardTime, rewardNum));
        }
    }

    public void commonOfficialMsg(String uid, String picture, int actionType, int newType, String actText, String title, String body, String activityUrl) {
        OfficialData officialData = new OfficialData();
        officialData.setSubTitle("");
        officialData.setValid(1);
        officialData.setPicture(picture);
        officialData.setTo_uid(uid);
        officialData.setCtime(DateHelper.getNowSeconds());
        officialData.setNews_type(newType);
        officialData.setAtype(actionType);
        officialData.setUrl(activityUrl);
        officialData.setAct(actText);
        officialData.setTitle(title);
        officialData.setBody(body);
        officialData.setNtype(1);
        officialDao.save(officialData);
        if (officialData.get_id() != null) {
            NoticeNewData noticeNewData = new NoticeNewData();
            noticeNewData.setAid(uid);
            noticeNewData.setStatus(1);
            noticeNewData.setOfficial_id(officialData.get_id().toString());
            noticeNewData.setNtype(1);
            noticeNewData.setCtime(DateHelper.getNowSeconds());
            noticeNewDao.save(noticeNewData);
            OfficialPushMsg msg = new OfficialPushMsg();
            msg.setTitle(officialData.getTitle());
            msg.setBody(officialData.getBody());
            msg.setMsg_type(1);
            roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
        }
    }

    @Cacheable(value = "getPopularRoomId", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public String getPopularRoomId() {
        List<PopularListVO> popularList = activityOtherRedis.getPopularList();
        return popularList != null && popularList.size() > 0 ? popularList.get(0).getRoomId() : "";
    }


    public void commonShareMoment(String uid, String picture, int slang, String momentText, String acName) {
        if (!StringUtils.hasLength(picture) && !StringUtils.hasLength(momentText)) {
            logger.info("picture and momentText is empty return uid:{} acName:{}", uid, acName);
            return;
        }
        commonMomentPush(uid, momentText, acName, acName, picture, 800, 800);
    }

    public void commonMomentPush(String uid, String momentText, String activityId, String origin, String picture, int width, int height) {

        synchronized (stringPool.intern(uid)) {

            InnerPublishMomentDTO publishMomentDTO = new InnerPublishMomentDTO();

            publishMomentDTO.setUid(uid);
            publishMomentDTO.setText(momentText);
            publishMomentDTO.setShow(1);
            publishMomentDTO.setActiveId(activityId);
            publishMomentDTO.setLocation(origin);
            if (!StringUtils.isEmpty(picture)) {
                InnerPublishMomentDTO.MomentImageDTO imageDTO = new InnerPublishMomentDTO.MomentImageDTO();
                imageDTO.setUrl(picture);
                imageDTO.setWidth(String.valueOf(width));
                imageDTO.setHeight(String.valueOf(height));
                publishMomentDTO.setImgs(Collections.singletonList(imageDTO));
            }

            HttpResult<String> result = iMomentService.publish(publishMomentDTO);
            if (result.getCode() == 20) {
                logger.info("motherMomentPush level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
            }

            if (result.getCode() == 41) {
                logger.info("motherMomentPush level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.DIRTY_WORD);
            }

            if (result.isError()) {
                logger.error("motherMomentPush error. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }
        }
    }

    public void makeOtherRankingData(List<OtherRankingListVO> rankingList, OtherRankingListVO myRank, String rankKey, String uid, int length) {
        makeOtherRankingData(rankingList, myRank, rankKey, uid, length, false);
    }

    public void makeOtherRankingData(List<OtherRankingListVO> rankingList, OtherRankingListVO myRank, String rankKey, String uid, int length, boolean gloryInfo) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(rankKey, length);
        Map<String, String> commonFlagConfigMap = activityCommonConfig.getFlagConfigList().stream().collect(Collectors.toMap(ActivityCommonConfig.CommonFlagConfig::getCountryName, ActivityCommonConfig.CommonFlagConfig::getFlagUrl));
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO rankingVO = new OtherRankingListVO();
            String aid = entry.getKey();
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            rankingVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            rankingVO.setName(rankActor.getName());
            rankingVO.setUid(aid);
            rankingVO.setScore(entry.getValue());
            rankingVO.setRank(rank);
            rankingVO.setRidData(rankActor.getRidData());
            if (gloryInfo) {
                String countryCode = ActorUtils.getCountryCode(rankActor.getCountry());
                rankingVO.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));
                rankingVO.setBadgeList(badgeDao.getBadgeList(aid));
                rankingVO.setVipLevel(vipInfoDao.getIntVipLevel(aid));
            }

            if (aid.equals(uid)) {
                BeanUtils.copyProperties(rankingVO, myRank);
                myRank.setRidData(rankActor.getRidData());
            }
            rankingList.add(rankingVO);
            rank += 1;
        }

        if (myRank.getRank() == null || myRank.getRank() == 0) {
            myRank.setName(actorData.getName());
            myRank.setUid(uid);
            myRank.setRidData(actorData.getRidData());
            myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myRank.setScore(activityCommonRedis.getCommonZSetRankingScore(rankKey, uid));
            myRank.setRank(-1);
            if (gloryInfo) {
                String countryCode = ActorUtils.getCountryCode(actorData.getCountry());
                myRank.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));
                myRank.setBadgeList(badgeDao.getBadgeList(uid));
                myRank.setVipLevel(vipInfoDao.getIntVipLevel(uid));
            }
        }
    }

    public void sendActivityShareMsg(String uid, String aid, int shareId) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        SendChatMsgDTO msgDto = new SendChatMsgDTO();
        msgDto.setUid(uid);
        msgDto.setAid(aid);
        msgDto.setMsgType(MsgType.SHARE_ACTIVITY);
        msgDto.setOs(actorData.getIntOs());
        msgDto.setMsgBody("");
        msgDto.setSlang(actorData.getSlang());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("shareId", shareId);
        msgDto.setMsgInfo(jsonObject);
        msgDto.setVersioncode(actorData.getVersion_code());
        msgDto.setNew_versioncode(5);
        iMsgService.sendMsg(msgDto);
    }


    public void sendActivityShareMsg(String uid, String aid, JSONObject jsonObject) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        SendChatMsgDTO msgDto = new SendChatMsgDTO();
        msgDto.setUid(uid);
        msgDto.setAid(aid);
        msgDto.setMsgType(MsgType.SHARE_ACTIVITY);
        msgDto.setOs(actorData.getIntOs());
        msgDto.setMsgBody("");
        msgDto.setSlang(actorData.getSlang());
        jsonObject.put("fillDone", 1);
        msgDto.setMsgInfo(jsonObject);
        msgDto.setVersioncode(actorData.getVersion_code());
        msgDto.setNew_versioncode(5);
        iMsgService.sendMsg(msgDto);
    }

    /**
     * 中奖推送广播及公屏消息
     */
    public void pushBroadcastScreenMsg(String uid, PrizeConfigVO drawConfig, String activityUrl, String activityTitleEn, String activityTitleAr, String prizeIcon) {
        String roomId = null;
        Integer inRoomScreen = drawConfig.getInRoomScreen();
        Integer allRoomScreen = drawConfig.getAllRoomScreen();
        if (inRoomScreen != null && inRoomScreen > 0) {
            roomId = roomPlayerRedis.getActorRoomStatus(uid);
        }
        if (allRoomScreen != null && allRoomScreen > 0) {
            roomId = "all";
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);

        // 推送公屏消息
        if (!StringUtils.isEmpty(roomId)) {
            List<HighlightTextObject> list = new ArrayList<>();
            HighlightTextObject object = new HighlightTextObject();
            object.setText(actorData.getName());
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText(drawConfig.getNameEn());
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText(drawConfig.getNameAr());
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText("Join Now >>");
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText("انضم الآن >>");
            object.setHighlightColor("#FFE200");
            list.add(object);

            RoomNotificationMsg msg = new RoomNotificationMsg();
            msg.setUid(uid);
            msg.setUser_name(actorData.getName());
            msg.setUser_head(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            msg.setText(String.format("Notice: Congratulations \u202C%s getting \u202C%s. Join Now>>", actorData.getName(), drawConfig.getNameEn()));
            msg.setText_ar(String.format("ملاحظة:  تهانينا ل \u202b%s لحصوله على \u202b%s. انضم الآن >>", actorData.getName(), drawConfig.getNameAr()));
            msg.setHighlight_text(list);
            msg.setHighlight_text_ar(list);
            msg.setWeb_url(activityUrl);
            msg.setWeb_type(1);
            msg.setWidth(375);
            msg.setHeight(560);
            roomWebSender.sendRoomWebMsg(roomId, null, msg, false);
        }

        Integer broadcast = drawConfig.getBroadcast();
        if (broadcast != null && broadcast > 0) {
            RoomCommonScrollMsg scrollMsg = new RoomCommonScrollMsg();
            scrollMsg.setUid(uid);
            scrollMsg.setPrizeIcon(prizeIcon);
            scrollMsg.setPrizeTextEn(String.format("Congratulations to %s for getting %s in %s", actorData.getName(), drawConfig.getNameEn(), activityTitleEn));
            scrollMsg.setPrizeTextAr(String.format("تهانينا لـ %s للحصول على %s في %s", actorData.getName(), drawConfig.getNameAr(), activityTitleAr));

            List<HighlightTextObject> list = new ArrayList<>();
            HighlightTextObject object = new HighlightTextObject();
            object.setText(actorData.getName());
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText(activityTitleEn);
            object.setHighlightColor("#FFE200");
            list.add(object);
            object = new HighlightTextObject();
            object.setText(activityTitleAr);
            object.setHighlightColor("#FFE200");
            list.add(object);
            scrollMsg.setHighlightTextEn(list);
            scrollMsg.setHighlightTextAr(list);
            scrollMsg.setActionType(19);
            scrollMsg.setActionValue(activityUrl);
            roomWebSender.sendRoomWebMsg("all", uid, scrollMsg, false);
        }
    }


    public ResourceKeyConfigData.ResourceMeta drawOne(String uid, String resKey, String eventTitle) {
        ResourceKeyConfigData keyConfigData = resourceKeyConfigDao.findByKey(resKey);
        if (keyConfigData == null || keyConfigData.getKeyType() != 1) {
            logger.error("资源key必须为概率类型");
            return null;
        }
        Map<String, ResourceKeyConfigData.ResourceMeta> allPrizeConfigMap = new HashMap<>();
        keyConfigData.getResourceMetaList().forEach(subItem -> {
            allPrizeConfigMap.put(subItem.getMetaId(), subItem);
        });
        Map<String, Integer> calcMap = getCalcMap(allPrizeConfigMap);
        String prizeId = getMetaIdByProbability(calcMap);
        ResourceKeyConfigData.ResourceMeta resourceMeta = allPrizeConfigMap.get(prizeId);
        ResourceKeyConfigData.ResourceMeta toResourceMeta = null;

        if (resourceMeta != null) {
            toResourceMeta = new ResourceKeyConfigData.ResourceMeta();
            BeanUtils.copyProperties(resourceMeta, toResourceMeta);
            resourceKeyHandlerService.sendOneResourceData(uid, toResourceMeta, 905,
                    eventTitle,
                    eventTitle,
                    eventTitle,
                    "", "", 1);
//            doDrawPrizeRecordEvent(uid, Collections.singletonList(toResourceMeta));
        }
        return toResourceMeta;
    }

    private Map<String, Integer> getCalcMap(Map<String, ResourceKeyConfigData.ResourceMeta> srcMap) {
        Map<String, Integer> calcMap = new HashMap<>();
        for (Map.Entry<String, ResourceKeyConfigData.ResourceMeta> entry : srcMap.entrySet()) {
            String k = entry.getKey();
            ResourceKeyConfigData.ResourceMeta v = entry.getValue();
            try {
                int rate = (int) (Float.parseFloat(v.getRateNumber()) * 1000);
                if (rate >= 0) {
                    calcMap.put(k, rate);
                }
            } catch (NumberFormatException e) {
                logger.error("key:{} rateNumber:{} msg:{} ", k, v.getRateNumber(), e.getMessage(), e);
                return null;
            }
        }
        return calcMap;
    }

    public String getMetaIdByProbability(Map<String, Integer> sourceMap) {
        if (CollectionUtils.isEmpty(sourceMap)) {
            return null;
        }

        TreeMap<Integer, String> weightMap = new TreeMap<>();
        int totalWeight = 0;

        for (Map.Entry<String, Integer> entry : sourceMap.entrySet()) {
            if (entry.getValue() > 0) {
                totalWeight += entry.getValue();
                weightMap.put(totalWeight, entry.getKey());
            }
        }

        if (totalWeight == 0) {
            return null;
        }

        int randomWeight = ThreadLocalRandom.current().nextInt(totalWeight);
        return weightMap.higherEntry(randomWeight).getValue();
    }

    public int getBaseIndexLevel(int score, List<Integer> scoreLevelList) {
        List<Integer> tempLevelNumList = new ArrayList<>(scoreLevelList);
        int currentLevelIndex = 0;
        if (tempLevelNumList.contains(score)) {
            currentLevelIndex = tempLevelNumList.indexOf(score);
        } else {
            tempLevelNumList.add(score);
            tempLevelNumList.sort(Integer::compare);
            currentLevelIndex = tempLevelNumList.indexOf(score) - 1;
        }
        return currentLevelIndex;
    }


    @Cacheable(value = "getActivityData", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public OtherRankingActivityData getActivityData(String activityId) {
        if (!StringUtils.hasLength(activityId)) {
            return null;
        }
        return otherRankingActivityDao.findData(activityId);
    }

    /**
     * 发送奖励
     */
    public boolean sendActivityReward(String aid, int resType, int resId, int resNum, int resDays, int aType, String title, String desc, int getWay) {
        if (resType == ResTypeEnum.COIN.getType()) {
            heartRecordDao.changeHeart(aid, resNum, title, desc);
        } else if (resType == ResTypeEnum.DIAMONDS.getType()) {
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setRoomId("");
            moneyDetailReq.setUid(aid);
            moneyDetailReq.setAtype(aType);
            moneyDetailReq.setChanged(resNum);
            moneyDetailReq.setTitle(title);
            moneyDetailReq.setDesc(desc);
            mqSenderService.asyncChargeDiamonds(moneyDetailReq);
        } else {
            mqSenderService.asyncHandleResources(getResourcesDTO(aid, resType, resId, resNum, resDays, desc, getWay));
        }
        return true;
    }

    private ResourcesDTO getResourcesDTO(String aid, int resType, int resId, int resNum, int resDays, String desc, int getWay) {
        ResourcesDTO dto = new ResourcesDTO();
        dto.setUid(aid);
        dto.setResId(String.valueOf(resId));
        dto.setResType(resType);
        dto.setNum(resNum);
        dto.setDays(resDays);
        dto.setGetWay(getWay);
        dto.setActionType(2);
        dto.setmTime(DateHelper.getNowSeconds());
        dto.setDesc(desc);
        dto.setItemsSourceDetail(desc);
        // 自然天过期
        dto.setGainType(0);
        return dto;
    }

    public String getDayByBase(String activityId, String uid) {
        return getDayByBase(activityId, uid, true, false);
    }

    public OtherRankConfigVO testUidDayBase(int cmd, String uid, int addDays, String activityId) {
        OtherRankConfigVO otherRankConfigVO = new OtherRankConfigVO();
        if (cmd == 1) {
            // 设置偏移天数
            activityCommonRedis.setCommonHashData(getHashTestDayKeyByBase(activityId), uid, String.valueOf(addDays));
            otherRankConfigVO.setScore(addDays);
        } else if (cmd == 2) {
            // 查询偏移天数
            int add = activityCommonRedis.getCommonHashValue(getHashTestDayKeyByBase(activityId), uid);
            otherRankConfigVO.setScore(add);
        }
        return otherRankConfigVO;
    }

    public String getDayByBase(String activityId, String uid, boolean baseIsToday, boolean isGrayTest) {
        if ((ServerConfig.isNotProduct() || isGrayTest) && StringUtils.hasLength(uid)) {
            return getTestDaysByBase(activityId, uid, baseIsToday);
        }
        return baseIsToday ? DateHelper.ARABIAN.formatDateInDay()
                : DateHelper.ARABIAN.getYesterdayStr(new Date());
    }


    public String getTestDaysByBase(String activityId, String uid, boolean baseIsToday) {
        int addDays = activityCommonRedis.getCommonHashValue(getHashTestDayKeyByBase(activityId), uid);
        logger.info("test add uid:{} days:{}", uid, addDays);
        addDays = baseIsToday ? addDays : addDays - 1;
        if (addDays == 0) {
            return DateHelper.ARABIAN.formatDateInDay();
        }
        return todayMinusDaysByBase(addDays);
    }

    /**
     * @param days
     * @return
     */
    private String todayMinusDaysByBase(int days) {
        days = -days; // -1为明天  1为昨天
        LocalDate nowDate = DateSupport.ARABIAN.getToday();
        LocalDate oldDate = nowDate.minusDays(days);
        // 获取字符格式 yyyy-MM-dd
        return DateSupport.format(oldDate);
    }

    private String getHashTestDayKeyByBase(String activityId) {
        return activityId + ":test:uid:day";
    }
}
