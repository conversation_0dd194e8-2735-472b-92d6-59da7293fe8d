package com.quhong.service;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.dao.CelebrityActivityDao;
import com.quhong.mongo.dao.RankingActivityDao;
import com.quhong.mongo.data.CelebrityActivity;
import com.quhong.mongo.data.RankingActivity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


@Component
public class ActivityService {
    private static final Logger logger = LoggerFactory.getLogger(ActivityService.class);

    @Resource
    private RankingActivityDao rankingActivityDao;
    @Resource
    private CelebrityActivityDao celebrityActivityDao;
    @Resource
    private ActivityService activityService;


    /**
     * 获取正在运行的冲榜活动
     */
    public List<RankingActivity> getRankingActivities() {
        int nowSeconds = DateHelper.getNowSeconds();
        List<RankingActivity> activityList = activityService.getRankingActivitiesFromCache();
        return activityList.stream().filter(a -> a.getEndTime() > nowSeconds && a.getStartTime() <= nowSeconds).collect(Collectors.toList());
    }

    @Cacheable(value = "getRankingActivitiesFromCache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<RankingActivity> getRankingActivitiesFromCache() {
        List<RankingActivity> activityList = rankingActivityDao.getRankingActivities();
        logger.info("getRankingActivitiesFromCache size={}", activityList.size());
        return activityList;
    }

    /**
     * 获取正在运行的名人活动
     */
    public List<CelebrityActivity> getCelebrityActivities() {
        int nowSeconds = DateHelper.getNowSeconds();
        List<CelebrityActivity> activityList = activityService.getCelebrityActivitiesFromCache();
        return activityList.stream().filter(a -> a.getEndTime() > nowSeconds && a.getStartTime() <= nowSeconds).collect(Collectors.toList());
    }

    @Cacheable(value = "getCelebrityActivitiesFromCache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<CelebrityActivity> getCelebrityActivitiesFromCache() {
        List<CelebrityActivity> activityList = celebrityActivityDao.getCelebrityActivities();
        logger.info("getCelebrityActivitiesFromCache size={}", activityList.size());
        return activityList;
    }
}
