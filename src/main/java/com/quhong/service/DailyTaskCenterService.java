package com.quhong.service;

import com.quhong.api.RoomListService;
import com.quhong.config.DailyTaskCenterConfig;
import com.quhong.config.UserTaskConfig;
import com.quhong.consumer.DailyTaskMqConsumer;
import com.quhong.consumer.UserTaskMqConsumer;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.ResourceMetaData;
import com.quhong.data.TaskInfo;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.dto.DailyTaskDTO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.enums.UserHttpCode;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.IndexBannerDao;
import com.quhong.mongo.dao.SignTableDao;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mongo.data.SignTableData;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.DailyTaskManagerDao;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.dao.NewcomerTaskDao;
import com.quhong.mysql.dao.UserTaskDao;
import com.quhong.mysql.data.DailyTaskManager;
import com.quhong.mysql.data.NewcomerTaskData;
import com.quhong.mysql.data.OnceTaskRecord;
import com.quhong.mysql.data.UserTaskData;
import com.quhong.redis.UserTaskRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.*;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;


/**
 * 任务中心
 *
 * <AUTHOR>
 * @date 2023/9/21
 */
@Component
public class DailyTaskCenterService {

    private static final Logger logger = LoggerFactory.getLogger(DailyTaskCenterService.class);

    private static final int A_TYPE = 72;
    private static final String TITLE = "Daily task award";

    public static final int NEWCOMER_TASK = 0; //新人任务
    public static final int DAILY_TASK = 1; // 日常任务
    public static final int ADVANCED_TASK = 2; // 进阶任务

    private static final String LOTTERY_TICKET_ICON = "https://cdn3.qmovies.tv/youstar/op_1696922421_lotteryTicket.png";
    private static final String COIN_ICON = "https://cdn3.qmovies.tv/youstar/op_1696922421_coin.png";
    private static final String DIAMONDS_ICON = "https://cdn3.qmovies.tv/youstar/op_1696759471_diamonds_1.png";

    public final Map<String, TaskInfo> TASK_MAP = new HashMap<>();

    private static final String LUCKY_WHEEL_DEBUG = "https://test2.qmovies.tv/task_wheel/";
    private static final String LUCKY_WHEEL_PRO = "https://static.youstar.live/task_wheel/";

    // 特殊新人任务
    private static final String NEW_COMER_STAY_IN_ROOM = "stay_in_the_room";
    private static final List<String> NEW_COMER_SPECIAL_TASK = Arrays.asList("stay_in_the_room", "on_mic_time_chat");
    private static final List<String> NEW_DEVICE_ACCOUNT_RES_KEY = Arrays.asList("Otherstay", "DZstay", "AEstay", "OMstay", "EGstay", "PLstay", "BHstay", "DEstay", "FRstay", "CAstay", "QAstay", "LBstay", "LYstay", "USstay", "MAstay", "SAstay", "SDstay", "TNstay", "TRstay", "ESstay", "SYstay", "YEstay", "IQstay", "UKstay", "JOstay");


    private static final Map<Integer, String> ON_MIC_TIME_1_MAP = new HashMap<Integer, String>() {
        {
            //  1一个账号的新用户 2多个账号的新用户 3一个账号的老用户 4多个账号的老用户
            put(1, "taskCenterDailyOnMicTime5userType1");
            put(2, "taskCenterDailyOnMicTime5userType2");
            put(3, "taskCenterDailyOnMicTime5userType3");
            put(4, "taskCenterDailyOnMicTime5userType4");
        }
    };

    private static final Map<Integer, String> ON_MIC_TIME_2_MAP = new HashMap<Integer, String>() {
        {
            put(1, "taskCenterDailyOnMicTime10userType1");
            put(2, "taskCenterDailyOnMicTime10userType2");
            put(3, "taskCenterDailyOnMicTime10userType3");
            put(4, "taskCenterDailyOnMicTime10userType4");
        }
    };

    private static final Map<Integer, String> ON_MIC_TIME_3_MAP = new HashMap<Integer, String>() {
        {
            put(1, "taskCenterDailyOnMicTime30userType1");
            put(2, "taskCenterDailyOnMicTime30userType2");
            put(3, "taskCenterDailyOnMicTime30userType3");
            put(4, "taskCenterDailyOnMicTime30userType4");
        }
    };

    private static final Map<Integer, String> ON_MIC_TIME_4_MAP = new HashMap<Integer, String>() {
        {
            put(1, "taskCenterDailyOnMicTime60userType1");
            put(2, "taskCenterDailyOnMicTime60userType2");
            put(3, "taskCenterDailyOnMicTime60userType3");
            put(4, "taskCenterDailyOnMicTime60userType4");
        }
    };


    private static final Map<Integer, String> GIFT_COST_DIAMOND_1_MAP = new HashMap<Integer, String>() {
        {
            put(0, "taskCenterAdvancedGiftCostDiamond10userType0"); // 消费10钻下发
        }
    };

    private static final Map<Integer, String> GIFT_COST_DIAMOND_2_MAP = new HashMap<Integer, String>() {
        {
            put(0, "taskCenterAdvancedGiftCostDiamond100userType0"); // 消费100钻下发
        }
    };

    private static final Map<Integer, String> GIFT_COST_DIAMOND_3_MAP = new HashMap<Integer, String>() {
        {
            put(0, "taskCenterAdvancedGiftCostDiamond200userType0"); // 消费200钻下发
        }
    };

    private static final Map<Integer, String> GIFT_COST_DIAMOND_4_MAP = new HashMap<Integer, String>() {
        {
            put(0, "taskCenterAdvancedGiftCostDiamond1000userType0"); // 消费1000钻下发
        }
    };


    public final Map<String, Map<Integer, String>> STAGE_TASK_MAP = new HashMap<String, Map<Integer, String>>() {
        {
            put("on_mic_time_1", ON_MIC_TIME_1_MAP);
            put("on_mic_time_2", ON_MIC_TIME_2_MAP);
            put("on_mic_time_3", ON_MIC_TIME_3_MAP);
            put("on_mic_time_4", ON_MIC_TIME_4_MAP);

            put("gift_cost_diamond_1", GIFT_COST_DIAMOND_1_MAP);
            put("gift_cost_diamond_2", GIFT_COST_DIAMOND_2_MAP);
            put("gift_cost_diamond_3", GIFT_COST_DIAMOND_3_MAP);
            put("gift_cost_diamond_4", GIFT_COST_DIAMOND_4_MAP);
        }
    };


    @Resource
    private DailyTaskCenterConfig taskCenterConfig;
    @Resource
    private SignTableDao signTableDao;
    @Resource
    private DailyTaskManagerDao dailyTaskManagerDao;
    @Resource
    private OnceTaskService onceTaskService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private UserTaskConfig userTaskConfig;
    @Resource
    private UserTaskDao userTaskDao;
    @Resource
    private NewcomerTaskDao newcomerTaskDao;
    @Resource
    private UserLotteryTicketsService userLotteryTicketsService;
    @Resource
    private UserTaskRedis userTaskRedis;
    @Resource
    private RoomListService roomListService;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private HomeBannerService homeBannerService;
    @Resource
    private BasePlayerRedis basePlayerRedis;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;

    @PostConstruct
    private void init() {
        userTaskConfig.getNewcomerTasks().forEach(taskInfo -> {
            taskInfo.setType(NEWCOMER_TASK);
            TASK_MAP.put(taskInfo.getKey(), taskInfo);
        });
        userTaskConfig.getDailyTasks().forEach(taskInfo -> {
            taskInfo.setType(DAILY_TASK);
            TASK_MAP.put(taskInfo.getKey(), taskInfo);
        });
        userTaskConfig.getAdvancedTasks().forEach(taskInfo -> {
            taskInfo.setType(ADVANCED_TASK);
            TASK_MAP.put(taskInfo.getKey(), taskInfo);
        });
    }

    /**
     * 新版任务列表（849）
     */
    public TaskListVO taskList(DailyTaskDTO dto) {
        ActorData actorData = actorDao.getActorData(dto.getUid());
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", dto.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        TaskListVO vo = new TaskListVO();
        vo.setCountry(actorData.getCountry());
        vo.setCoinBalance((long) actorData.getHeartGot());
        vo.setLotteryTicketNum(userLotteryTicketsService.getUserTicketsNum(dto.getUid()));
        vo.setBannerList(homeBannerService.getHomeBannerList(dto.getUid(), IndexBannerDao.TYPE_DAILY_TASK, dto.getSlang(), dto.getVersioncode(),
                dto.getApp_package_name(), dto.getOs(), false));
        String jumpRoomId = getJumpChatRoomId(dto.getUid());
        // 新人任务
        fillNewcomerTasks(dto, jumpRoomId, vo);
        String strDate = DateHelper.ARABIAN.formatDateInDay();
        int taskDate = Integer.parseInt(strDate.replace("-", ""));
        List<UserTaskData> userTaskList = userTaskDao.getTaskListByUid(dto.getUid(), taskDate);
        Map<String, UserTaskData> userTaskMap = CollectionUtil.listToKeyMap(userTaskList, UserTaskData::getTaskKey);
        // 日常任务
        fillDailyTask(dto, jumpRoomId, userTaskMap, vo);
        // 进阶任务
        fillAdvancedTasks(dto, jumpRoomId, userTaskMap, vo);
        return vo;
    }

    private void fillNewcomerTasks(DailyTaskDTO dto, String jumpRoomId, TaskListVO vo) {
        String uid = dto.getUid();
        int slang = dto.getSlang();
        if (!ActorUtils.isNewRegisterActor(uid, 7)) {
            // 非新用户没有新人任务
            return;
        }
        TaskInfoVO taskInfo = new TaskInfoVO();
        taskInfo.setTitle(slang == SLangType.ENGLISH ? "Newcomer" : "الوافد الجديد");
        taskInfo.setEndTime(getEndTime(uid, true));
        // 新人任务
        List<TaskInfo> newcomerTasks = userTaskConfig.getNewcomerTasks();
        // 任务完成情况
        List<NewcomerTaskData> newcomerTaskList = newcomerTaskDao.getListByUid(uid);
        List<UserTaskData> taskList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(newcomerTaskList)) {
            for (NewcomerTaskData newcomerTaskData : newcomerTaskList) {
                UserTaskData userTaskData = new UserTaskData();
                BeanUtils.copyProperties(newcomerTaskData, userTaskData);
                taskList.add(userTaskData);
            }
        }
        Map<String, UserTaskData> newcomerTaskMap = CollectionUtil.listToKeyMap(taskList, UserTaskData::getTaskKey);
        taskInfo.setStageTasks(getStageTaskList(dto, jumpRoomId, newcomerTasks, newcomerTaskMap));
        taskInfo.setCommonTasks(getCommonTaskList(dto, jumpRoomId, newcomerTasks, newcomerTaskMap));
        if (checkAllFinished(taskInfo)) {
            // 新人任务全部完成后不显示
            return;
        }
        vo.setNewcomerTasks(taskInfo);
    }

    private boolean checkAllFinished(TaskInfoVO taskInfo) {
        for (StageTaskVO taskVO : taskInfo.getStageTasks()) {
            for (StageTaskVO.Reward reward : taskVO.getRewards()) {
                if (reward.getStatus() != 2) {
                    return false;
                }
            }
        }
        for (CommonTaskVO taskVO : taskInfo.getCommonTasks()) {
            if (taskVO.getStatus() != 2) {
                return false;
            }
        }
        return true;
    }

    private List<StageTaskVO> getStageTaskList(DailyTaskDTO dto, String jumpRoomId, List<TaskInfo> infoList, Map<String, UserTaskData> userTaskMap) {
        String uid = dto.getUid();
        int slang = dto.getSlang();
        if (CollectionUtils.isEmpty(infoList)) {
            return Collections.emptyList();
        }
        List<TaskInfo> stageTaskList = infoList.stream().filter(k -> k.getIsStageTask() == 1).collect(Collectors.toList());
        Map<String, List<TaskInfo>> stageTaskMap = CollectionUtil.listToKeyListMap(stageTaskList, TaskInfo::getStageTaskKey);
        List<StageTaskVO> stageTasks = new ArrayList<>();
        for (Map.Entry<String, List<TaskInfo>> entry : stageTaskMap.entrySet()) {
            StageTaskVO stageTask = new StageTaskVO();
            stageTask.setName(slang == SLangType.ENGLISH ? entry.getValue().get(0).getName() : entry.getValue().get(0).getNameAr());
            stageTask.setRewards(getStageTaskRewards(entry.getValue(), userTaskMap));
            stageTask.setNum(getMaxTaskNum(entry.getKey(), userTaskMap));
            stageTask.setJumpRoomId(getJumpRoomId(entry.getKey(), uid, jumpRoomId));
            stageTask.setJumpCall(entry.getValue().get(0).getJumpCall());
            if ("jump_room".equals(stageTask.getJumpCall()) && RoomUtils.formatRoomId(uid).equals(stageTask.getJumpRoomId())) {
                stageTask.setJumpCall("jump_myRoom");
            }
            stageTasks.add(stageTask);
        }
        return stageTasks;
    }

    private int getMaxTaskNum(String key, Map<String, UserTaskData> userTaskMap) {
        int maxTaskNum = 0;
        for (int i = 1; i <= 4; i++) {
            UserTaskData userTaskData = userTaskMap.get(key + "_" + i);
            if (userTaskData != null) {
                maxTaskNum = Math.max(maxTaskNum, userTaskData.getTaskNum());
            }
        }
        return maxTaskNum;
    }

    private List<CommonTaskVO> getCommonTaskList(DailyTaskDTO dto, String jumpRoomId, List<TaskInfo> infoList, Map<String, UserTaskData> userTaskMap) {
        String uid = dto.getUid();
        int slang = dto.getSlang();
        if (CollectionUtils.isEmpty(infoList)) {
            return Collections.emptyList();
        }
        List<CommonTaskVO> commonTasks = new ArrayList<>();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        for (TaskInfo info : infoList) {
            if (info.getIsStageTask() != 0) {
                continue;
            }
            UserTaskData userTaskData = userTaskMap.get(info.getKey());
            CommonTaskVO commonTask = new CommonTaskVO();
            commonTask.setKey(info.getKey());
            commonTask.setName(slang == SLangType.ENGLISH ? info.getName() : info.getNameAr());
            commonTask.setIcon(info.getIcon());
            commonTask.setNum(userTaskData == null ? 0 : userTaskData.getTaskNum());
            commonTask.setLimit(info.getLimit());
            String resourceKey = info.getResourceKey();
            ResourceKeyConfigData resourceKeyConfigData = resourceKeyHandlerService.getConfigData(resourceKey);
            int rewardNum = info.getRewardNum();
            int rewardType = info.getRewardType();
            if (resourceKeyConfigData != null) {
                ResourceKeyConfigData.ResourceMeta resourceMeta = resourceKeyConfigData.getResourceMetaList().get(0);
                rewardNum = resourceMeta.getResourceNumber();
                rewardType = BaseDataResourcesConstant.TYPE_TASK_CENTER_TICKET == resourceMeta.getResourceType() ? 1 :
                        BaseDataResourcesConstant.TYPE_COIN == resourceMeta.getResourceType() ? 0
                                : resourceMeta.getResourceType();
            }
            commonTask.setRewardType(rewardType);
            commonTask.setRewardNum(rewardNum);
            commonTask.setStatus(userTaskData == null ? 0 : userTaskData.getStatus());
            commonTask.setJumpRoomId(getJumpRoomId(info.getKey(), uid, jumpRoomId));
            commonTask.setJumpUrl(getJumpUrl(info.getKey(), uid));
            commonTask.setJumpCall(info.getJumpCall());
            commonTasks.add(commonTask);
        }
        // 未领取 > 未完成 > 已领取
        commonTasks.sort(Comparator.comparingInt(o -> o.getStatus() == 1 ? 0 : o.getStatus() == 0 ? 1 : 2));
        return commonTasks;
    }

    /**
     * 设置特殊新手任务奖励key
     */
    private void fillNewComerSpecialKey(TaskInfo info, ActorData actorData){
        String uid = actorData.getUid();
        boolean newDeviceAccount = ActorUtils.isNewDeviceAccount(uid, 7, actorData.getFirstTnId());
        String countryCode = ActorUtils.getCountryCode(actorData.getCountry());
        String resKey = String.format("%sstay", countryCode);
        if (newDeviceAccount){
            resKey = NEW_DEVICE_ACCOUNT_RES_KEY.contains(resKey) ? resKey : NEW_DEVICE_ACCOUNT_RES_KEY.get(0);
        }else {
            resKey = NEW_DEVICE_ACCOUNT_RES_KEY.get(0);
        }
        info.setResourceKey(resKey);
    }

    private String getJumpUrl(String key, String uid) {
        String jumpUrl = "";
        if ("play_fruit_machine".equals(key)) {
            String token = basePlayerRedis.getToken(uid);
            jumpUrl = ServerConfig.isNotProduct() ? "https://api.springluckygame.com/game_fruits?appId=880088&gameName=fruit_party"
                    : "https://h5.bigluckygame.com/game_fruits/?appId=888888&gameName=fruit_party";
            UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(jumpUrl);
            urlBuilder.queryParam("uid", uid);
            urlBuilder.queryParam("token", token);
            return urlBuilder.build(false).encode().toUriString();
        }
        return jumpUrl;
    }

    private String getJumpChatRoomId(String uid) {
        String roomId = roomPlayerRedis.getActorRoomStatus(uid);
        if (StringUtils.hasLength(roomId)) {
            return roomId;
        }
        return roomListService.getJumpChatRoomId(uid);
    }

    private String getJumpRoomId(String key, String uid, String jumpRoomId) {
        String ownRoomId = RoomUtils.formatRoomId(uid);
        switch (key) {
            case "send_room_msg_1":
            case "send_room_msg_2":
            case "send_room_gift_1":
            case "send_room_gift_2":
            case "follow_room":
            case "join_room_member":
            case "gift_cost_diamond":
                return jumpRoomId;
            case "invite_new_users_on_mic":
            case "get_new_room_member":
            case "play_fruit_machine":
            case "watch_video_time":
            case "win_finger_guess":
                return ownRoomId;
            case "on_mic_time":
                String roomId = roomPlayerRedis.getActorRoomStatus(uid);
                if (ownRoomId.equals(roomId)) {
                    return "";
                }
                return StringUtils.isEmpty(roomId) ? "" : roomId;
            default:
                return "";
        }
    }

    private List<StageTaskVO.Reward> getStageTaskRewards(List<TaskInfo> taskList, Map<String, UserTaskData> userTaskMap) {
        List<StageTaskVO.Reward> rewardList = new ArrayList<>();
        for (TaskInfo taskInfo : taskList) {
            UserTaskData userTaskData = userTaskMap.get(taskInfo.getKey());
            rewardList.add(new StageTaskVO.Reward(taskInfo.getKey(), taskInfo.getLimit(), userTaskData == null ? 0 : userTaskData.getStatus()));
        }
        return rewardList;
    }

    private void fillDailyTask(DailyTaskDTO dto, String jumpRoomId, Map<String, UserTaskData> userTaskMap, TaskListVO vo) {
        String uid = dto.getUid();
        int slang = dto.getSlang();
        TaskInfoVO taskInfo = new TaskInfoVO();
        List<TaskInfo> dailyTasks = userTaskConfig.getDailyTasks();
        taskInfo.setTitle(slang == SLangType.ENGLISH ? "Daily" : "يوميًا");
        taskInfo.setEndTime(getEndTime(uid, false));
        taskInfo.setStageTasks(getStageTaskList(dto, jumpRoomId, dailyTasks, userTaskMap));
        taskInfo.setCommonTasks(getCommonTaskList(dto, jumpRoomId, dailyTasks, userTaskMap));
        vo.setDailyTasks(taskInfo);
    }

    private void fillAdvancedTasks(DailyTaskDTO dto, String jumpRoomId, Map<String, UserTaskData> userTaskMap, TaskListVO vo) {
        String uid = dto.getUid();
        int slang = dto.getSlang();
        TaskInfoVO taskInfo = new TaskInfoVO();
        List<TaskInfo> advancedTasks = userTaskConfig.getAdvancedTasks();
        taskInfo.setTitle(slang == SLangType.ENGLISH ? "Advanced" : "متقدمة");
        taskInfo.setEndTime(getEndTime(uid, false));
        taskInfo.setStageTasks(getStageTaskList(dto, jumpRoomId, advancedTasks, userTaskMap));
        taskInfo.setCommonTasks(getCommonTaskList(dto, jumpRoomId, advancedTasks, userTaskMap));
        vo.setAdvancedTasks(taskInfo);
    }

    private int getEndTime(String uid, boolean isNewcomerTask) {
        if (isNewcomerTask) {
            return 7 * 24 * 60 * 60 + new ObjectId(uid).getTimestamp();
        } else {
            return (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000) + 24 * 60 * 60 - 1;
        }
    }

    public TaskRewardVO getTaskReward(DailyTaskDTO dto) {
        ActorData actorData = actorDao.getActorData(dto.getUid());
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", dto.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        TaskInfo taskInfo = TASK_MAP.get(dto.getTaskKey());
        if (StringUtils.isEmpty(dto.getTaskKey()) || null == taskInfo) {
            logger.error("get task reward param error. uid={} taskKey={}", dto.getUid(), dto.getTaskKey());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        TaskRewardVO vo = new TaskRewardVO();
        TaskRewardVO.Reward reward;
        try (DistributeLock lock = new DistributeLock(getLockKey(dto.getUid()))) {
            lock.lock();
            if (taskInfo.getType() == NEWCOMER_TASK) {
                if (!ActorUtils.isNewRegisterActor(dto.getUid(), 7)) {
                    logger.info("Non-new users cannot receive newcomer tasks rewards. uid={} taskKey={}", dto.getUid(), dto.getTaskKey());
                    throw new CommonException(UserHttpCode.NO_NEW_USERS_CANNOT_RECEIVE);
                }
                NewcomerTaskData newcomerTask = newcomerTaskDao.getByUidAndTaskKey(dto.getUid(), dto.getTaskKey());
                if (null == newcomerTask || newcomerTask.getTaskNum() < taskInfo.getLimit()) {
                    logger.info("The task has not been completed and the reward cannot be received. uid={} taskKey={}", dto.getUid(), dto.getTaskKey());
                    throw new CommonException(UserHttpCode.NOT_COMPLETED_AND_CANNOT_RECEIVE);
                }
                checkStatus(newcomerTask.getStatus());
                reward = sendReward(dto.getSlang(), actorData, taskInfo, vo);
                newcomerTask.setStatus(2);
                newcomerTaskDao.update(newcomerTask);
            } else {
                int taskDate = Integer.parseInt(DateHelper.ARABIAN.formatDateInDay2());
                UserTaskData userTask = userTaskDao.getTaskByTaskKey(dto.getUid(), taskDate, dto.getTaskKey());
                if (null == userTask || userTask.getTaskNum() < taskInfo.getLimit()) {
                    logger.info("The task has not been completed and the reward cannot be received. uid={} taskKey={}", dto.getUid(), dto.getTaskKey());
                    throw new CommonException(UserHttpCode.NOT_COMPLETED_AND_CANNOT_RECEIVE);
                }
                checkStatus(userTask.getStatus());
                reward = sendReward(dto.getSlang(), actorData, taskInfo, vo);
                userTask.setStatus(2);
                userTaskDao.update(userTask);
                userTaskRedis.incUserHasRewardCount(dto.getUid(), -1);
            }
        }
        long coinBalance = actorData.getHeartGot();
        if (reward.getResType() == 100) {
            coinBalance += reward.getNum();
        }
        vo.setCoinBalance(coinBalance);
        vo.setLotteryTicketNum(userLotteryTicketsService.getUserTicketsNum(dto.getUid()));
        vo.setReward(reward);
        userTaskRedis.saveTnGetRewardRecord(dto.getTaskKey(), actorData.getTn_id());
        return vo;
    }

    private TaskRewardVO.Reward sendReward(int slang, ActorData actorData, TaskInfo taskInfo, TaskRewardVO vo) {
        String wheelUrl = ServerConfig.isProduct() ? LUCKY_WHEEL_PRO : LUCKY_WHEEL_DEBUG;
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyHandlerService.getConfigData(taskInfo.getResourceKey());
        int num = 0;
        int rewardType = -1;
        if (resourceKeyConfigData != null) {
            ResourceKeyConfigData.ResourceMeta resourceMeta = resourceKeyConfigData.getResourceMetaList().get(0);
            num = resourceMeta.getResourceNumber();
            rewardType = BaseDataResourcesConstant.TYPE_TASK_CENTER_TICKET == resourceMeta.getResourceType() ? 1 :
                    BaseDataResourcesConstant.TYPE_COIN == resourceMeta.getResourceType() ? 0 : resourceMeta.getResourceType();
        } else {
            rewardType = taskInfo.getRewardType();
        }
        if (rewardType == 0) {
            heartRecordDao.changeHeart(actorData.getUid(), num != 0 ? num : taskInfo.getRewardNum(), TITLE, "");
            return new TaskRewardVO.Reward(slang == SLangType.ENGLISH ? "Coins" : "كوينزات", COIN_ICON, 100, taskInfo.getRewardNum(), wheelUrl + "?turntableType=primary");
        } else if (rewardType == 1) {
            userLotteryTicketsService.addTicketsNum(actorData.getUid(), num != 0 ? num : taskInfo.getRewardNum(), taskInfo.getKey());
            return new TaskRewardVO.Reward(slang == SLangType.ENGLISH ? "Raffle Ticket" : "تذكرة اليانصيب", LOTTERY_TICKET_ICON, 0, taskInfo.getRewardNum(), wheelUrl + "?turntableType=advanced");
        } else {
            // 礼盒类型奖励，达成相关等级任务奖励
            return asyncHandleResources(slang, actorData, taskInfo);
        }
    }

    private TaskRewardVO.Reward asyncHandleResources(int slang, ActorData actorData, TaskInfo taskInfo) {

//        TaskInfo.Reward reward = getOneReward(actorData, taskInfo.getKey(), taskInfo.getRewards());
//        if (reward == null) {
//            return new TaskRewardVO.Reward();
//        }
        ResourceKeyConfigData.ResourceMeta resourceMeta = getOneRewardByRes(actorData, taskInfo.getKey());
        if (resourceMeta == null) {
            return new TaskRewardVO.Reward();
        }
        TaskInfo.Reward reward = toTaskInfoReward(resourceMeta);

        String wheelUrl = ServerConfig.isProduct() ? LUCKY_WHEEL_PRO : LUCKY_WHEEL_DEBUG;
        if (reward.getResType() == 0) {
            userLotteryTicketsService.addTicketsNum(actorData.getUid(), reward.getNum(), taskInfo.getKey());
            return new TaskRewardVO.Reward(slang == SLangType.ENGLISH ? "Raffle Ticket" : "تذكرة اليانصيب", LOTTERY_TICKET_ICON, reward.getResType(), reward.getNum(), wheelUrl + "?turntableType=advanced");
        } else if (reward.getResType() == 100) {
            heartRecordDao.changeHeart(actorData.getUid(), reward.getNum(), TITLE, "");
            return new TaskRewardVO.Reward(slang == SLangType.ENGLISH ? "Coins" : "كوينزات", COIN_ICON, reward.getResType(), reward.getNum(), wheelUrl + "?turntableType=primary");
        } else if (reward.getResType() == 101) {
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(actorData.getUid());
            moneyDetailReq.setAtype(A_TYPE);
            moneyDetailReq.setChanged(reward.getNum());
            moneyDetailReq.setTitle(TITLE);
            moneyDetailReq.setDesc("");
            mqSenderService.asyncChargeDiamonds(moneyDetailReq);
            return new TaskRewardVO.Reward(slang == SLangType.ENGLISH ? "Diamonds" : "الماس", DIAMONDS_ICON, reward.getResType(), reward.getNum(), "");
        } else {
//            ResourcesDTO dto = new ResourcesDTO();
//            dto.setUid(actorData.getUid());
//            dto.setResId(String.valueOf(reward.getResId()));
//            dto.setResType(reward.getResType());
//            if (BaseDataResourcesConstant.TYPE_BAG_GIFT == reward.getResType()) {
//                dto.setNum(reward.getNum());
//                dto.setDays(1);
//            } else {
//                dto.setDays(reward.getNum());
//            }
//            dto.setGetWay(3);
//            dto.setActionType(2);
//            dto.setmTime(DateHelper.getNowSeconds());
//            // 自然天过期
//            dto.setGainType(0);
//            mqSenderService.asyncHandleResources(dto);
            resourceKeyHandlerService.sendOneResourceDataNoBroadcast(actorData, resourceMeta, A_TYPE, TITLE, TITLE, 3);
            String rewardName = slang == SLangType.ENGLISH ? reward.getName() : reward.getNameAr();
            if(BaseDataResourcesConstant.TYPE_BAG_GIFT==reward.getResType()){
                rewardName = slang == SLangType.ENGLISH ? "Gift": "هدية";
            }
            return new TaskRewardVO.Reward(rewardName, reward.getIcon(), reward.getResType(), reward.getNum(), "");
        }
    }

    private TaskInfo.Reward getOneReward(ActorData actorData, String key, List<TaskInfo.Reward> rewards) {
        if (CollectionUtils.isEmpty(rewards)) {
            return null;
        }
        if (key.startsWith("on_mic_time")) {
            // 根据是否是新用户，以及是否是同一个设备下的多个账号
            boolean isNewUser = ActorUtils.isNewRegisterActor(actorData.getUid(), 7);
            boolean tnHasGotReward = userTaskRedis.tnHasGotReward(key, actorData.getTn_id());
            int userType;
            if (isNewUser) {
                userType = tnHasGotReward ? 2 : 1;
            } else {
                userType = tnHasGotReward ? 4 : 3;
            }
            Map<Integer, List<TaskInfo.Reward>> userTypeRewardMaps = CollectionUtil.listToKeyListMap(rewards, TaskInfo.Reward::getUserType);
            return userTypeRewardMaps.containsKey(userType) ? userTypeRewardMaps.get(userType).get(0) : null;
        } else {
            // 随机获取奖励中的一个
            Collections.shuffle(rewards);
            return rewards.get(0);
        }
    }

    private ResourceKeyConfigData.ResourceMeta getOneRewardByRes(ActorData actorData, String key) {
        Map<Integer, String> taskMap = STAGE_TASK_MAP.get(key);
        if (taskMap == null) {
            return null;
        }
        String resourceKey = null;
        if (key.startsWith("on_mic_time")) {
            // 根据是否是新用户，以及是否是同一个设备下的多个账号
            boolean isNewUser = ActorUtils.isNewRegisterActor(actorData.getUid(), 7);
            boolean tnHasGotReward = userTaskRedis.tnHasGotReward(key, actorData.getTn_id());
            int userType;
            if (isNewUser) {
                userType = tnHasGotReward ? 2 : 1;
            } else {
                userType = tnHasGotReward ? 4 : 3;
            }
            resourceKey = taskMap.get(userType);
        } else {
            resourceKey = taskMap.get(0);
        }
        if (StringUtils.isEmpty(resourceKey)) {
            return null;
        }
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyHandlerService.getConfigData(resourceKey);
        if (resourceKeyConfigData == null) {
            return null;
        }
        List<ResourceKeyConfigData.ResourceMeta> resourceMetaList = resourceKeyConfigData.getResourceMetaList();
        Collections.shuffle(resourceMetaList);
        ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaList.get(0);
        return resourceMeta;
    }

    private TaskInfo.Reward toTaskInfoReward(ResourceKeyConfigData.ResourceMeta resourceMeta) {
        TaskInfo.Reward reward = new TaskInfo.Reward();
        int resType = 0; // 游戏抽奖券 0  心心 100   钻石 101
        int resourceType = resourceMeta.getResourceType();
        resType = resourceType == BaseDataResourcesConstant.TYPE_TASK_CENTER_TICKET ? 0 :
                resourceType == BaseDataResourcesConstant.TYPE_COIN ? 100 :
                        resourceType == BaseDataResourcesConstant.TYPE_DIAMOND ? 101 : resourceType;
        reward.setResType(resType);
        reward.setIcon(resourceMeta.getResourceIcon());
        reward.setName(resourceMeta.getResourceNameEn());
        reward.setNameAr(resourceMeta.getResourceNameAr());
        reward.setNum(resourceMeta.getResourceNumber());
        reward.setResIdPro(resourceMeta.getResourceId());
        reward.setResId(resourceMeta.getResourceId());
        reward.setUserType(0);
        return reward;
    }


    private void checkStatus(int status) {
        if (status == 1) {
            return;
        }
        if (status == 0) {
            logger.info("The task has not been completed and the reward cannot be received.");
            throw new CommonException(UserHttpCode.NOT_COMPLETED_AND_CANNOT_RECEIVE);
        }
        if (status == 2) {
            logger.info("This reward has been gotten.");
            throw new CommonException(UserHttpCode.THIS_REWARD_HAS_BEEN_GOTTEN);
        }
    }

    public Map<String, TaskInfo> getTaskInfoMap() {
        return TASK_MAP;
    }

    //=================================== 以下是旧版本的任务中心 =====================================

    public DailyTaskVO getDailyTasksCenter(DailyTaskDTO req) {
        DailyTaskVO vo = getDailyTaskInstance();
        SignTableData signTableData = signTableDao.findData(req.getUid());
        String signDateStr = DateHelper.ARABIAN.formatDateInDay();
        if (null != signTableData && signDateStr.equals(signTableData.getLast_sign())) {
            vo.setSign_today(1);
            vo.setSign_end_time((int) (DateHelper.DEFAULT.getDayOffset(1) / 1000) - DateHelper.getNowSeconds());
            vo.getMission_task().incrTaskValue();
            vo.getMission_task().incrFinishValue();
        } else {
            vo.getMission_task().incrTaskValue();
        }
        String dateKey = DateHelper.ARABIAN.formatDateInDay2();
        fillMissionTask(req, vo, dateKey);
        fillOnceTask(req, vo, dateKey);
        fillDailyTask(req, vo, dateKey);
        fillMoreTask(req, vo, dateKey);
        int finishPercent = vo.getMission_task().getFinish_value() * 100 / vo.getMission_task().getTask_value();
        vo.getMission_task().setFinish_percent(finishPercent);
        if (vo.getMission_task().getFinish_value() >= vo.getMission_task().getTask_value() && vo.getMission_task().getAward_status() == 0) {
            vo.getMission_task().setAward_status(1);
            try (DistributeLock lock = new DistributeLock(DailyTaskMqConsumer.DAILY_TASK_LOCK_KEY + req.getUid())) {
                lock.lock();
                DailyTaskManager taskManager = dailyTaskManagerDao.getTaskById(req.getUid(), dateKey, vo.getMission_task().getTask_id());
                if (taskManager == null) {
                    taskManager = new DailyTaskManager();
                    taskManager.setUid(req.getUid());
                    taskManager.setDateKey(Integer.parseInt(dateKey));
                    taskManager.setTaskId(vo.getMission_task().getTask_id());
                    taskManager.setTaskValue(vo.getMission_task().getTask_value());
                    taskManager.setAwardStatus(1);
                    taskManager.setcTime(DateHelper.getNowSeconds());
                    taskManager.setmTime(DateHelper.getNowSeconds());
                    dailyTaskManagerDao.insert(taskManager);
                }
            }
        }
        vo.setGetTaskNum(0);
        return vo;
    }

    public DrawRewardsVO drawRewards(DailyTaskDTO req) {
        String dateKey = DateHelper.ARABIAN.formatDateInDay2();
        int curTime = DateHelper.getNowSeconds();
        Map<Integer, TaskVO> taskMap = getTaskMap();
        TaskVO dailyTask = taskMap.get(req.getTask_id());
        if (dailyTask == null) {
            logger.error("can not find daily task data. taskId={}", req.getTask_id());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        try (DistributeLock lock = new DistributeLock(getLockKey(req.getUid()))) {
            lock.lock();
            List<Integer> noviceTaskIds = taskCenterConfig.getNovice_task().stream().map(TaskVO::getTask_id).collect(Collectors.toList());
            if (noviceTaskIds.contains(req.getTask_id())) {
                OnceTaskRecord onceTaskRecord = onceTaskService.getByUidAndTaskId(req.getUid(), req.getTask_id());
                if (onceTaskRecord == null || onceTaskRecord.getAwardStatus() != 1) {
                    String msg = String.format("task_id %s unfinished", req.getTask_id());
                    logger.info("{} uid={}", msg, req.getUid());
                    throw new CommonException(new HttpCode(1, msg));
                }
                drawRewards(req.getUid(), dailyTask);
                onceTaskRecord.setAwardStatus(2);
                onceTaskRecord.setmTime(curTime);
                onceTaskService.updateById(onceTaskRecord);
            } else {
                DailyTaskManager dailyTaskManager = dailyTaskManagerDao.getTaskById(req.getUid(), dateKey, req.getTask_id());
                if (dailyTaskManager == null || dailyTaskManager.getAwardStatus() != 1) {
                    String msg = String.format("task_id %s unfinished", req.getTask_id());
                    logger.info("{} uid={}", msg, req.getUid());
                    throw new CommonException(new HttpCode(1, msg));
                }
                drawRewards(req.getUid(), dailyTask);
                dailyTaskManager.setAwardStatus(2);
                dailyTaskManager.setmTime(curTime);
                dailyTaskManagerDao.update(dailyTaskManager);
            }
        }
        ActorData actorData = actorDao.getActorData(req.getUid());
        return new DrawRewardsVO(
                actorData != null ? actorData.getHeartGot() : 0,
                actorData != null ? actorData.getBeans() : 0,
                dailyTask.getAward_list());
    }

    private String getLockKey(String uid) {
        return "getUserTaskReward_" + uid;
    }

    public Map<Integer, TaskVO> getTaskMap() {
        List<TaskVO> list = new ArrayList<>();
        TaskVO missionTask = new TaskVO();
        BeanUtils.copyProperties(taskCenterConfig.getMission_task(), missionTask);
        list.add(missionTask);
        list.addAll(deepCopy(taskCenterConfig.getNovice_task()));
        list.addAll(deepCopy(taskCenterConfig.getDaily_task()));
        list.addAll(deepCopy(taskCenterConfig.getMore_task()));
        return list.stream().collect(Collectors.toMap(TaskVO::getTask_id, Function.identity()));
    }

    private DailyTaskVO getDailyTaskInstance() {
        DailyTaskVO vo = new DailyTaskVO();
        TaskVO missionTask = new TaskVO();
        BeanUtils.copyProperties(taskCenterConfig.getMission_task(), missionTask);
        vo.setMission_task(missionTask);
        vo.setNovice_task(deepCopy(taskCenterConfig.getNovice_task()));
        vo.setDaily_task(deepCopy(taskCenterConfig.getDaily_task()));
        vo.setMore_task(deepCopy(taskCenterConfig.getMore_task()));
        return vo;
    }

    private void drawRewards(String uid, TaskVO dailyTask) {
        if (CollectionUtils.isEmpty(dailyTask.getAward_list())) {
            logger.info("task award list is empty. taskId={}", dailyTask.getTask_id());
            return;
        }
        for (TaskVO.AwardVO awardData : dailyTask.getAward_list()) {
            // 下发奖励
            if ("Diamond".equals(awardData.getAward_title())) {
                MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
                moneyDetailReq.setRandomId();
                moneyDetailReq.setUid(uid);
                moneyDetailReq.setAtype(A_TYPE);
                moneyDetailReq.setChanged(awardData.getAward_num());
                moneyDetailReq.setTitle(TITLE);
                moneyDetailReq.setDesc("daily task");
                mqSenderService.asyncChargeDiamonds(moneyDetailReq);
            } else {
                heartRecordDao.changeHeart(uid, awardData.getAward_num(), TITLE, "daily task");
            }
        }
    }

    /**
     * 99	My task progress
     */
    private void fillMissionTask(DailyTaskDTO req, DailyTaskVO vo, String dateKey) {
        DailyTaskManager missionTask = dailyTaskManagerDao.getMissionTaskByUid(req.getUid(), dateKey);
        vo.getMission_task().setAward_status(null == missionTask ? 0 : missionTask.getAwardStatus());
    }

    /**
     * 17	Open system notifications
     * 18	Upload 5 photos
     * 19	Add 5 tags
     */
    private void fillOnceTask(DailyTaskDTO req, DailyTaskVO vo, String dateKey) {
        if (CollectionUtils.isEmpty(vo.getNovice_task())) {
            return;
        }
        List<OnceTaskRecord> onceTaskRecordList = onceTaskService.getByUid(req.getUid());
        Map<Integer, OnceTaskRecord> onceTaskRecordMap;
        if (!CollectionUtils.isEmpty(onceTaskRecordList)) {
            onceTaskRecordMap = onceTaskRecordList.stream().filter(distinctByKey(OnceTaskRecord::getTaskId)).collect(Collectors.toMap(OnceTaskRecord::getTaskId, Function.identity()));
        } else {
            onceTaskRecordMap = Collections.emptyMap();
        }
        int systemNoticeTask = 17;
        Iterator<TaskVO> iterator = vo.getNovice_task().iterator();
        while (iterator.hasNext()) {
            TaskVO taskVO = iterator.next();
            OnceTaskRecord onceTaskRecord = onceTaskRecordMap.get(taskVO.getTask_id());
            if (onceTaskRecord != null) {
                taskVO.setFinish_value(onceTaskRecord.getAwardStatus() == 1 ? 1 : 0);
                taskVO.setAward_status(onceTaskRecord.getAwardStatus() == 1 ? 1 : 0);
                if (onceTaskRecord.getAwardStatus() == 1) {
                    // 完成任务未领取奖励
                    vo.getMission_task().incrFinishValue();
                } else if (onceTaskRecord.getAwardStatus() == 2) {
                    // 完成任务并已领取奖励
                    if (dateKey.equals(onceTaskRecord.getDateKey() + "")) {
                        vo.getMission_task().incrTaskValue();
                        vo.getMission_task().incrFinishValue();
                    }
                    iterator.remove();
                    continue;
                }
                // 开启系统通知任务
                if (onceTaskRecord.getTaskId() == systemNoticeTask) {
                    if (req.getSwitch_notice() == 0 && onceTaskRecord.getAwardStatus() == 1) {
                        onceTaskRecord.setAwardStatus(0);
                        taskVO.setFinish_value(0);
                        taskVO.setAward_status(0);
                    }
                    if (req.getSwitch_notice() == 1 && onceTaskRecord.getAwardStatus() == 0) {
                        onceTaskRecord.setAwardStatus(1);
                        taskVO.setFinish_value(1);
                        taskVO.setAward_status(1);
                    }
                    onceTaskService.updateById(onceTaskRecord);
                }
                vo.getMission_task().incrTaskValue();
            } else {
                vo.getMission_task().incrTaskValue();
                // 开启系统通知任务
                if (req.getSwitch_notice() != 0 && taskVO.getTask_id() == systemNoticeTask) {
                    taskVO.setFinish_value(1);
                    taskVO.setAward_status(1);
                    OnceTaskRecord taskRecord = new OnceTaskRecord();
                    taskRecord.setUid(req.getUid());
                    taskRecord.setTaskId(systemNoticeTask);
                    taskRecord.setDateKey(Integer.parseInt(dateKey));
                    taskRecord.setcTime(DateHelper.getNowSeconds());
                    taskRecord.setmTime(DateHelper.getNowSeconds());
                    taskRecord.setAwardStatus(1);
                    onceTaskService.save(taskRecord);
                    vo.getMission_task().incrFinishValue();
                }
            }
        }
        // 排序： 已完成 > 未完成
        Comparator<TaskVO> awardStatusAsc = Comparator.comparing(o -> o.getAward_status() == 1 ? 0 : 2);
        Comparator<TaskVO> mtimeDesc = Comparator.comparing(TaskVO::getM_time).reversed();
        vo.getNovice_task().sort(awardStatusAsc.thenComparing(mtimeDesc));
    }

    /**
     * 1    Send 5 messages in the room
     * 5    Take the Mic and chat for 10 mins
     * 7    Send gift 3 times
     */
    private void fillDailyTask(DailyTaskDTO req, DailyTaskVO vo, String dateKey) {
        if (CollectionUtils.isEmpty(vo.getDaily_task())) {
            return;
        }
        for (TaskVO taskVO : vo.getDaily_task()) {
            DailyTaskManager taskManager = dailyTaskManagerDao.getTaskById(req.getUid(), dateKey, taskVO.getTask_id());
            taskVO.setFinish_value(taskManager != null ? taskManager.getTaskValue() : 0);
            taskVO.setFinish_value(Math.min(taskVO.getFinish_value(), taskVO.getTask_value()));
            taskVO.setAward_status(taskManager != null ? taskManager.getAwardStatus() : 0);
            taskVO.setC_time(taskManager != null ? taskManager.getcTime() : 0);
            taskVO.setM_time(taskManager != null ? taskManager.getmTime() : 0);
            int targetTaskValue = taskVO.getTask_value();
            if (taskVO.getTime_calculation() == 1) {
                taskVO.setTask_value(taskVO.getTask_value() / 60);
                taskVO.setFinish_value(taskVO.getFinish_value() / 60);
            }
            vo.getMission_task().incrTaskValue();
            if (taskManager != null && taskManager.getTaskValue() >= targetTaskValue) {
                vo.getMission_task().incrFinishValue();
            }
        }
        // 排序： 已完成未领取奖励 > 未完成 > 已完成并已领取奖励
        Comparator<TaskVO> awardStatusAsc = Comparator.comparing(o -> o.getAward_status() == 0 ? 15 : o.getAward_status() * 10);
        Comparator<TaskVO> mtimeDesc = Comparator.comparing(TaskVO::getM_time).reversed();
        vo.getDaily_task().sort(awardStatusAsc.thenComparing(mtimeDesc));
    }

    /**
     * 3    Create a room
     * 9    Join the Roshambo once
     * 10   Play the lucky wheel once
     * 16   Watching video for 3 minutes
     */
    private void fillMoreTask(DailyTaskDTO req, DailyTaskVO vo, String dateKey) {
        if (CollectionUtils.isEmpty(vo.getMore_task())) {
            return;
        }
        for (TaskVO taskVO : vo.getMore_task()) {
            DailyTaskManager taskManager = dailyTaskManagerDao.getTaskById(req.getUid(), dateKey, taskVO.getTask_id());
            taskVO.setFinish_value(taskManager != null ? taskManager.getTaskValue() : 0);
            taskVO.setFinish_value(Math.min(taskVO.getFinish_value(), taskVO.getTask_value()));
            taskVO.setAward_status(taskManager != null ? taskManager.getAwardStatus() : 0);
            taskVO.setC_time(taskManager != null ? taskManager.getcTime() : 0);
            taskVO.setM_time(taskManager != null ? taskManager.getmTime() : 0);
            int targetTaskValue = taskVO.getTask_value();
            if (taskVO.getTime_calculation() == 1) {
                taskVO.setTask_value(taskVO.getTask_value() / 60);
                taskVO.setFinish_value(taskVO.getFinish_value() / 60);
            }
            vo.getMission_task().incrTaskValue();
            if (taskManager != null && taskManager.getTaskValue() >= targetTaskValue) {
                vo.getMission_task().incrFinishValue();
            }
        }
        Comparator<TaskVO> awardStatusAsc = Comparator.comparing(o -> o.getAward_status() == 0 ? 15 : o.getAward_status() * 10);
        Comparator<TaskVO> mtimeDesc = Comparator.comparing(TaskVO::getM_time).reversed();
        vo.getMore_task().sort(awardStatusAsc.thenComparing(mtimeDesc));
    }

    private <T> List<T> deepCopy(List<T> srcList) {
        try {
            ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
            ObjectOutputStream out = new ObjectOutputStream(byteOut);
            out.writeObject(srcList);
            ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
            ObjectInputStream inStream = new ObjectInputStream(byteIn);
            return (List<T>) inStream.readObject();
        } catch (Exception e) {
            logger.error("deepCopy error. {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }
}
