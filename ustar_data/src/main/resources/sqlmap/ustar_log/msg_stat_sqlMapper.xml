<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.ustar_log.MsgStatMapper">
    <resultMap id="baseResultMap" type="com.quhong.mysql.data.MsgStatData">
        <result column="id" property="id"></result>
        <result column="cmd" property="cmd"></result>
        <result column="msg_id" property="msgId"></result>
        <result column="to_uid" property="toUid"></result>
        <result column="room_id" property="roomId"></result>
        <result column="send_test_count" property="sendTestCount"></result>
        <result column="cost_time_millis" property="costTimeMillis"></result>
        <result column="status" property="sendUserCount"></result>
        <result column="ctime" property="ctime"></result>
        <result column="os" property="os"></result>
    </resultMap>
    <sql id="baseSql">
        cmd,msg_id,to_uid,room_id,send_test_count,cost_time_millis,status,ctime,os
    </sql>
    <sql id="itemSql">
        #{item.cmd},#{item.msgId},#{item.toUid},#{item.roomId},#{item.sendTestCount},#{item.costTimeMillis},#{item.status},#{item.ctime},#{item.os}
    </sql>
    <insert id="insert" useGeneratedKeys="true" keyProperty="item.id" keyColumn="id">
        insert into t_msg_stat_${tableSuffix} (<include refid="baseSql"/>) values (<include refid="itemSql"/>)
    </insert>
    <update id="update">
        update t_msg_stat_${tableSuffix} set send_test_count=#{item.sendTestCount},cost_time_millis={item.costTimeMillis},status=#{item.status} where id=#{item.id} limit 1
    </update>
</mapper>
