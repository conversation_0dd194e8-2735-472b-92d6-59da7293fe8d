package com.quhong.mongo.data;

import com.quhong.mongo.dao.FloatScreenSourceDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = FloatScreenSourceDao.TABLE_NAME)
public class FloatScreenSourceData {
    @Id
    private ObjectId _id;
    private String name;
    private String namear;
    private String label_name;
    private String label_namear;
    private String screen_icon;
    private String screen_source;
    private String screenSourceVap;
    private int screen_id; //
    private long c_time; //
    private int status; // 状态    1 代表有效， 0代表失效
    private int seven_times_cost; //
    private int is_activity; //  0 代表商店  1代表抽奖获, 活动得
    private int forder;  // 排序
    private int buy_type; // 购买类型  0钻石  1: 心心
    private int beans;  // 售价
    private int days;  // 售卖天数
    private int is_new;  // 上新标识字段 1: 是

    private int item_type; // 1: 奖励相关  2: 荣誉相关 3: vip相关  4: 用户等级相关 5 商店购买
    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNamear() {
        return namear;
    }

    public void setNamear(String namear) {
        this.namear = namear;
    }

    public String getLabel_name() {
        return label_name;
    }

    public void setLabel_name(String label_name) {
        this.label_name = label_name;
    }

    public String getLabel_namear() {
        return label_namear;
    }

    public void setLabel_namear(String label_namear) {
        this.label_namear = label_namear;
    }

    public String getScreen_icon() {
        return screen_icon;
    }

    public void setScreen_icon(String screen_icon) {
        this.screen_icon = screen_icon;
    }

    public String getScreen_source() {
        return screen_source;
    }

    public void setScreen_source(String screen_source) {
        this.screen_source = screen_source;
    }

    public String getScreenSourceVap() {
        return screenSourceVap;
    }

    public void setScreenSourceVap(String screenSourceVap) {
        this.screenSourceVap = screenSourceVap;
    }

    public int getScreen_id() {
        return screen_id;
    }

    public void setScreen_id(int screen_id) {
        this.screen_id = screen_id;
    }

    public long getC_time() {
        return c_time;
    }

    public void setC_time(long c_time) {
        this.c_time = c_time;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getSeven_times_cost() {
        return seven_times_cost;
    }

    public void setSeven_times_cost(int seven_times_cost) {
        this.seven_times_cost = seven_times_cost;
    }

    public int getIs_activity() {
        return is_activity;
    }

    public void setIs_activity(int is_activity) {
        this.is_activity = is_activity;
    }

    public int getForder() {
        return forder;
    }

    public void setForder(int forder) {
        this.forder = forder;
    }

    public int getBuy_type() {
        return buy_type;
    }

    public void setBuy_type(int buy_type) {
        this.buy_type = buy_type;
    }

    public int getBeans() {
        return beans;
    }

    public void setBeans(int beans) {
        this.beans = beans;
    }

    public int getDays() {
        return days;
    }

    public void setDays(int days) {
        this.days = days;
    }

    public int getItem_type() {
        return item_type;
    }

    public void setItem_type(int item_type) {
        this.item_type = item_type;
    }

    public int getIs_new() {
        return is_new;
    }

    public void setIs_new(int is_new) {
        this.is_new = is_new;
    }
}
