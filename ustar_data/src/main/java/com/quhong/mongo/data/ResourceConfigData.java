package com.quhong.mongo.data;

import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.mongo.dao.ResourceConfigDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2023/4/24
 */
@Document(collection = ResourceConfigDao.TABLE_NAME)
public class ResourceConfigData {

    @Id
    private ObjectId _id;
    private int resourceId; // 资源id
    /**
     * @see BaseDataResourcesConstant
     */
    private int resourceType; // 资源类型 11进场通知 12荣誉称号
    private String name; // 资源名称
    private String nameAr; // 资源名称
    private String icon; // 资源图标
    private String iconAr; // 资源阿语图标
    private String desc; // 资源描述
    private String descAr; // 资源阿语描述
    private int animationType;  // 动画类型 0 webp 1：svg 2:mp4
    private String animationUrl; // 动画url
    private String animationUrlAr; // 阿语动画url
    private String videoUrl; // 音效url
    private String resourceUrl; // 资源下载路径
    private String resourceUrlAr; // 阿语资源下载路径
    private String resourceMd5; // 资源MD5值
    private int itemType; // 1:奖励相关 2:荣誉相关 3:vip相关 4:用户等级相关 5:商店购买
    private int currencyType; // 货币类型：0钻石 1金币
    private int price; // 售卖价格
    private int days;  // 售卖天数
    private int status; // 状态 0无效 1有效
    private int isNew; // 上新标识字段 0否 1时
    private int order; // 排序字段
    private int ctime; // 创建时间

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public int getResourceId() {
        return resourceId;
    }

    public void setResourceId(int resourceId) {
        this.resourceId = resourceId;
    }

    public int getResourceType() {
        return resourceType;
    }

    public void setResourceType(int resourceType) {
        this.resourceType = resourceType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public int getAnimationType() {
        return animationType;
    }

    public void setAnimationType(int animationType) {
        this.animationType = animationType;
    }

    public String getAnimationUrl() {
        return animationUrl;
    }

    public void setAnimationUrl(String animationUrl) {
        this.animationUrl = animationUrl;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getResourceUrl() {
        return resourceUrl;
    }

    public void setResourceUrl(String resourceUrl) {
        this.resourceUrl = resourceUrl;
    }

    public String getResourceMd5() {
        return resourceMd5;
    }

    public void setResourceMd5(String resourceMd5) {
        this.resourceMd5 = resourceMd5;
    }

    public int getItemType() {
        return itemType;
    }

    public void setItemType(int itemType) {
        this.itemType = itemType;
    }

    public int getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(int currencyType) {
        this.currencyType = currencyType;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public int getDays() {
        return days;
    }

    public void setDays(int days) {
        this.days = days;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getIsNew() {
        return isNew;
    }

    public void setIsNew(int isNew) {
        this.isNew = isNew;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public String getIconAr() {
        return iconAr;
    }

    public void setIconAr(String iconAr) {
        this.iconAr = iconAr;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDescAr() {
        return descAr;
    }

    public void setDescAr(String descAr) {
        this.descAr = descAr;
    }

    public String getAnimationUrlAr() {
        return animationUrlAr;
    }

    public void setAnimationUrlAr(String animationUrlAr) {
        this.animationUrlAr = animationUrlAr;
    }

    public String getResourceUrlAr() {
        return resourceUrlAr;
    }

    public void setResourceUrlAr(String resourceUrlAr) {
        this.resourceUrlAr = resourceUrlAr;
    }
}
