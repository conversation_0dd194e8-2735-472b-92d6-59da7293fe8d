package com.quhong.mongo.data;

import com.quhong.mongo.dao.LuckyLotteryActivityDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Map;

/**
 * 幸运抽奖活动模板
 */
@Document(collection = LuckyLotteryActivityDao.TABLE_NAME)
public class LuckyLotteryActivity {

    @Id
    private ObjectId _id;
    private String acNameEn; // 英文活动名
    private String acNameAr; // 阿语活动名
    private String acUrl; // h5地址(自动生成)
    private Integer startTime; // 活动开始时间
    private Integer endTime; // 活动结束时间
    private Integer status; // 1 是否已结束
    private Integer ctime; // 创建时间
    private Integer mtime; // 更新时间
    private ActivityConfig activityConfig; // 活动配置
    private PartakeConfig partakeConfig; // 参与条件配置
    private List<RewardConfigDetail> rewardConfigList; // 奖励配置列表
    // private Map<String, RewardConfigDetail> rewardConfigMap; // 奖励配置列表


    public static class ActivityConfig {
        private int gameType; // 0转盘 1卡牌 2砸蛋 3九宫格 4扭蛋机
        private String ruleTextEn; // 规则英语
        private String ruleTextAr; // 规则阿语
        private int showRotate; // 是否展示轮播消息 0轮播 1 不轮播
        private int showJoin; //是否展示参与人数 0展示 1 不展示
        // private int awardShowSize; // 转盘奖励展示大小  0: 小  1: 中  2: 大
        private String topBanner; // 顶部banner
        private String topBannerAr; // 阿语顶部banner
        private String backgroundRgba; // 十六进制颜色代码
        private String backgroundUrl; // 页面背景图url
        private String backgroundUrlAr; // 页面阿语背景图url
        private String turntableBgUrl; // 转盘背景图片url
        private String lotteryButton; // 抽奖按钮url
        private String lotteryButtonAr; // 阿语抽奖按钮url
        private String lotteryPopUp; // 中奖弹窗url
        private String lotteryPopUpAr; // 阿语中奖弹窗url
        // private String closeButton; // 关闭按钮url

        // 调研
        private int survey; // 活动调研  0: 否  1: 是
        private String surveyTextEn; // 跳转文本英语
        private String surveyTextAr; // 跳转文本啊语
        private String surveyLinkEn; // 跳转链接英语
        private String surveyLinkAr; // 跳转链接啊语

        // 字体颜色
        private String ruleTextColor; // 规则文本颜色
        private String turnTextColor; // 转盘文本颜色
        private String rewardTextColor; // 中奖文本颜色

        //抽奖
        private String frontIcon;   // 抽奖卡牌前景图
        private String backIcon;   // 抽奖卡牌背景图


        public int getGameType() {
            return gameType;
        }

        public void setGameType(int gameType) {
            this.gameType = gameType;
        }

        public String getRuleTextEn() {
            return ruleTextEn;
        }

        public void setRuleTextEn(String ruleTextEn) {
            this.ruleTextEn = ruleTextEn;
        }

        public String getRuleTextAr() {
            return ruleTextAr;
        }

        public void setRuleTextAr(String ruleTextAr) {
            this.ruleTextAr = ruleTextAr;
        }

        public int getShowRotate() {
            return showRotate;
        }

        public void setShowRotate(int showRotate) {
            this.showRotate = showRotate;
        }

        public int getShowJoin() {
            return showJoin;
        }

        public void setShowJoin(int showJoin) {
            this.showJoin = showJoin;
        }

        public String getTopBanner() {
            return topBanner;
        }

        public void setTopBanner(String topBanner) {
            this.topBanner = topBanner;
        }

        public String getTopBannerAr() {
            return topBannerAr;
        }

        public void setTopBannerAr(String topBannerAr) {
            this.topBannerAr = topBannerAr;
        }

        public String getBackgroundRgba() {
            return backgroundRgba;
        }

        public void setBackgroundRgba(String backgroundRgba) {
            this.backgroundRgba = backgroundRgba;
        }

        public String getBackgroundUrl() {
            return backgroundUrl;
        }

        public void setBackgroundUrl(String backgroundUrl) {
            this.backgroundUrl = backgroundUrl;
        }

        public String getBackgroundUrlAr() {
            return backgroundUrlAr;
        }

        public void setBackgroundUrlAr(String backgroundUrlAr) {
            this.backgroundUrlAr = backgroundUrlAr;
        }

        public String getTurntableBgUrl() {
            return turntableBgUrl;
        }

        public void setTurntableBgUrl(String turntableBgUrl) {
            this.turntableBgUrl = turntableBgUrl;
        }

        public String getLotteryButton() {
            return lotteryButton;
        }

        public void setLotteryButton(String lotteryButton) {
            this.lotteryButton = lotteryButton;
        }

        public String getLotteryButtonAr() {
            return lotteryButtonAr;
        }

        public void setLotteryButtonAr(String lotteryButtonAr) {
            this.lotteryButtonAr = lotteryButtonAr;
        }

        public String getLotteryPopUp() {
            return lotteryPopUp;
        }

        public void setLotteryPopUp(String lotteryPopUp) {
            this.lotteryPopUp = lotteryPopUp;
        }

        public String getLotteryPopUpAr() {
            return lotteryPopUpAr;
        }

        public void setLotteryPopUpAr(String lotteryPopUpAr) {
            this.lotteryPopUpAr = lotteryPopUpAr;
        }


        public int getSurvey() {
            return survey;
        }

        public void setSurvey(int survey) {
            this.survey = survey;
        }

        public String getSurveyTextEn() {
            return surveyTextEn;
        }

        public void setSurveyTextEn(String surveyTextEn) {
            this.surveyTextEn = surveyTextEn;
        }

        public String getSurveyTextAr() {
            return surveyTextAr;
        }

        public void setSurveyTextAr(String surveyTextAr) {
            this.surveyTextAr = surveyTextAr;
        }

        public String getSurveyLinkEn() {
            return surveyLinkEn;
        }

        public void setSurveyLinkEn(String surveyLinkEn) {
            this.surveyLinkEn = surveyLinkEn;
        }

        public String getSurveyLinkAr() {
            return surveyLinkAr;
        }

        public void setSurveyLinkAr(String surveyLinkAr) {
            this.surveyLinkAr = surveyLinkAr;
        }

        public String getRuleTextColor() {
            return ruleTextColor;
        }

        public void setRuleTextColor(String ruleTextColor) {
            this.ruleTextColor = ruleTextColor;
        }

        public String getTurnTextColor() {
            return turnTextColor;
        }

        public void setTurnTextColor(String turnTextColor) {
            this.turnTextColor = turnTextColor;
        }

        public String getRewardTextColor() {
            return rewardTextColor;
        }

        public void setRewardTextColor(String rewardTextColor) {
            this.rewardTextColor = rewardTextColor;
        }

        public String getFrontIcon() {
            return frontIcon;
        }

        public void setFrontIcon(String frontIcon) {
            this.frontIcon = frontIcon;
        }

        public String getBackIcon() {
            return backIcon;
        }

        public void setBackIcon(String backIcon) {
            this.backIcon = backIcon;
        }
    }

    public static class PartakeConfig {
        private Integer partakeType; // 0: 免费次数、 1: 心心货币、 2: 钻石货币
        private Integer unitPrice; // 抽奖单价   如果是免费抽， 就是要几个免费次数来抽
        private Integer roundFreeNums; // 当partakeType选择为0时 赠送给用户每人每天抽奖次数

        private Integer poolSize; // 每轮奖池大小
        private Integer drawLimit; // 是否限制每人抽奖总次数 0:否  1:是
        private Integer drawNums; // 每人抽奖总次数
        private Integer dailyLimit; // 是否限制每人每天抽奖次数 0:否  1:是
        private Integer dailyNums;  // 每人每天抽奖次数

        public Integer getPartakeType() {
            return partakeType;
        }

        public void setPartakeType(Integer partakeType) {
            this.partakeType = partakeType;
        }

        public Integer getUnitPrice() {
            return unitPrice;
        }

        public void setUnitPrice(Integer unitPrice) {
            this.unitPrice = unitPrice;
        }

        public Integer getRoundFreeNums() {
            return roundFreeNums;
        }

        public void setRoundFreeNums(Integer roundFreeNums) {
            this.roundFreeNums = roundFreeNums;
        }

        public Integer getPoolSize() {
            return poolSize;
        }

        public void setPoolSize(Integer poolSize) {
            this.poolSize = poolSize;
        }

        public Integer getDrawLimit() {
            return drawLimit;
        }

        public void setDrawLimit(Integer drawLimit) {
            this.drawLimit = drawLimit;
        }

        public Integer getDrawNums() {
            return drawNums;
        }

        public void setDrawNums(Integer drawNums) {
            this.drawNums = drawNums;
        }

        public Integer getDailyLimit() {
            return dailyLimit;
        }

        public void setDailyLimit(Integer dailyLimit) {
            this.dailyLimit = dailyLimit;
        }

        public Integer getDailyNums() {
            return dailyNums;
        }

        public void setDailyNums(Integer dailyNums) {
            this.dailyNums = dailyNums;
        }
    }


    public static class RewardConfigDetail {
        private double rewardPercent;  // 所占百分比
        // 资源类型 背包礼物:gift 麦位框:mic 气泡框:buddle 入场动画:ride 麦位声波:ripple 钻石:diamond 勋章:badge 浮屏:float_screen 钻石:diamond
        private String rewardIndex;
        private String rewardType;
        private Integer sourceId; // 奖励资源id
        private String rewardIcon; // 奖励资源介绍图片
        private Integer rewardTime; //资源时长（天） 0永久
        private Integer rewardNum; // 礼物数量 可能为空
        private String rewardNameEn; // 奖励名称
        private String rewardNameAr; // 奖励名称阿语

        private String rewardRemark; // 奖励备注(弹窗说明)
        private String rewardRemarkAr; // 阿语奖励备注(弹窗说明)

        // web配置和业务无关
        private Integer rewardTimes;
        private Boolean disabled;

        public double getRewardPercent() {
            return rewardPercent;
        }

        public void setRewardPercent(double rewardPercent) {
            this.rewardPercent = rewardPercent;
        }

        public String getRewardIndex() {
            return rewardIndex;
        }

        public void setRewardIndex(String rewardIndex) {
            this.rewardIndex = rewardIndex;
        }

        public String getRewardType() {
            return rewardType;
        }

        public void setRewardType(String rewardType) {
            this.rewardType = rewardType;
        }

        public Integer getSourceId() {
            return sourceId;
        }

        public void setSourceId(Integer sourceId) {
            this.sourceId = sourceId;
        }

        public String getRewardIcon() {
            return rewardIcon;
        }

        public void setRewardIcon(String rewardIcon) {
            this.rewardIcon = rewardIcon;
        }

        public Integer getRewardTime() {
            return rewardTime;
        }

        public void setRewardTime(Integer rewardTime) {
            this.rewardTime = rewardTime;
        }

        public Integer getRewardNum() {
            return rewardNum;
        }

        public void setRewardNum(Integer rewardNum) {
            this.rewardNum = rewardNum;
        }

        public String getRewardNameEn() {
            return rewardNameEn;
        }

        public void setRewardNameEn(String rewardNameEn) {
            this.rewardNameEn = rewardNameEn;
        }

        public String getRewardNameAr() {
            return rewardNameAr;
        }

        public void setRewardNameAr(String rewardNameAr) {
            this.rewardNameAr = rewardNameAr;
        }

        public String getRewardRemark() {
            return rewardRemark;
        }

        public void setRewardRemark(String rewardRemark) {
            this.rewardRemark = rewardRemark;
        }

        public String getRewardRemarkAr() {
            return rewardRemarkAr;
        }

        public void setRewardRemarkAr(String rewardRemarkAr) {
            this.rewardRemarkAr = rewardRemarkAr;
        }

        public Integer getRewardTimes() {
            return rewardTimes;
        }

        public void setRewardTimes(Integer rewardTimes) {
            this.rewardTimes = rewardTimes;
        }

        public Boolean getDisabled() {
            return disabled;
        }

        public void setDisabled(Boolean disabled) {
            this.disabled = disabled;
        }
    }


    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getAcNameEn() {
        return acNameEn;
    }

    public void setAcNameEn(String acNameEn) {
        this.acNameEn = acNameEn;
    }

    public String getAcNameAr() {
        return acNameAr;
    }

    public void setAcNameAr(String acNameAr) {
        this.acNameAr = acNameAr;
    }

    public String getAcUrl() {
        return acUrl;
    }

    public void setAcUrl(String acUrl) {
        this.acUrl = acUrl;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public ActivityConfig getActivityConfig() {
        return activityConfig;
    }

    public void setActivityConfig(ActivityConfig activityConfig) {
        this.activityConfig = activityConfig;
    }

    public PartakeConfig getPartakeConfig() {
        return partakeConfig;
    }

    public void setPartakeConfig(PartakeConfig partakeConfig) {
        this.partakeConfig = partakeConfig;
    }

    public List<RewardConfigDetail> getRewardConfigList() {
        return rewardConfigList;
    }

    public void setRewardConfigList(List<RewardConfigDetail> rewardConfigList) {
        this.rewardConfigList = rewardConfigList;
    }

    // public Map<String, RewardConfigDetail> getRewardConfigMap() {
    //     return rewardConfigMap;
    // }
    //
    // public void setRewardConfigMap(Map<String, RewardConfigDetail> rewardConfigMap) {
    //     this.rewardConfigMap = rewardConfigMap;
    // }
}
