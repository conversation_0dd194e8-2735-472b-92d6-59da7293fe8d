package com.quhong.mongo.data;

import com.quhong.mongo.dao.GiftRecommendDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Document(collection = GiftRecommendDao.TABLE_NAME)
public class GiftRecommendData {

    @Id
    private ObjectId _id;
    private ExtraConfig extra_config;
    private List<GiftInfo> gifts;

    public static class ExtraConfig{
        private Integer survey;
        private String surveyLinkEn;
        private String surveyLinkAr;
        private String surveyTextEn;
        private String surveyTextAr;

        public Integer getSurvey() {
            return survey;
        }

        public void setSurvey(Integer survey) {
            this.survey = survey;
        }

        public String getSurveyLinkEn() {
            return surveyLinkEn;
        }

        public void setSurveyLinkEn(String surveyLinkEn) {
            this.surveyLinkEn = surveyLinkEn;
        }

        public String getSurveyLinkAr() {
            return surveyLinkAr;
        }

        public void setSurveyLinkAr(String surveyLinkAr) {
            this.surveyLinkAr = surveyLinkAr;
        }

        public String getSurveyTextEn() {
            return surveyTextEn;
        }

        public void setSurveyTextEn(String surveyTextEn) {
            this.surveyTextEn = surveyTextEn;
        }

        public String getSurveyTextAr() {
            return surveyTextAr;
        }

        public void setSurveyTextAr(String surveyTextAr) {
            this.surveyTextAr = surveyTextAr;
        }
    }

    public static class GiftInfo{
        private String gname;
        private String gnamear;
        private String describe;
        private String describeAr;
        private Integer price;
        private String gicon;
        private String action_url;
        private String audio_url;

        public String getGname() {
            return gname;
        }

        public void setGname(String gname) {
            this.gname = gname;
        }

        public String getGnamear() {
            return gnamear;
        }

        public void setGnamear(String gnamear) {
            this.gnamear = gnamear;
        }

        public String getDescribe() {
            return describe;
        }

        public void setDescribe(String describe) {
            this.describe = describe;
        }

        public String getDescribeAr() {
            return describeAr;
        }

        public void setDescribeAr(String describeAr) {
            this.describeAr = describeAr;
        }

        public Integer getPrice() {
            return price;
        }

        public void setPrice(Integer price) {
            this.price = price;
        }

        public String getGicon() {
            return gicon;
        }

        public void setGicon(String gicon) {
            this.gicon = gicon;
        }

        public String getAction_url() {
            return action_url;
        }

        public void setAction_url(String action_url) {
            this.action_url = action_url;
        }

        public String getAudio_url() {
            return audio_url;
        }

        public void setAudio_url(String audio_url) {
            this.audio_url = audio_url;
        }
    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public ExtraConfig getExtra_config() {
        return extra_config;
    }

    public void setExtra_config(ExtraConfig extra_config) {
        this.extra_config = extra_config;
    }

    public List<GiftInfo> getGifts() {
        return gifts;
    }

    public void setGifts(List<GiftInfo> gifts) {
        this.gifts = gifts;
    }
}
