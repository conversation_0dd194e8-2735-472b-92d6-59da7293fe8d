package com.quhong.mongo.dao;

import com.quhong.cache.CacheMap;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.BadgeData;
import com.quhong.mongo.data.BadgeListData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 徽章
 */
@Component
public class BadgeDao {
    private static final Logger logger = LoggerFactory.getLogger(BadgeDao.class);

    public static final String TABLE_NAME = "badge";
    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    @Resource
    private BadgeListDao badgeListDao;

    private static final Comparator<BadgeData> COMPARATOR = Comparator.comparingInt(BadgeData::getStatus);
    private CacheMap<String, List<BadgeData>> cacheMap;

    public BadgeDao() {
        cacheMap = new CacheMap<>();
    }

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public List<String> getBadgeList(String aid) {
        List<BadgeData> wearBadgeList = findBadgeListByCache(aid);
        if (CollectionUtils.isEmpty(wearBadgeList)) {
            return Collections.emptyList();
        }
        return badgeListDao.getSmallIconsFromCache(wearBadgeList);
    }

    public List<BadgeData> findBadgeListByCache(String uid) {
        List<BadgeData> dataList = cacheMap.getData(uid);
        if (dataList != null) {
            return dataList;
        }
        dataList = findBadgeList(uid);
        if (dataList != null) {
            cacheMap.cacheData(uid, dataList);
        }
        return dataList;
    }

    public List<BadgeData> findBadgeList(String uid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("status").ne(0);
            Query query = new Query(criteria);
            List<BadgeData> list = mongoTemplate.find(query, BadgeData.class);
            list.sort(COMPARATOR);
            return list;
        } catch (Exception e) {
            logger.error("get badges error. uid={} {} ", uid, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public BadgeData getBadgeData(String uid, int badgeId) {
        try {
            Query query = new Query(Criteria.where("uid").is(uid).and("badge_id").is(badgeId));
            return mongoTemplate.findOne(query, BadgeData.class);
        } catch (Exception e) {
            logger.error("get badge error uid={} badgeId={} {}", uid, badgeId, e.getMessage());
            return null;
        }
    }

    public List<BadgeData> getWearBadgeList(String uid) {
        try {
            Query query = new Query(Criteria.where("uid").is(uid).and("status").ne(0));
            return mongoTemplate.find(query, BadgeData.class);
        } catch (Exception e) {
            logger.error("get wear badge error uid={} {}", uid, e.getMessage());
            return Collections.emptyList();
        }
    }

    public List<BadgeData> getOwnedBadgeList(String uid) {
        try {
            Query query = new Query(Criteria.where("uid").is(uid)).with(Sort.by(Sort.Order.desc("get_time")));
            return mongoTemplate.find(query, BadgeData.class);
        } catch (Exception e) {
            logger.error("get owned badge error uid={} {}", uid, e.getMessage());
            return null;
        }
    }

    public List<BadgeData> getOwnedBadgeListOrderStatus(String uid) {
        try {
            Query query = new Query(Criteria.where("uid").is(uid)).with(Sort.by(Sort.Order.desc("status")));
            return mongoTemplate.find(query, BadgeData.class);
        } catch (Exception e) {
            logger.error("get owned badge error uid={} {}", uid, e.getMessage());
            return Collections.emptyList();
        }
    }

    public void deleteBadge(BadgeData badgeData) {
        try {
            logger.info(" delete badge, uid={} badgeId={}", badgeData.getUid(), badgeData.getBadge_id());
            mongoTemplate.remove(badgeData);
        } catch (Exception e) {
            logger.error("delete badge error uid={} badgeId={} {}", badgeData.getUid(), badgeData.getBadge_id(), e.getMessage());
        }
    }

    /**
     * 批量删除勋章，用于等级勋章下发
     */
    public void removeBadges(String uid, Collection<Integer> badgeIdList) {
        try {
            if (CollectionUtils.isEmpty(badgeIdList)) {
                return;
            }
            logger.info("removeBadges uid={} badgeIdList={}", uid, badgeIdList);
            Query query = new Query(Criteria.where("uid").is(uid).and("badge_id").in(badgeIdList));
            mongoTemplate.remove(query, TABLE_NAME);
        } catch (Exception e) {
            logger.error("removeBadges error uid={} badgeIdList={} {}", uid, badgeIdList, e.getMessage());
        }
    }

    public void save(BadgeData badgeData) {
        try {
            mongoTemplate.save(badgeData);
        } catch (Exception e) {
            logger.error("save badge error uid={} badgeId={} {}", badgeData.getUid(), badgeData.getBadge_id(), e.getMessage());
        }
    }

    public void upsert(BadgeData badgeData) {
        try {
            Query query = new Query(Criteria.where("uid").is(badgeData.getUid()).and("badge_id").is(badgeData.getBadge_id()));
            Update update = new Update();
            update.set("status", badgeData.getStatus());
            update.set("get_time", badgeData.getGet_time());
            update.set("end_time", badgeData.getEnd_time());
            mongoTemplate.upsert(query, update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("save badge error uid={} badgeId={} {}", badgeData.getUid(), badgeData.getBadge_id(), e.getMessage());
        }
    }

    public void update(String uid, Integer order, Integer status) {
        try {
            // 更新mongodb数据
            Query query = new Query(Criteria.where("uid").is(uid).and("status").is(order));
            Update update = new Update();
            update.set("status", status);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update badge error uid={} order={} {}", uid, order, e.getMessage());
        }
    }

    public void updateByBid(String uid, Integer badgeId, Integer status) {
        try {
            // 更新mongodb数据
            Query query = new Query(Criteria.where("uid").is(uid).and("badge_id").is(badgeId));
            Update update = new Update();
            update.set("status", status);
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("update badge error uid={} badgeId={} status={} {}", uid, badgeId, status, e.getMessage());
        }
    }


    public void updateBadgeByTime(String uid, int badgeId, long endTime, BadgeData oldBadgeData) {
        try {
            logger.info("update badge. badgeId={} endTime={} uid={}", badgeId, endTime, uid);
            if (oldBadgeData == null) {
                BadgeData badgeData = new BadgeData();
                badgeData.setUid(uid);
                badgeData.setBadge_id(badgeId);
                badgeData.setGet_time(DateHelper.getNowSeconds());
                badgeData.setStatus(0);
                badgeData.setEnd_time(endTime);
                mongoTemplate.insert(badgeData);
            } else {
                Criteria criteria = Criteria.where("uid").is(uid).and("badge_id").is(badgeId);
                Update update = new Update();
                update.set("end_time", endTime);
                mongoTemplate.updateFirst(new Query(criteria), update, TABLE_NAME);
            }

        } catch (Exception e) {
            logger.error("updateBadgeByTime data error. badge_id={}  uid={} {}", badgeId, uid, e.getMessage(), e);
        }
    }

    public List<BadgeData> listByEndTime(int start, int end) {
        Criteria criteria = Criteria.where("end_time").exists(true).gt(start).lte(end);
        List<BadgeData> list = mongoTemplate.find(new Query(criteria), BadgeData.class);
        if (CollectionUtils.isEmpty(list)) {
            logger.info("listByEndTime list is null  end = {}", end);
            return new ArrayList<>();
        }
        return list;
    }

    public int getAvailableWearPos(String aid, List<BadgeData> wearBadgeList) {
        int pos = 0;
        if (wearBadgeList == null) {
            wearBadgeList = getWearBadgeList(aid);
        }
        List<Integer> badgeStatus = new ArrayList<>();
        for (BadgeData badgeData : wearBadgeList) {
            badgeStatus.add(badgeData.getStatus());
        }
        if (badgeStatus.size() <= 3) {
            for (int status = 1; status <= 3; status++) {
                if (!badgeStatus.contains(status)) {
                    pos = status;
                    break;
                }
            }
        }
        return pos;
    }


    /**
     * 获取用户拥有的勋章
     */
    public List<BadgeData> findBadgesByIds(String uid, List<Integer> badgeIdList) {
        try {
            if (CollectionUtils.isEmpty(badgeIdList)) {
                return Collections.emptyList();
            }
            Query query = new Query(Criteria.where("uid").is(uid).and("badge_id").in(badgeIdList));
            List<BadgeData> list = mongoTemplate.find(query, BadgeData.class);
            if (CollectionUtils.isEmpty(list)) {
                return Collections.emptyList();
            }
            return list;
        } catch (Exception e) {
            logger.error("findBadgesByIds error uid={} badgeIdList={} {}", uid, badgeIdList, e.getMessage());
        }
        return Collections.emptyList();
    }
}
