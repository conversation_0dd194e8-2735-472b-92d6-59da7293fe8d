package com.quhong.mongo.dao;

import com.quhong.cache.CacheMap;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.ResourceConfigData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/24
 */
@Component
public class ResourceConfigDao {

    private static final Logger logger = LoggerFactory.getLogger(ResourceConfigDao.class);

    public static final String TABLE_NAME = "resource_config";
    private static final long CACHE_TIME_MILLIS = 5 * 60 * 1000L;

    private final CacheMap<String, ResourceConfigData> cacheMap;
    private final CacheMap<String, List<ResourceConfigData>> listCacheMap;

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public ResourceConfigDao () {
        cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
        listCacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    public void save(ResourceConfigData data) {
        try {
            mongoTemplate.save(data);
        } catch (Exception e) {
            logger.error("save resource config data error. resourceId={} {}", data.getResourceId(), e.getMessage(), e);
        }
    }

    /**
     * 分页查询资源列表
     */
    public List<ResourceConfigData> selectResourcePage(int resType, Integer itemType, Integer status, String search, int start, int pageSize) {
        try {
            // 查询条件
            Criteria criteria = buildCriteria(resType, itemType, status, search);
            Sort sort;
            if(itemType != null && itemType == 5){
                sort = Sort.by(Sort.Order.asc("order"), Sort.Order.desc("_id"));
            }else {
                sort = Sort.by(Sort.Direction.DESC, "_id");
            }
            Query query = new Query(criteria);
            query.with(sort).skip(start).limit(pageSize);
            return mongoTemplate.find(query, ResourceConfigData.class);
        } catch (Exception e) {
            logger.error("select resource data page error. itemType={} status={} search={} start={} pageSize={} {}", itemType, status, search, start, pageSize, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 资源列表总数量
     */
    public long selectResourceCount(int resType, Integer itemType, Integer status, String search) {
        return mongoTemplate.count(new Query(buildCriteria(resType, itemType, status, search)), TABLE_NAME);
    }

    private Criteria buildCriteria(int resType, Integer itemType, Integer status, String search) {
        Criteria criteria = new Criteria();
        criteria.and("resourceType").is(resType);
        if(status != -1){
            criteria.and("status").is(status);
        }
        if (!StringUtils.isEmpty(search)){
            try {
                int resourceId = Integer.parseInt(search);
                criteria.and("resourceId").is(resourceId);
            } catch (NumberFormatException e) {
                criteria.and("name").regex(".*?" + search + ".*?");
            }
        }
        if(itemType != -1){
            criteria.and("itemType").is(itemType);
        }
        return criteria;
    }

    /**
     * 分页查询商店资源列表
     */
    public List<ResourceConfigData> getStoreResourcePage(int resourceType, int start, int pageSize) {
        try {
            Criteria criteria = Criteria.where("resourceType").is(resourceType).and("status").is(1).and("itemType").is(5);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.ASC, "order").and(Sort.by(Sort.Direction.DESC, "_id"));
            query.with(sort);
            query.skip(start).limit(pageSize);
            return mongoTemplate.find(query, ResourceConfigData.class);
        } catch (Exception e) {
            logger.error("get store resource page list error. resourceType={} start={} pageSize={} {}", resourceType, start, pageSize, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public List<ResourceConfigData> getResourceListFromCache(int resourceType, int itemType) {
        String listCacheKey = getListCacheKey(resourceType, itemType);
        if (listCacheMap.hasData(listCacheKey)) {
            return listCacheMap.getData(listCacheKey);
        }
        List<ResourceConfigData> resourceList = getResourceListFromDb(resourceType, itemType);
        listCacheMap.cacheData(listCacheKey, resourceList);
        return resourceList;
    }

    public List<ResourceConfigData> getResourceListFromDb(int resourceType, int itemType) {
        try {
            Criteria criteria = Criteria.where("resourceType").is(resourceType).and("itemType").is(itemType).and("status").is(1);
            return mongoTemplate.find(new Query(criteria).with(Sort.by(Sort.Direction.DESC, "ctime")), ResourceConfigData.class);
        } catch (Exception e) {
            logger.error("get resource list error. resourceType={} itemType={} {}", resourceType, itemType, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 从数据库中获取资源数据
     */
    public ResourceConfigData getResourceDataFromCache(int resourceId, int resourceType) {
        String cacheKey = getCacheKey(resourceId, resourceType);
        if (cacheMap.hasData(cacheKey)) {
            return cacheMap.getData(cacheKey);
        }
        ResourceConfigData resourceData = getResourceDataFromDb(resourceId, resourceType);
        cacheMap.cacheData(cacheKey, resourceData);
        return resourceData;
    }

    /**
     * 从数据库中获取资源数据
     */
    public ResourceConfigData getResourceDataFromDb(int resourceId, int resourceType) {
        try {
            Criteria criteria = Criteria.where("resourceId").is(resourceId).and("resourceType").is(resourceType);
            return mongoTemplate.findOne(new Query(criteria), ResourceConfigData.class);
        } catch (Exception e) {
            logger.error("get resource data error. resourceId={} resourceType={} {}", resourceId, resourceType, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从数据库中获取资源数据
     */
    public ResourceConfigData getResourceDataFromCache(int resourceId) {
        String cacheKey = getCacheKey(resourceId);
        if (cacheMap.hasData(cacheKey)) {
            return cacheMap.getData(cacheKey);
        }
        ResourceConfigData resourceData = getResourceDataFromDb(resourceId);
        cacheMap.cacheData(cacheKey, resourceData);
        return resourceData;
    }

    /**
     * 从数据库中获取资源数据
     */
    public ResourceConfigData getResourceDataFromDb(int resourceId) {
        try {
            Criteria criteria = Criteria.where("resourceId").is(resourceId);
            return mongoTemplate.findOne(new Query(criteria), ResourceConfigData.class);
        } catch (Exception e) {
            logger.error("get resource data error. resourceId={} {}", resourceId, e.getMessage(), e);
            return null;
        }
    }

    public ResourceConfigData getResourceDataById(String docId) {
        try {
            return mongoTemplate.findById(docId, ResourceConfigData.class);
        } catch (Exception e) {
            logger.error("get resource data error. docId={} {}", docId, e.getMessage(), e);
            return null;
        }
    }

    public ResourceConfigData getLastResourceData() {
        try {
            Query query = new Query();
            query.with(Sort.by(Sort.Order.desc("resourceId")));
            return mongoTemplate.findOne(query, ResourceConfigData.class);
        } catch (Exception e) {
            logger.error("getLastResourceData error. {}", e.getMessage(), e);
            return null;
        }
    }

    public void updateData(String docId, Update update) {
        try {
            mongoTemplate.updateFirst(new Query(Criteria.where("_id").is(docId)), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("updateResourceConfigData error.  id={} {}", docId, e.getMessage(), e);
        }
    }

    private String getCacheKey(int resourceId, int resourceType) {
        return "resourceData:" + resourceId + "_" + resourceType;
    }

    private String getCacheKey(int resourceId) {
        return "resourceData:" + resourceId;
    }

    private String getListCacheKey(int resourceId, int resourceType) {
        return "resourceList:" + resourceId + "_" + resourceType;
    }
}
