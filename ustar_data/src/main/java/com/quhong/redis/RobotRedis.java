package com.quhong.redis;

import com.quhong.cache.CacheMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@Component
@Lazy
public class RobotRedis {

    private static final Logger logger = LoggerFactory.getLogger(RobotRedis.class);
    private static final long CACHE_TIME_MILLIS = 60 * 60 * 1000L;
    private static final int ALL = 1;
    private final CacheMap<Integer, List<String>> cacheMap;

    public RobotRedis() {
        cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    /**
     * 朋友圈自动点赞、自动猜拳等机器人、房间活动自动订阅
     */
    public List<String> getBizRobotSet() {
        try {
            if (cacheMap.hasData(ALL)) {
                return cacheMap.getData(ALL);
            }
            Set<String> set = redisTemplate.opsForSet().members(getBizRobotKey());
            List<String> resultList = set == null ? Collections.emptyList() : new ArrayList<>(set);
            if (!CollectionUtils.isEmpty(resultList)) {
                logger.info("get biz robot size={}", resultList.size());
                cacheMap.cacheData(ALL, resultList);
            }
            return resultList;
        } catch (Exception e) {
            logger.error("get biz robot error.", e);
            return Collections.emptyList();
        }
    }

    /**
     * 游戏专用机器人
     */
    public List<String> getGameRobotList() {
        try {
            Set<String> set = redisTemplate.opsForSet().members(getGameRobotKey());
            if (ObjectUtils.isEmpty(set)) {
                return getBizRobotSet();
            }
            logger.info("get game robot. size={}", set.size());
            return new ArrayList<>(set);
        } catch (Exception e) {
            logger.error("get game robot error.", e);
            return Collections.emptyList();
        }
    }

    public List<String> setBizRobotSet(List<String> aidList) {
        try {
            int gameRobotCount = (int) (aidList.size() * 0.3);
            if (gameRobotCount > 0) {
                List<String> gameRobots = aidList.subList(0, gameRobotCount);
                String[] aids = new String[gameRobots.size()];
                aids = gameRobots.toArray(aids);
                redisTemplate.opsForSet().add(getGameRobotKey(), aids);
                gameRobots.clear();
            }
            String[] aids = new String[aidList.size()];
            aids = aidList.toArray(aids);
            redisTemplate.opsForSet().add(getBizRobotKey(), aids);
            return aidList;
        } catch (Exception e) {
            logger.error("set biz robot error.", e);
            return Collections.emptyList();
        }
    }

    public void deleteBizRobotSet() {
        try {
            redisTemplate.delete(getBizRobotKey());
            redisTemplate.delete(getGameRobotKey());
        } catch (Exception e) {
            logger.error("delete biz robot error.", e);
        }
    }

    private String getBizRobotKey() {
        return "set:bizRobot";
    }

    private String getGameRobotKey() {
        return "set:gameRobot";
    }
}
