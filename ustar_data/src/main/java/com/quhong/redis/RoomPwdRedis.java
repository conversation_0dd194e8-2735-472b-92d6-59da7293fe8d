package com.quhong.redis;

import com.quhong.cache.CacheMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Lazy
@Component
public class RoomPwdRedis {
    private static final Logger logger = LoggerFactory.getLogger(RoomPwdRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
    private final CacheMap<String, Boolean> pwdCacheMap;

    public RoomPwdRedis() {
        pwdCacheMap = new CacheMap<>(30 * 1000L);
    }

    @PostConstruct
    public void postInit() {
        pwdCacheMap.start();
    }

    public String getRoomPwd(String roomId) {
        try {
            return redisTemplate.opsForValue().get(getKey(roomId));
        } catch (Exception e) {
            logger.error("get room pwd error. roomId={} msg={}", roomId, e.getMessage());
        }
        return null;
    }

    public boolean hasPwdFromCache(String roomId) {
        if (pwdCacheMap.hasData(roomId)) {
            return pwdCacheMap.getData(roomId);
        }
        boolean hasPwd = hasPwd(roomId);
        pwdCacheMap.cacheData(roomId, hasPwd);
        return hasPwd;
    }

    public boolean hasPwd(String roomId) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(getKey(roomId)));
        } catch (Exception e) {
            logger.error("get room has pwd error. roomId={} msg={}", roomId, e.getMessage());
        }
        return false;
    }

    public void setRoomPwd(String roomId, String pwd) {
        try {
            redisTemplate.opsForValue().set(getKey(roomId), pwd);
        } catch (Exception e) {
            logger.error("set room pwd error. roomId={} msg={}", roomId, e.getMessage());
        }
    }

    public void remove(String roomId) {
        try {
            redisTemplate.delete(getKey(roomId));
        } catch (Exception e) {
            logger.error("delete room pwd redis error. roomId={} msg={}", roomId, e.getMessage(), e);
        }
    }

    private String getKey(String roomId) {
        return "room_pwd_" + roomId;
    }
}
