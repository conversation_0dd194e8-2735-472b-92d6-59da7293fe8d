package com.quhong.redis;

import com.alibaba.druid.util.StringUtils;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.utils.MatchUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class UserInterceptionRedis {

    private static final Logger logger = LoggerFactory.getLogger(UserInterceptionRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;


    private String getUserInterceptionKey(String uid, String aid) {
        return String.format("str:UserInterception:%s:%s", uid, aid);
    }

    public void setUserInterception(String uid, String aid, int timeout) {
        try {
            String key = getUserInterceptionKey(uid, aid);
            clusterRedis.opsForValue().set(key, String.valueOf(timeout));
            clusterRedis.expire(key, timeout, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setUserInterception error: {}", e.getMessage(), e);
        }
    }


    public int getUserInterceptionStatus(String uid, String aid) {
        try {
            String timeout = clusterRedis.opsForValue().get(getUserInterceptionKey(uid, aid));
            return timeout == null ? 0 : Integer.parseInt(timeout);
        } catch (Exception e) {
            logger.error("getUserInterceptionStatus error: {}", e.getMessage(), e);
        }
        return 0;
    }

    public void removeUserInterception(String uid, String aid) {
        try {
            clusterRedis.delete(getUserInterceptionKey(uid, aid));
        } catch (Exception e) {
            logger.error("removeUserInterception error:{}", e.getMessage(), e);
        }
    }


    private String getDayReplayKey(String day) {
        return String.format("set:privateMsgDayReplay:%s", day);
    }

    private String getDayReplayValue(String uid, String aid) {
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(aid)) {
            return null;
        }
        return MatchUtils.generateFriendIndex(uid, aid);
    }

    public void addDayReplaySetData(String day, String uid, String aid) {
        String data = getDayReplayValue(uid, aid);
        if (data == null) {
            return;
        }
        try {
            String key = getDayReplayKey(day);
            clusterRedis.opsForSet().add(key, data);
            clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addCommonSetData error day={} data={} e={}", day, data, e);
        }
    }


    public boolean isDayReplaySetData(String day, String uid, String aid) {
        String data = getDayReplayValue(uid, aid);
        if (data == null) {
            return false;
        }
        try {
            return Boolean.TRUE.equals(clusterRedis.opsForSet().isMember(getDayReplayKey(day), data));
        } catch (Exception e) {
            logger.error("isCommonSetData error day={} data={}, e={}", day, data, e);
            return false;
        }
    }

}
