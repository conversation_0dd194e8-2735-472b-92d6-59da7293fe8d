package com.quhong.data.dto;

public class BadgeListDTO {

    /**
     * 徽章id
     */
    private int bid;
    /**
     * 徽章名字
     */
    private String name;
    /**
     * 图标
     */
    private String icon;
    /**
     * 徽章佩戴状态，0为未佩戴，1-3为佩戴序号
     */
    private int status;
    /**
     * 勋章类型
     */
    private int badgeType;
    /**
     * 徽章描述
     */
    private String desc;
    /**
     * svga图标url
     */
    private String svgaIcon;
    /**
     * 年份
     */
    private Integer year;
    /**
     * 获取时间
     */
    private int gainTime;
    private String getTime;
    /**
     * 发布时间
     */
    private int releaseTime;

    /**
     * 荣誉等级
     */
    private int honorLevel;

    /**
     * 父勋章id
     */
    private int pBadgeId;
    /**
     * 子勋章顺序
     */
    private int subBadgeOrder;
    /**
     * 个人成就勋章子分类(大于0展示进度条) 1: 签到、 2: 爆火箭 3: 猜拳圣手 4: 发礼物 5:交友 6: Ludo 7: UMO 8: 动态之星  9: 接收礼物
     */
    private int achieveType;
    /**
     * 当前数量
     */
    private long achieveNumber;
    /**
     * 所需达成数量
     */
    private int condition;

    public int getBid() {
        return bid;
    }

    public void setBid(int bid) {
        this.bid = bid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getBadgeType() {
        return badgeType;
    }

    public void setBadgeType(int badgeType) {
        this.badgeType = badgeType;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getSvgaIcon() {
        return svgaIcon;
    }

    public void setSvgaIcon(String svgaIcon) {
        this.svgaIcon = svgaIcon;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public int getGainTime() {
        return gainTime;
    }

    public void setGainTime(int gainTime) {
        this.gainTime = gainTime;
    }

    public String getGetTime() {
        return getTime;
    }

    public void setGetTime(String getTime) {
        this.getTime = getTime;
    }

    public int getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(int releaseTime) {
        this.releaseTime = releaseTime;
    }

    public int getHonorLevel() {
        return honorLevel;
    }

    public void setHonorLevel(int honorLevel) {
        this.honorLevel = honorLevel;
    }

    public int getSubBadgeOrder() {
        return subBadgeOrder;
    }

    public void setSubBadgeOrder(int subBadgeOrder) {
        this.subBadgeOrder = subBadgeOrder;
    }

    public int getpBadgeId() {
        return pBadgeId;
    }

    public void setpBadgeId(int pBadgeId) {
        this.pBadgeId = pBadgeId;
    }

    public int getAchieveType() {
        return achieveType;
    }

    public void setAchieveType(int achieveType) {
        this.achieveType = achieveType;
    }

    public long getAchieveNumber() {
        return achieveNumber;
    }

    public void setAchieveNumber(long achieveNumber) {
        this.achieveNumber = achieveNumber;
    }

    public int getCondition() {
        return condition;
    }

    public void setCondition(int condition) {
        this.condition = condition;
    }
}
