package com.quhong.data.dto;

import com.alibaba.fastjson.JSON;
import com.quhong.handler.HttpEnvData;

import java.util.Map;
import java.util.Set;

public class SendFcmDTO extends HttpEnvData {
    private String toUid;
    private Set<String> toUidSet; // 不为空时使用批量发送
    private Set<String> toTokenSet;    // 使用fcmToken直接推送, 不在查Actor表
    private Map<String, String> paramMap;
    // 所有被推送用户共用信息
    private String title;
    private String titleAr;
    private String body;
    private String bodyAr;
    private String img;

    // 每个被推送用户不共用信息-以uid为key
    private Map<String, FcmUserInfo> userInfoMap;

    public SendFcmDTO() {
    }


    public static class FcmUserInfo{
        private String fcmToken;
        private String title;
        private String titleAr;
        private String body;
        private String bodyAr;
        private String img;
        private Integer slang;

        public String getFcmToken() {
            return fcmToken;
        }

        public void setFcmToken(String fcmToken) {
            this.fcmToken = fcmToken;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getTitleAr() {
            return titleAr;
        }

        public void setTitleAr(String titleAr) {
            this.titleAr = titleAr;
        }

        public String getBody() {
            return body;
        }

        public void setBody(String body) {
            this.body = body;
        }

        public String getBodyAr() {
            return bodyAr;
        }

        public void setBodyAr(String bodyAr) {
            this.bodyAr = bodyAr;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public Integer getSlang() {
            return slang;
        }

        public void setSlang(Integer slang) {
            this.slang = slang;
        }
    }

    public SendFcmDTO(String toUid, Map<String, String> paramMap, String title, String titleAr, String body, String bodyAr, String img) {
        this.toUid = toUid;
        this.paramMap = paramMap;
        this.title = title;
        this.titleAr = titleAr;
        this.body = body;
        this.bodyAr = bodyAr;
        this.img = img;
    }

    public SendFcmDTO(Set<String> toUidSet, Map<String, String> paramMap, String title, String titleAr, String body, String bodyAr, String img) {
        this.toUidSet = toUidSet;
        this.paramMap = paramMap;
        this.title = title;
        this.titleAr = titleAr;
        this.body = body;
        this.bodyAr = bodyAr;
        this.img = img;
    }

    public String getToUid() {
        return toUid;
    }

    public void setToUid(String toUid) {
        this.toUid = toUid;
    }

    public Set<String> getToUidSet() {
        return toUidSet;
    }

    public void setToUidSet(Set<String> toUidSet) {
        this.toUidSet = toUidSet;
    }

    public Set<String> getToTokenSet() {
        return toTokenSet;
    }

    public void setToTokenSet(Set<String> toTokenSet) {
        this.toTokenSet = toTokenSet;
    }

    public Map<String, String> getParamMap() {
        return paramMap;
    }

    public void setParamMap(Map<String, String> paramMap) {
        this.paramMap = paramMap;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleAr() {
        return titleAr;
    }

    public void setTitleAr(String titleAr) {
        this.titleAr = titleAr;
    }

    public String getBodyAr() {
        return bodyAr;
    }

    public void setBodyAr(String bodyAr) {
        this.bodyAr = bodyAr;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public Map<String, FcmUserInfo> getUserInfoMap() {
        return userInfoMap;
    }

    public void setUserInfoMap(Map<String, FcmUserInfo> userInfoMap) {
        this.userInfoMap = userInfoMap;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
