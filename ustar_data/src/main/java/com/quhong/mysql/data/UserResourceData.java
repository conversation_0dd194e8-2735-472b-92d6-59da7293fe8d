package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @date 2023/4/23
 */
@TableName("t_user_resource")
public class UserResourceData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private int id;

    /**
     * 用户uid
     */
    private String uid;

    /**
     * 资源id
     */
    private int resourceId;

    /**
     * 资源类型
     */
    private int resourceType;

    /**
     * 使用状态 0未使用 1正在使用
     */
    private int status;

    /**
     * 资源数量, 某些资源需要增加数量 如: 抽奖券
     */
    private int resourceNumber;

    /**
     * 有效期结束时间
     */
    private long endTime;

    /**
     * 创建时间
     */
    private int ctime;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getResourceId() {
        return resourceId;
    }

    public void setResourceId(int resourceId) {
        this.resourceId = resourceId;
    }

    public int getResourceType() {
        return resourceType;
    }

    public void setResourceType(int resourceType) {
        this.resourceType = resourceType;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getResourceNumber() {
        return resourceNumber;
    }

    public void setResourceNumber(int resourceNumber) {
        this.resourceNumber = resourceNumber;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
