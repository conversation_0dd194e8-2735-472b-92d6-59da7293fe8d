package com.quhong.mysql.mapper.ustar;

import com.quhong.mysql.data.VipInfoData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface VipInfoMapper {

    @Select("<script>" +
            "select rid,uid,vip_end_time,vip_level from t_vip_info where uid in " +
            "<foreach collection = 'uidList' open = '(' item = 'item' separator = ',' close = ')'>" +
            "#{item}" +
            "</foreach>" +
            "and vip_end_time >= #{time}" +
            "</script>")
    List<VipInfoData> queryVipDataList(@Param("uidList") Collection<String> uidList, @Param("time") String time);

    @Select("select vip_gift_remain from t_vip_info where uid=#{uid} limit 1")
    Integer queryGiftRemain(@Param("uid") String uid);

    @Update("update t_vip_info set vip_gift_remain=vip_gift_remain-#{cost}, vip_gift_used=vip_gift_used+#{cost} where uid=#{uid} and vip_gift_remain=#{curRemain}")
    int updateGiftRemain(@Param("uid") String uid, @Param("curRemain") int curRemain, @Param("cost") int cost);


    @Select("select rid,uid,vip_buy_time,vip_end_time,vip_level, vip_time_total, vip_gift_remain from t_vip_info where uid=#{uid} limit 1")
    VipInfoData queryVipInfo(@Param("uid") String uid);


    @Update("update t_vip_info set vip_gift_remain=#{curRemain}, vip_time_total=#{vipTotal}, vip_buy_time=#{buyTime}, vip_end_time=#{endTime}, vip_level=#{vipLevel}, vip_expire_status=0 where uid=#{uid}")
    int updateVipInfo(@Param("uid") String uid, @Param("curRemain") int curRemain, @Param("buyTime") Date buyTime, @Param("endTime") Date endTime, @Param("vipTotal") int vipTotal, @Param("vipLevel") int vipLevel);

    @Insert("insert into t_vip_info (uid, vip_level, vip_time_total, vip_buy_time, vip_end_time, vip_gift_remain, vip_gold_total, ctime, mtime, vip_expire_status) " +
            "values (#{uid}, #{vipLevel}, #{vipTotal}, #{buyTime}, #{endTime}, #{curRemain}, #{goldTotal}, #{currentDate}, #{currentDate}, 0)")
    int insertVipInfo(@Param("uid") String uid, @Param("vipLevel") int vipLevel, @Param("vipTotal") int vipTotal, @Param("buyTime") Date buyTime, @Param("endTime") Date endTime,
                      @Param("curRemain") int curRemain, @Param("goldTotal") int goldTotal, @Param("currentDate") Date currentDate);


    @Select("<script>" +
            "select rid,uid,vip_end_time,vip_level from t_vip_info where " +
            "vip_end_time <![CDATA[<=]]> #{time} and vip_expire_status = 0" +
            "</script>")
    List<VipInfoData> selectExpireVipDataList(@Param("time") String time);

    @Update("update t_vip_info set vip_expire_status = 1 where uid=#{uid}")
    int updateExpireVip(@Param("uid") String uid);


    @Select("<script>" +
            "select rid,uid,vip_end_time,vip_level from t_vip_info where " +
            "<![CDATA[vip_end_time > #{startTime} and vip_end_time < #{endTime}]]>" +
            "</script>")
    List<VipInfoData> selectAwareExpireVipList(@Param("startTime") String startTime, @Param("endTime") String endTime);

}
