package com.quhong.mysql.dao;

import com.quhong.cache.CacheMap;
import com.quhong.core.date.DayTimeSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.CommonConfig;
import com.quhong.mysql.data.VipInfoData;
import com.quhong.mysql.mapper.ustar.VipInfoMapper;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
@Lazy
public class VipInfoDao {
    private static final Logger logger = LoggerFactory.getLogger(VipInfoDao.class);
    private static final long CACHE_TIME_MILLIS = 2 * 60 * 1000L;
    public static final int MIN_VIP_URL_LEVEL = 4;

    @Resource
    private VipInfoMapper vipInfoMapper;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainTemplate;
    @Resource(name = DataRedisBean.VIP)
    private StringRedisTemplate redisTemplate;
    private final CacheMap<String, Integer> vipCacheMap;
    private final CacheMap<String, VipInfoData> vipSortCacheMap;
    @Resource
    private CommonConfig commonConfig;

    public VipInfoDao() {
        vipCacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
        vipSortCacheMap = new CacheMap<>(30 * 1000L);
    }

    @PostConstruct
    public void postInit() {
        vipCacheMap.start();
        vipSortCacheMap.start();
    }

    public int getIntVipLevelFromCache(String uid) {
        if (vipCacheMap.hasData(uid)) {
            return vipCacheMap.getData(uid);
        }
        int vipLevel = getIntVipLevel(uid);
        vipCacheMap.cacheData(uid, vipLevel);
        return vipLevel;
    }

    public int getIntVipLevel(String uid) {
        Integer level = getVipLevelFromRedis(uid);
        return level == null ? 0 : level;
    }

    /**
     * 查询vip_gift_remain
     */
    public int getVipGiftRemain(String uid) {
        try {
            Double remain = mainTemplate.opsForZSet().score(getVipGiftRemainKey(), uid);
            if (null == remain) {
                return 0;
            }
            return remain.intValue();
        } catch (Exception e) {
            logger.error("get vip gift remain from redis error uid={} {}", uid, e.getMessage());
            return getVipGiftRemainFromDB(uid);
        }
    }

    public void updateRedisVipGiftRemain(String uid, int remain) {
        try {
            if (remain > 0) {
                mainTemplate.opsForZSet().add(getVipGiftRemainKey(), uid, remain);
            } else {
                mainTemplate.opsForZSet().remove(getVipGiftRemainKey(), uid);
            }
        } catch (Exception e) {
            logger.error("update redis vip gift remain error uid={} remain={} {}", uid, remain, e.getMessage());
        }
    }

    public void incRedisVipGiftRemain(String uid, int num) {
        try {
            mainTemplate.opsForZSet().incrementScore(getVipGiftRemainKey(), uid, num);
        } catch (Exception e) {
            logger.error("incRedisVipGiftRemain error uid={} num={} {}", uid, num, e.getMessage());
        }
    }

    private String getVipGiftRemainKey() {
        return "vip:zkey:vip_gift_remain";
    }

    public int getVipGiftRemainFromDB(String uid) {
        try {
            Integer remain = vipInfoMapper.queryGiftRemain(uid);
            if (remain != null) {
                return remain;
            }
            return 0;
        } catch (Exception e) {
            logger.error("get vip gift remain from db error uid={} {}", uid, e.getMessage());
            return 0;
        }
    }

    /**
     * 更新vip礼物剩余数量
     *
     * @param curRemain 未修改前的数量
     * @param cost      花费数量
     */
    public int updateVipGiftRemain(String uid, int curRemain, int cost) {
        updateRedisVipGiftRemain(uid, curRemain - cost);
        try {
            return vipInfoMapper.updateGiftRemain(uid, curRemain, cost);
        } catch (Exception e) {
            logger.error("update vip gift remain error uid={} curRemain={} {}", uid, curRemain, e.getMessage());
            return 0;
        }
    }

    public Integer getVipLevel(String uid) {
        return getVipLevelFromRedis(uid);
    }

    private Integer getVipLevelFromRedis(String uid) {
        try {
            String levelStr = (String) redisTemplate.opsForHash().get(uid, "vip_level");
            if (StringUtils.isEmpty(levelStr)) {
                return null;
            }
            return Integer.parseInt(levelStr);
        } catch (Exception e) {
            logger.error("get vip level from redis error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public Integer setVipLevelRedis(String uid, int vipLevel, int endTime) {
        try {
            redisTemplate.opsForHash().put(uid, "vip_level", String.valueOf(vipLevel));
            int expireTime = endTime - DateHelper.getNowSeconds();
            redisTemplate.expire(uid, expireTime, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.error("get vip level from redis error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public void clearVipLevelFromRedis(String uid) {
        try {
            String levelStr = (String) redisTemplate.opsForHash().get(uid, "vip_level");
            if (StringUtils.isEmpty(levelStr)) {
                return;
            }
            redisTemplate.opsForHash().put(uid, "vip_level", "0");
        } catch (Exception e) {
            logger.error("clear vip level from redis error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    /**
     * 后去vip集合，会使用缓存
     *
     * @param uidList
     * @return
     */
    public Map<String, Integer> getVipMap(Collection<String> uidList) {
        Map<String, Integer> retMap = new HashMap<>();
        Collection<String> remainList = new ArrayList<>();
        for (String uid : uidList) {
            if (vipSortCacheMap.hasData(uid)) {
                VipInfoData vipInfoData = vipSortCacheMap.getData(uid);
                Integer vipLevel = vipInfoData != null ? vipInfoData.getVipLevel() : 0;
                retMap.put(uid, vipLevel);
            } else {
                remainList.add(uid);
            }
        }
        if (!remainList.isEmpty()) {
            List<VipInfoData> vipInfoDataList = findList(remainList);
            Map<String, VipInfoData> vipInfoMap = new HashMap<>();
            vipInfoDataList.forEach(data -> vipInfoMap.put(data.getUid(), data));
            for (String uid : remainList) {
                VipInfoData vipInfoData = vipInfoMap.get(uid);
                Integer vipLevel = vipInfoData != null ? vipInfoData.getVipLevel() : 0;
                vipSortCacheMap.cacheData(uid, vipInfoData);
                retMap.put(uid, vipLevel);
            }
        }
        return retMap;
    }

    /**
     * 获取有效的vip列表
     *
     * @param uidList
     * @return
     */
    public List<VipInfoData> findList(Collection<String> uidList) {
        try {
            // ！！！py服务器用的是印度时区
            return vipInfoMapper.queryVipDataList(uidList, DayTimeSupport.format(LocalDateTime.now().plusMinutes(5 * 60 + 30)));
        } catch (Exception e) {
            logger.error("find vip data error. uidList={} {}", uidList, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取vip redis过期时间
     */
    public long getVipRdsExpire(String uid) {
        try {
            Long expire = redisTemplate.getExpire(uid, TimeUnit.SECONDS);
            if (null == expire) {
                return 0;
            }
            return expire;
        } catch (Exception e) {
            logger.error("getVipRdsEndTime error.  uid={} {}", uid, e.getMessage(), e);
        }
        return 0;
    }

    /**
     * 查询vip信息
     */
    public VipInfoData findVipInfo(String uid) {
        try {
            return vipInfoMapper.queryVipInfo(uid);
        } catch (Exception e) {
            logger.error("findVipInfo error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 查询已过期但未标记过期vip用户
     */
    public List<VipInfoData> selectExpireVipInfo() {
        try {
            return vipInfoMapper.selectExpireVipDataList(DayTimeSupport.format(LocalDateTime.now().plusMinutes(5 * 60 + 30)));
        } catch (Exception e) {
            logger.error("selectExpireVipInfo error. {}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    /**
     * 查询即将过期vip用户
     */
    public List<VipInfoData> selectAwareExpireVip(String startTime, String endTime) {
        try {
            return vipInfoMapper.selectAwareExpireVipList(startTime, endTime);
        } catch (Exception e) {
            logger.error("selectAwareExpireVip error. {}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    /**
     * 更新vip过期状态
     */
    public void updateExpireVip(String uid) {
        try {
            vipInfoMapper.updateExpireVip(uid);
            clearVipLevelFromRedis(uid);
        } catch (Exception e) {
            logger.error("updateExpireVip error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    /**
     * 更新vip信息
     */
    public int updateVipInfo(String uid, int curRemain, Date buyTime, Date endTime, int vipTotal, int vipLevel) {
        try {
            return vipInfoMapper.updateVipInfo(uid, curRemain, buyTime, endTime, vipTotal, vipLevel);
        } catch (Exception e) {
            logger.error("updateVipInfo error. uid={} {}", uid, e.getMessage(), e);
        }
        return 0;
    }

    /**
     * 新增vip信息
     */
    public int insertVipInfo(String uid, int vipLevel, int vipTotal, Date buyTime, Date endTime, int curRemain, int goldTotal, Date currentDate) {
        try {
            return vipInfoMapper.insertVipInfo(uid, vipLevel, vipTotal, buyTime, endTime, curRemain, goldTotal, currentDate);
        } catch (Exception e) {
            logger.error("insertVipInfo error. uid={} {}", uid, e.getMessage(), e);
        }
        return 0;
    }

    public String generateVipUrl(String uid, String url, int mode) {
        int vipLevel = getIntVipLevelFromCache(uid);
        return generateVipUrlByLevel(vipLevel, url, mode);
    }

    public String generateVipUrlByLevel(int vipLevel, String url, int mode) {
        int vipOnlineSwitch = commonConfig.getSwitchConfigValue(CommonConfig.VIP_ONLINE_SWITCH_KEY, 0);
        boolean isGif = (vipOnlineSwitch==1 && vipLevel >= MIN_VIP_URL_LEVEL);
        return ImageUrlGenerator.generateUrlByMode(url, isGif, mode);
    }
}
