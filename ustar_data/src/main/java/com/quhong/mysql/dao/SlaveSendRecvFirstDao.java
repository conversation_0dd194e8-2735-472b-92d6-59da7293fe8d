package com.quhong.mysql.dao;

import com.quhong.data.SendRecvFirstJoinData;
import com.quhong.mysql.data.SendRecvFirstData;
import com.quhong.mysql.slave_mapper.ustar_log.SlaveSendRecvFirstMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/19
 */
@Lazy
@Component
public class SlaveSendRecvFirstDao {

    private final static Logger logger = LoggerFactory.getLogger(SlaveSendRecvFirstDao.class);

    public static final String TABLE_PRE = "t_gift_recv_send_first";

    @Resource
    private SlaveSendRecvFirstMapper slaveSendRecvFirstMapper;

    public void insert(SendRecvFirstData data) {
        slaveSendRecvFirstMapper.insert(getTableName(data.getReceiveUid()), data);
    }

    public int incGiftNum(SendRecvFirstData data) {
        return slaveSendRecvFirstMapper.incGiftNum(getTableName(data.getReceiveUid()), data);
    }

    public List<SendRecvFirstJoinData> getFirstJoinData(String receiveUid) {
        List<SendRecvFirstJoinData> list = slaveSendRecvFirstMapper.getFirstJoinData(getTableName(receiveUid), receiveUid);
        if (list == null) {
            return Collections.emptyList();
        }
        return list;
    }

    public List<SendRecvFirstData> getGidNumList(String sendUid, String receiveUid) {
        List<SendRecvFirstData> list = slaveSendRecvFirstMapper.getGidNumList(getTableName(receiveUid), receiveUid, sendUid);
        if (list == null) {
            return Collections.emptyList();
        }
        return list;
    }

    public List<SendRecvFirstData> selectBySendUid(String tableName, String sendUid, List<String> uidList) {
        return slaveSendRecvFirstMapper.selectBySendUid(tableName, sendUid, uidList);
    }

    public void batchInsert(String tableName, List<SendRecvFirstData> insertList) {
        slaveSendRecvFirstMapper.batchInsert(tableName, insertList);
    }

    public void batchIncGiftNum(String tableName, List<SendRecvFirstData> updateList) {
        slaveSendRecvFirstMapper.batchIncGiftNum(tableName, updateList);
    }

    /**
     * 获取分表名
     */
    private String getTableName(String uid) {
        return TABLE_PRE + "_" + uid.substring(uid.length() - 1);
    }
}
