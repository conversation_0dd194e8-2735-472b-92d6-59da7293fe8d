package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.cache.CacheMap;
import com.quhong.mysql.data.HuaWeiGoodsData;
import com.quhong.mysql.mapper.ustar.HuaWeiGoodsMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Lazy
public class HuaWeiGoodsDao {

    private static final Logger logger = LoggerFactory.getLogger(HuaWeiGoodsDao.class);
    private static final long CACHE_TIME_MILLIS = 3 * 60 * 1000L;
    private final CacheMap<String, List<HuaWeiGoodsData>> cacheMap;

    @Resource
    private HuaWeiGoodsMapper huaWeiGoodsMapper;

    public HuaWeiGoodsDao() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    private String getConfigKey() {
        return "huawei_config_key";
    }


    public List<HuaWeiGoodsData> getHuaWeiGoods() {
        List<HuaWeiGoodsData> huaWeiGoods = cacheMap.getData(getConfigKey());
        if (huaWeiGoods != null) {
            return huaWeiGoods;
        }

        QueryWrapper<HuaWeiGoodsData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.orderByAsc("diamonds");

        huaWeiGoods = huaWeiGoodsMapper.selectList(queryWrapper);
        if (huaWeiGoods != null) {
            cacheMap.cacheData(getConfigKey(), huaWeiGoods);
        }
        return huaWeiGoods;
    }


}
