package com.quhong.room.redis;

import com.quhong.cache.CacheMap;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.mongo.dao.MicFrameSourceDao;
import com.quhong.mongo.data.MicFrameSourceData;
import com.quhong.redis.DataRedisBean;
import com.quhong.service.WearResourcesService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 麦位redis
 */
@Component
public class MicFrameRedis {
    private static final Logger logger = LoggerFactory.getLogger(MicFrameRedis.class);
    public static final String ALL = "ALL";

    //    @Resource(name = DataRedisBean.MIC_SOURCE)
//    private StringRedisTemplate redisTemplate;
    //    @Resource(name = DataRedisBean.ROOM_TALK)
//    private StringRedisTemplate forbidRedisTemplate;
//    @Resource(name = DataRedisBean.MIC_FRAME_EXPIRE)
//    private StringRedisTemplate expireRedisTemplate;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainCluster;
    @Resource
    private WearResourcesService wearResourcesService;
    @Resource
    private MicFrameSourceDao micFrameSourceDao;

    private CacheMap<String, Map<Integer, String>> micSourceMap;


    public MicFrameRedis() {
        micSourceMap = new CacheMap<>(5 * 60 * 1000L);
    }

    /**
     * 获取麦位
     *
     * @param uid
     * @return
     */
    public Integer getMicFrame(String uid) {
        try {
//            String micFrameStr = (String) redisTemplate.opsForHash().get(getRedisKey(), uid);
//            if (StringUtils.isEmpty(micFrameStr)) {
//                return null;
//            }
//            return Integer.parseInt(micFrameStr);

            int wearId = wearResourcesService.getWearResId(uid, BaseDataResourcesConstant.TYPE_MIC);
            if (wearId == BaseDataResourcesConstant.COMMON_DEFAULT_ID) {
                return null;
            }
            return wearId;

        } catch (Exception e) {
            logger.info("get mic frame error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public void addMicFrame(String uid, int micFrameId) {
        try {
            wearResourcesService.setWearResIdById(uid, BaseDataResourcesConstant.TYPE_MIC, micFrameId);
//            redisTemplate.opsForHash().put(getRedisKey(), uid, micFrameId + "");
        } catch (Exception e) {
            logger.info("add mic frame error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public void removeMicFrame(String uid, Integer micFrameId) {
        try {
            Integer curMicFrame = getMicFrame(uid);
            if (micFrameId == null || micFrameId.equals(curMicFrame)) {
                wearResourcesService.deleteWearResIdByType(uid, BaseDataResourcesConstant.TYPE_MIC);
//                redisTemplate.opsForHash().delete(getRedisKey(), uid);
            }
        } catch (Exception e) {
            logger.info("remove mic frame error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    @Deprecated
    public String getMicSourceFromUid(String uid) {
        Integer micId = getMicFrame(uid);
        if (micId == null || micId == 0) {
            return "";
        }
        return getMicSource(micId);
    }

    private String getRedisKey() {
        return "user_take_mic";
    }

    public String getMicSourceFromCache(String uid) {
        try {
            Integer micId = getMicFrame(uid);
            if (micId == null || micId == 0) {
                return "";
            }
//            Map<Integer, String> map = getAllMicSourceFromCache();
//            String micSrc = map.get(micId);
//            logger.info("getMicSourceFromCache uid:{} map.size:{} micId:{} micSrc:{}", uid, map.size(), micId, micSrc);
            return getAllMicSourceFromCache().get(micId);
        } catch (Exception e) {
            logger.error("get mic source from cache error. uid={} {}", uid, e.getMessage(), e);
            return "";
        }
    }

    public Map<Integer, String> getAllMicSourceFromCache() {
        if (micSourceMap.hasData(ALL)) {
            return micSourceMap.getData(ALL);
        }
        return getAllMicSource();
    }

//    public synchronized Map<Integer, String> getAllMicSource() {
//        try {
//            if (micSourceMap.hasData(ALL)) {
//                return micSourceMap.getData(ALL);
//            }
//            Map<Integer, String> resultMap = new HashMap<>();
//            Map<Object, Object> map = redisTemplate.opsForHash().entries(getMicSourceKey());
//            for (Map.Entry<Object, Object> entry : map.entrySet()) {
//                try {
//                    resultMap.put(Integer.parseInt(String.valueOf(entry.getKey())), String.valueOf(entry.getValue()));
//                } catch (Exception e) {
//                    logger.error("parse mic source error. key={} value={}", entry.getKey(), entry.getValue(), e);
//                }
//            }
//            if (!resultMap.isEmpty()) {
//                micSourceMap.cacheData(ALL, resultMap);
//            }
//            return resultMap;
//        } catch (Exception e) {
//            logger.error("get all mic source error. {}", e.getMessage(), e);
//            return Collections.emptyMap();
//        }
//    }

    public synchronized Map<Integer, String> getAllMicSource() {
        try {
            if (micSourceMap.hasData(ALL)) {
                return micSourceMap.getData(ALL);
            }
            Map<Integer, String> resultMap = new HashMap<>();
            List<MicFrameSourceData> allList = micFrameSourceDao.findDataByAll();
            for (MicFrameSourceData item : allList) {
                try {
//                    logger.info("getAllMicSource add mic_id:{} mic_source:{}", item.getMic_id(), item.getMic_source());
                    resultMap.put(item.getMic_id(), item.getMic_source());
                } catch (Exception e) {
                    logger.error("parse mic source error. key={} value={}", item.getMic_id(), item.getMic_source(), e);
                }
            }
            if (!resultMap.isEmpty()) {
                micSourceMap.cacheData(ALL, resultMap);
            }
            return resultMap;

        } catch (Exception e) {
            logger.error("get all mic source error. {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }


    public String getMicSource(int micId) {
//        String key = getMicSourceKey();
        try {
            MicFrameSourceData data = micFrameSourceDao.findData(micId);
            return data != null ? data.getMic_source() : "";
        } catch (Exception e) {
            logger.error("get mic source error. micId={} {}", micId, e.getMessage(), e);
        }
        return "";
    }

    public void setMicSource(int micId, String micSource) {
//        String key = getMicSourceKey();
//        try {
//            redisTemplate.opsForHash().put(key, micId + "", micSource);
//        } catch (Exception e) {
//            logger.error("set mic source error. micId={} {}", micId, e.getMessage(), e);
//        }
    }

    private String getMicSourceKey() {
        return "mic_source";
    }

    public Integer getForbidTime(String roomId, String uid) {
        String key = getNewForbiddenKey(roomId, uid);
        try {
            String value = mainCluster.opsForValue().get(key);
            if (StringUtils.isEmpty(value)) {
                return 0;
            }
            int time = Integer.parseInt(value);
            if (time < DateHelper.getNowSeconds()) {
                mainCluster.delete(key);
                return 0;
            } else {
                return time;
            }
        } catch (Exception e) {
            logger.error("get mic forbid error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
        return 0;
    }

    public void setForbidTime(String roomId, String uid) {
        String key = getNewForbiddenKey(roomId, uid);
        try {
            mainCluster.opsForValue().set(key, String.valueOf(DateHelper.getNowSeconds() + 60 * 60), ExpireTimeConstant.EXPIRE_HOURS_ONE, TimeUnit.HOURS);
        } catch (Exception e) {
            logger.error("set mic forbid error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    public void removeForbidTime(String roomId, String uid) {
        String key = getNewForbiddenKey(roomId, uid);
        try {
            mainCluster.delete(key);
        } catch (Exception e) {
            logger.error("remove mic forbid error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    public void addMaleMicUser(String roomId, String uid) {
        String key = getMaleMicUser();
        try {
            mainCluster.opsForSet().add(key, getRoomIdUidComp(roomId, uid));
            mainCluster.expire(key, 30, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.error("addMaleMicUser error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    public void addFemaleMicUser(String roomId, String uid) {
        String key = getFemaleMicUser();
        try {
            mainCluster.opsForSet().add(key, getRoomIdUidComp(roomId, uid));
            mainCluster.expire(key, 30, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.error("addFemaleMicUser error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    public int getMaleMicUserCount() {
        String key = getMaleMicUser();
        try {
            Long size = mainCluster.opsForSet().size(key);
            return size != null ? size.intValue() : 0;
        } catch (Exception e) {
            logger.error("addMaleMicUser error. {}", e.getMessage(), e);
            return 0;
        }
    }

    private String getRoomIdUidComp(String roomId, String uid) {
        return roomId + "#" + uid;
    }

    private String getForbiddenKey(String roomId, String uid) {
        return roomId + "_" + uid;
    }

//    public void addExpire(String uid, int micFrameId, long endTime) {
//        expireRedisTemplate.opsForZSet().add(getExpireKey(), formatValue(uid, micFrameId, endTime), endTime);
//    }

    public void removeExpire(String uid, int micFrameId, long endTime) {
//        expireRedisTemplate.opsForZSet().remove(getExpireKey(), formatValue(uid, micFrameId, endTime));
    }


//    public void addVipExpire(String uid, int micFrameId, long endTime) {
//        expireRedisTemplate.opsForZSet().add(getVipExpireKey(), formatValue(uid, micFrameId, endTime), endTime);
//    }

    public void removeVipExpire(String uid, int micFrameId, long endTime) {
//        expireRedisTemplate.opsForZSet().remove(getVipExpireKey(), formatValue(uid, micFrameId, endTime));
    }

    private String formatValue(String uid, int micId, long endTime) {
        return uid + "_" + micId + "_" + endTime;
    }

    private String getNewForbiddenKey(String roomId, String uid) {
        return "str:forbidden:key:" + roomId + ":" + uid;
    }

    private String getExpireKey() {
        return "user_laba_mic_manage";
    }

    private String getVipExpireKey() {
        return "user_mic_frame_manager";
    }

    private String getMaleMicUser() {
        return "set:male_mic_user";
    }

    private String getFemaleMicUser() {
        return "set:famale_mic_user";
    }
}
