package com.quhong.config;

import com.quhong.net.cache.ActorMsgCache;
import com.quhong.net.sender.PlayerMsgSender;
import org.springframework.context.annotation.Bean;

/**
 * 消息缓存重发配置
 */
public class MsgSendConfig {
    @Bean
    public PlayerMsgSender getPlayerMsgSender(){
        return new PlayerMsgSender();
    }

    @Bean
    public ActorMsgCache getCacheActor(){
        return new ActorMsgCache();
    }
}
