package com.quhong.utils;

import com.quhong.cache.CacheMap;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.management.ManagementFactory;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class K8sUtils implements DisposableBean {
    private static final Logger logger = LoggerFactory.getLogger(K8sUtils.class);
    private CacheMap<String, Boolean> cacheMap;
    private static final String HOSTNAME = System.getenv("HOSTNAME");


    @Value("${spring.application.name}")
    public String applicationName;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainRedisTemplate;

    @PostConstruct
    public void init() {
        cacheMap = new CacheMap<>(5 * 60 * 1000L);
        if (StringUtils.isEmpty(applicationName)) {
            logger.error("spring.application.name is empty please check.");
        }
    }


    public boolean isMasterFromCache() {
        // jvm刚启动5分钟内不使用缓存
        if (System.currentTimeMillis() - ManagementFactory.getRuntimeMXBean().getStartTime() < 5 * 60 * 1000L) {
            return isMaster();
        }
        if (cacheMap.hasData(applicationName)) {
            return cacheMap.getData(applicationName);
        }
        boolean isMaster = isMaster();
        cacheMap.cacheData(applicationName, isMaster);
        return isMaster;
    }

    /**
     * 将第一个节点判断为Master
     */
    public boolean isMaster() {
        boolean isMaster = false;
        String key = getKey();
        String hostname = getHostname();
        Boolean setSuccess = mainRedisTemplate.opsForValue().setIfAbsent(key, hostname, 6, TimeUnit.MINUTES);
        if (setSuccess != null && setSuccess) {
            isMaster = true;
        } else {
            String masterNodeId = mainRedisTemplate.opsForValue().get(key);
            if (hostname.equals(masterNodeId)) {
                mainRedisTemplate.expire(key, 6, TimeUnit.MINUTES);
                isMaster = true;
            }
        }
        return isMaster;
    }


    public String getHostname() {
        if (!StringUtils.isEmpty(HOSTNAME)) {
            return HOSTNAME;
        }
        try {
            logger.info("getHostname using ipAddress.");
            return InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            throw new RuntimeException(e);
        }
    }

    public String getKey() {
        return "str:master:" + applicationName;
    }

    @Override
    public void destroy() {
        logger.info("k8sUtils destroying...");
        if (isMaster()) {
            String key = getKey();
            mainRedisTemplate.delete(key);
            logger.info("delete master key. {}", key);
        }
    }
}
