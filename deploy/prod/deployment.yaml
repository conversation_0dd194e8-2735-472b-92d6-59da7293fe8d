apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/name: ustar-java-admin
  name: ustar-java-admin # 不能用下划线
  namespace: devops
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: ustar-java-admin
  template:
    metadata:
      labels:
        app.kubernetes.io/name: ustar-java-admin
    spec:
      containers:
        - name: ustar-java-admin
          image: 239620982073.dkr.ecr.ap-south-1.amazonaws.com/ustar-java-admin:master_latest
          imagePullPolicy: Always
          args: ["-server",
                 "-jar",
                 "/opt/app.jar",
                 "--spring.profiles.active=prod"]
          env:
            - name: "JAVA_TOOL_OPTIONS"
              value: "-XX:+UseContainerSupport -XX:InitialRAMPercentage=30.0 -XX:MaxRAMPercentage=75.0 -XX:+UseG1GC -XX:ParallelGCThreads=2 -XX:ConcGCThreads=2"
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /system/health_check
              port: 8080
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /system/health_check
              port: 8080
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              memory: 1536Mi
            requests:
              memory: 1024Mi
          startupProbe:
            failureThreshold: 30
            httpGet:
              path: /system/health_check
              port: 8080
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          lifecycle:
            preStop:
              exec:
                command: [ "/bin/sh","-c","sleep 20" ]
          volumeMounts:
            - mountPath: /opt/config
              name: java-config
            - name: logpath
              mountPath: /opt/logs/
        - name: filebeat
          image: public.ecr.aws/elastic/filebeat:7.17.0-arm64
          imagePullPolicy: IfNotPresent
          resources:
            requests:
              memory: "100Mi"
              cpu: "10m"
            limits:
              cpu: "200m"
              memory: "300Mi"
          env:
            - name: MY_NODE_NAME # 获取node名称
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: MY_POD_NAME # 获取pod名称
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: MY_POD_NAMESPACE # 获取pod的namespace
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: MY_POD_IP # 获取pod IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: MY_DEPLOY_NAME # 获取DeployName
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['app.kubernetes.io/name']
          volumeMounts:
            - name: logpath
              mountPath: /data/log/app/
            - name: filebeatconf
              mountPath: /usr/share/filebeat/filebeat.yml
              subPath: usr/share/filebeat/filebeat.yml
      nodeSelector:
        kubernetes.io/os: linux
      restartPolicy: Always
      volumes:
        - name: java-config
          secret:
            defaultMode: 420
            secretName: java-prod-config
        - name: logpath
          emptyDir: {}
        - name: filebeatconf
          configMap:
            name: filebeatconf
            items:
              - key: filebeat.yml
                path: usr/share/filebeat/filebeat.yml
