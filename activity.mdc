---
description: 
globs: 
alwaysApply: false
---
# UStar Activity 项目开发规范 (.rules)

## 1. 项目结构规范

### 1.1 包结构
```
com.quhong/
├── config/          # 配置类
├── controllers/     # 控制器层
├── service/         # 业务逻辑层
├── data/           # 数据传输对象
├── mongo/          # MongoDB相关
├── mysql/          # MySQL相关
├── redis/          # Redis相关
├── consumer/       # 消息消费者
├── deliver/        # 消息投递
├── handler/        # 处理器
├── task/           # 定时任务
├── clients/        # 外部服务客户端
├── constant/       # 常量定义
├── enums/          # 枚举类
└── ActivityApplication.java  # 主启动类
```

### 1.2 资源文件结构
```
resources/
├── i18n/                    # 国际化文件
├── templates/               # 模板文件
├── application.yml          # 主配置文件
├── application-{env}.yml    # 环境配置文件
├── logback.xml             # 日志配置
├── spring-context.xml      # Spring上下文配置
└── *_config.yml            # 业务配置文件
```

## 2. 命名规范

### 2.1 包命名
- 全部小写
- 使用点分隔符
- 遵循reverse domain pattern

### 2.2 类命名
- 使用PascalCase（首字母大写的驼峰命名）
- Controller类以Controller结尾
- Service类以Service结尾
- Config类以Config结尾
- 常量类以Constant结尾

### 2.3 方法命名
- 使用camelCase（驼峰命名）
- 方法名要能清楚表达方法的功能
- 布尔型方法以is、has、can开头

### 2.4 变量命名
- 使用camelCase
- 常量使用UPPER_SNAKE_CASE
- 避免使用缩写

## 3. 代码规范

### 3.1 注解使用
- 控制器类必须使用@RestController或@Controller
- 服务类必须使用@Service
- 配置类必须使用@Configuration
- 异步方法使用@Async
- 定时任务使用@Scheduled

### 3.2 异常处理
- 统一使用全局异常处理
- 自定义异常必须继承RuntimeException
- 异常信息要明确具体


### 3.4 格式规范
- 不要硬编码，数据库表名，type类型的值，需要用常量

## 4. 数据库规范

### 4.1 MongoDB
- 集合命名使用snake_case
- 字段命名使用camelCase
- 索引命名要有意义

### 4.2 MySQL
- 表名使用snake_case
- 字段名使用snake_case
- 主键统一命名为id

### 4.3 Redis
- key命名使用冒号分隔：模块:功能:标识
- 设置合理的过期时间
- 避免大key

## 6. Git规范

### 6.1 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 6.2 Type类型
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式化
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

### 6.3 忽略文件
- 编译产物：*.class, target/
- IDE文件：.idea/, *.iml
- 日志文件：*.log
- 配置文件：/config/
- 打包文件：*.jar, *.war

## 7. 部署规范

### 7.1 构建
- 使用Maven进行构建
- 最终产物命名为app.jar
- 构建前执行测试

### 7.2 环境部署
- dev: 开发环境自动部署
- test: 测试环境手动部署
- prod: 生产环境严格审核后部署

## 8. 安全规范

### 8.1 敏感信息
- 数据库密码不允许硬编码
- API密钥使用环境变量
- 敏感日志信息要脱敏

### 8.2 接口安全
- 重要接口必须有权限验证
- 参数验证要完整
- 防止SQL注入和XSS攻击

## 9. 性能规范

### 9.1 数据库优化
- 合理使用索引
- 避免N+1查询
- 大数据量操作使用分页

### 9.2 缓存使用
- 热点数据使用Redis缓存
- 设置合理的缓存时间
- 缓存更新策略要明确

## 10. 测试规范

### 10.1 单元测试
- 核心业务逻辑必须有单元测试
- 测试覆盖率不低于80%
- 测试方法命名要清晰

## 11. 复杂活动开发规范

### 11.1 活动架构设计

#### 11.1.1 继承体系规范
```java
// Controller层继承体系
@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class {ActivityName}Controller extends H5Controller {
    // 统一返回格式
    private String createResult(HttpCode code, Object data);
}

// Service层继承体系  
@Service
public class {ActivityName}Service extends OtherActivityService implements TaskMsgHandler {
    // 实现消息队列处理接口
    void taskMsgProcess(CommonMqTopicData data);
}
```

#### 11.1.2 类命名规范
- 活动Controller：`{ActivityName}Controller`
- 活动Service：`{ActivityName}Service`  
- 活动VO：`{ActivityName}VO`
- 活动DTO：`{ActivityName}DTO`

### 11.2 常量定义规范

#### 11.2.1 活动基础常量
```java
public class ActivityService {
    // 活动ID - 必须定义，使用MongoDB ObjectId格式
    public static final String ACTIVITY_ID = "67cad462946370923ebcccf5";
    
    // 活动类型常量 - 使用递增整数，便于扩展
    public static final int GAME_TYPE = 1;
    public static final int MIC_TYPE = 2;
    public static final int SUB_TYPE = 3;
    
    // 业务限制常量 - 明确业务规则
    public static final int USER_SUB_LIMIT = 3;
    public static final int MAX_ADMIN_LIMIT = 20;
    
    // 积分等级 - 使用List定义递进关系
    private static final List<Integer> SCORE_LEVEL_LIST = 
        Arrays.asList(0, 500, 1000, 1500, 2000);
}
```

#### 11.2.2 Redis Key命名规范
```java
// 格式：活动ID:模块:功能:标识
private String getRoomHashDetailKey(String activityId, String dayStr) {
    return activityId + ":game_event:room:detail:" + dayStr;
}

private String getUserHashDetailKey(String activityId, String dayStr) {
    return activityId + ":game_event:user:detail:" + dayStr;
}

private String getZetTypeKey(String activityId) {
    return activityId + ":game_event:ranking";
}
```

### 11.3 数据结构规范

#### 11.3.1 配置数据初始化
```java
// 使用静态初始化块配置任务列表
private static final List<ActivityCommonConfig.QueenConfig> TASK_LIST = new ArrayList<>();

static {
    TASK_LIST.add(new ActivityCommonConfig.QueenConfig(
        0, "任务名称", "任务描述", "", "", 100, 20, 5, "eventDesc", "iconUrl"));
}
```

#### 11.3.2 映射关系定义
```java
// 使用Map定义复杂映射关系，提高可读性
public static final Map<Integer, List<String>> LEVEL_REWARD_MAP = 
    new HashMap<Integer, List<String>>() {{
        put(1, Arrays.asList("hostReward1", "adminReward1"));
        put(2, Arrays.asList("hostReward2", "adminReward2"));
    }};

// 国际化支持的映射
public static final Map<Integer, List<String>> TYPE_NAME_MAP = 
    new HashMap<Integer, List<String>>() {{
        put(1, Arrays.asList("Party", "الحفلة", "iconUrl"));
        put(2, Arrays.asList("Chat", "الدردشة", "iconUrl"));
    }};
```

### 11.4 接口设计规范

#### 11.4.1 Controller方法模板
```java
/**
 * 接口功能描述
 * @param activityId 活动ID
 * @param uid 用户ID
 * @param optionalParam 可选参数
 * @return 统一返回格式
 */
@RequestMapping("methodName")
private String methodName(@RequestParam String activityId, 
                         @RequestParam String uid,
                         @RequestParam(required = false) String optionalParam) {
    return createResult(HttpCode.SUCCESS, 
        activityService.businessMethod(activityId, uid, optionalParam));
}
```

#### 11.4.2 参数验证规范
- 活动ID和用户ID为必传参数
- 使用`@RequestParam(required = false)`标识可选参数
- 复杂参数使用`@RequestBody`接收DTO对象
- 在Service层进行详细的业务参数验证

### 11.5 业务逻辑规范

#### 11.5.1 并发控制规范
```java
// 使用Interner进行字符串池化，避免锁对象过多
private static final Interner<String> stringPool = Interners.newWeakInterner();

// 房间级别同步控制
synchronized (stringPool.intern(getLocalEventRoomKey(roomId))) {
    // 房间相关业务逻辑
    updateRoomData();
}

// 用户级别同步控制
synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
    // 用户相关业务逻辑
    updateUserData();
}
```

#### 11.5.2 缓存操作规范
```java
// 统一使用cacheDataService获取缓存数据
ActivityVO.ChallengeInfo challengeInfo = cacheDataService
    .getActivityChallengeInfo(totalInfoKey, roomId);

// 更新缓存数据，使用JSON序列化
activityCommonRedis.setCommonHashData(totalInfoKey, roomId, 
    JSONObject.toJSONString(challengeInfo));
```

#### 11.5.3 积分计算流程
```java
private void processScoreUpdate(String roomId, int type, CommonMqTopicData mqData) {
    // 1. 前置条件检查
    String eventId = validateConditions(roomId, type, mqData);
    if (eventId == null) return;
    
    // 2. 获取配置和当前状态
    ActivityConfig config = getActivityConfig(type);
    int currentCount = getCurrentCount(type);
    
    // 3. 计算积分变化
    if (currentCount < config.getMaxLimit()) {
        int addScore = config.getScorePerAction();
        updateScore(roomId, addScore);
        recordHistory(roomId, type, addScore);
    }
}
```

### 11.6 消息队列处理规范

#### 11.6.1 TaskMsgHandler实现模板
```java
@Override
public void taskMsgProcess(CommonMqTopicData data) {
    String item = data.getItem();
    
    // 1. 消息类型过滤
    if (!SUPPORTED_TASK_LIST.contains(item)) {
        return;
    }
    
    // 2. 活动状态检查
    if (!isActivityActive(ACTIVITY_ID)) {
        return;
    }
    
    // 3. 业务逻辑路由
    switch (item) {
        case CommonMqTaskConstant.ON_MIC_TIME:
            handleMicAction(data);
            break;
        case CommonMqTaskConstant.SUB_ROOM_EVENT:
            handleSubscribeAction(data);
            break;
        // 其他消息类型处理...
    }
}
```

#### 11.6.2 消息类型管理
```java
// 定义支持的消息类型
private static final List<String> SUPPORTED_TASK_LIST = Arrays.asList(
    CommonMqTaskConstant.WATCH_VIDEO_TIME,
    CommonMqTaskConstant.CREATE_VOTE,
    CommonMqTaskConstant.PLAY_DOMINO,
    CommonMqTaskConstant.ON_MIC_TIME,
    CommonMqTaskConstant.SUB_ROOM_EVENT
);
```

### 11.7 奖励系统规范

#### 11.7.1 奖励发放接口
```java
private void distributeReward(String uid, String rewardKey, int eventType) {
    String eventTitle = EVENT_TITLE_MAP.getOrDefault(eventType, "");
    resourceKeyHandlerService.sendResourceData(uid, rewardKey,
        eventTitle, eventTitle, eventTitle, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
}
```

#### 11.7.2 防重复发放机制
```java
// 使用设备ID绑定防止重复领取
String deviceId = getDeviceId(uid);
String bindKey = getRewardBindKey(activityId, rewardType);
String boundUid = activityCommonRedis.getCommonHashStrValue(bindKey, deviceId);

if (StringUtils.isEmpty(boundUid)) {
    // 发放奖励并绑定设备
    distributeReward(uid, rewardKey, eventType);
    activityCommonRedis.setCommonHashData(bindKey, deviceId, uid);
} else {
    throw new CommonH5Exception(ActivityHttpCode.REWARD_ALREADY_CLAIMED);
}
```

### 11.8 排行榜系统规范

#### 11.8.1 排行榜数据结构
```java
// 使用Redis ZSet实现排行榜
String rankingKey = getRankingKey(activityId);

// 获取排行榜数据
Map<String, Integer> rankingMap = activityCommonRedis
    .getCommonRankingMap(rankingKey, maxSize);

// 更新排行榜积分
activityCommonRedis.incrCommonZSetRankingScoreSimple(rankingKey, userId, addScore);
```

#### 11.8.2 多维度排行榜
```java
// 总排行榜
String totalRankingKey = getTotalRankingKey(activityId);

// 分类排行榜
String categoryRankingKey = getCategoryRankingKey(activityId, category);

// 时间段排行榜
String periodRankingKey = getPeriodRankingKey(activityId, period);
```

### 11.9 历史记录规范

#### 11.9.1 历史记录存储
```java
public void recordHistory(String userId, int actionType, int scoreChange, String detail) {
    String historyKey = getHistoryKey(activityId, userId, actionType);
    
    HistoryData historyData = new HistoryData();
    historyData.setScoreChange(scoreChange);
    historyData.setDetail(detail);
    historyData.setTimestamp(DateHelper.getNowSeconds());
    
    // 使用List存储，限制最大数量
    activityCommonRedis.leftPushAllCommonList(historyKey, 
        Arrays.asList(JSONObject.toJSONString(historyData)), MAX_HISTORY_SIZE);
}
```

#### 11.9.2 历史记录查询
```java
public List<HistoryData> getHistoryByPage(String userId, int actionType, 
                                         int page, int pageSize) {
    int start = (page - 1) * pageSize;
    int end = page * pageSize;
    String historyKey = getHistoryKey(activityId, userId, actionType);
    
    List<String> jsonList = activityCommonRedis
        .getCommonListPageRecord(historyKey, start, end);
    
    return jsonList.stream()
        .map(json -> JSON.parseObject(json, HistoryData.class))
        .collect(Collectors.toList());
}
```

### 11.10 测试和调试规范

#### 11.10.1 测试接口设计
```java
/**
 * 测试专用接口 - 仅在非生产环境启用
 */
@RequestMapping("testMethod")
private String testMethod(@RequestParam String uid, 
                         @RequestParam int cmd, 
                         @RequestParam int value) {
    if (ServerConfig.isProduct()) {
        throw new CommonH5Exception(ActivityHttpCode.FORBIDDEN);
    }
    return createResult(HttpCode.SUCCESS, activityService.testMethod(uid, cmd, value));
}
```

#### 11.10.2 环境区分处理
```java
@PostConstruct
public void init() {
    // 根据环境设置不同的配置
    if (ServerConfig.isNotProduct()) {
        QUESTION_GROUP_ID = "test_group_001";
        ACTIVITY_DURATION_DAYS = 7; // 测试环境缩短活动时间
    } else {
        QUESTION_GROUP_ID = "prod_group_001";
        ACTIVITY_DURATION_DAYS = 30;
    }
}

// 灰度测试用户过滤
private boolean isTestUser(String userId) {
    if (ServerConfig.isProduct() && ACTIVITY_TITLE.startsWith("test")) {
        return whiteTestDao.isMemberByType(userId, WhiteTestDao.WHITE_TYPE_USER);
    }
    return true;
}
```

### 11.11 日志和监控规范

#### 11.11.1 关键操作日志
```java
// 成功操作日志 - 包含关键业务字段
logger.info("activity_action_success: activityId={}, userId={}, actionType={}, " +
           "scoreChange={}, roomId={}, eventId={}", 
           activityId, userId, actionType, scoreChange, roomId, eventId);

// 业务异常日志 - 便于问题排查
logger.warn("activity_validation_failed: activityId={}, userId={}, reason={}, " +
           "requestData={}", activityId, userId, reason, JSON.toJSONString(requestData));

// 性能监控日志
logger.info("activity_performance: method={}, userId={}, costTime={}ms", 
           methodName, userId, costTime);
```

#### 11.11.2 埋点事件规范
```java
// 定义埋点事件映射
public static final Map<Integer, String> ANALYTICS_EVENT_MAP = 
    new HashMap<Integer, String>() {{
        put(1, "activity_level_reward_claimed");
        put(2, "activity_task_completed");
        put(3, "activity_ranking_reward_distributed");
    }};

// 埋点数据上报
private void reportAnalytics(String userId, int eventType, Map<String, Object> properties) {
    AnalyticsEvent event = new AnalyticsEvent();
    event.setUserId(userId);
    event.setEventName(ANALYTICS_EVENT_MAP.get(eventType));
    event.setProperties(properties);
    event.setTimestamp(System.currentTimeMillis());
    
    analyticsService.track(event);
}
```

### 11.12 性能优化规范

#### 11.12.1 批量操作优化
```java
// 批量更新用户数据
public void batchUpdateUserScores(Map<String, Integer> userScoreMap) {
    // 使用Pipeline减少Redis网络开销
    try (Jedis jedis = redisPool.getResource()) {
        Pipeline pipeline = jedis.pipelined();
        
        userScoreMap.forEach((userId, score) -> {
            String key = getUserScoreKey(userId);
            pipeline.incrBy(key, score);
        });
        
        pipeline.sync();
    }
}
```

#### 11.12.2 异步处理规范
```java
// 异步处理非核心业务逻辑
@Async("activityTaskExecutor")
public void asyncProcessReward(String userId, String rewardKey) {
    try {
        // 异步发放奖励
        distributeReward(userId, rewardKey);
        
        // 异步发送通知
        sendNotification(userId, rewardKey);
        
    } catch (Exception e) {
        logger.error("async_reward_process_failed: userId={}, rewardKey={}, error={}", 
                    userId, rewardKey, e.getMessage(), e);
    }
}
```

#### 11.12.3 缓存策略规范
```java
// 多级缓存策略
public ActivityData getActivityData(String activityId) {
    // 1. 本地缓存
    ActivityData data = localCache.get(activityId);
    if (data != null) {
        return data;
    }
    
    // 2. Redis缓存
    data = redisCache.get(getActivityCacheKey(activityId));
    if (data != null) {
        localCache.put(activityId, data, LOCAL_CACHE_TTL);
        return data;
    }
    
    // 3. 数据库查询
    data = activityDao.getById(activityId);
    if (data != null) {
        redisCache.set(getActivityCacheKey(activityId), data, REDIS_CACHE_TTL);
        localCache.put(activityId, data, LOCAL_CACHE_TTL);
    }
    
    return data;
}
```

### 11.13 国际化支持规范

#### 11.13.1 多语言消息处理
```java
// 根据用户语言设置返回对应文本
private String getLocalizedMessage(String userId, String messageKey) {
    ActorData actorData = actorDao.getActorDataFromCache(userId);
    int slang = actorData.getSlang();
    
    return slang == SLangType.ARABIC ? 
        arabicMessages.get(messageKey) : englishMessages.get(messageKey);
}

// 多语言配置Map
private static final Map<String, List<String>> LOCALIZED_MESSAGES = 
    new HashMap<String, List<String>>() {{
        put("welcome_message", Arrays.asList("Welcome to activity!", "مرحبا بك في النشاط!"));
        put("reward_claimed", Arrays.asList("Reward claimed successfully!", "تم استلام المكافأة بنجاح!"));
    }};
```

#### 11.13.2 时区处理规范
```java
// 统一使用阿拉伯时区进行时间计算
private String getCurrentDay(String userId) {
    // 获取用户时区偏移量或使用默认阿拉伯时区
    return DateHelper.ARABIAN.formatDateInDay(new Date());
}

// 活动时间判断考虑时区
private boolean isInActivityTime(String activityId, String userId) {
    ActivityData activityData = getActivityData(activityId);
    int currentTime = DateHelper.getNowSeconds();
    
    return currentTime >= activityData.getStartTime() && 
           currentTime <= activityData.getEndTime();
}
```

## 12. MySQL数据层开发规范

### 12.1 包结构规范

#### 12.1.1 MySQL包目录结构
```
com.quhong.mysql/
├── dao/          # DAO层 - 数据访问对象
├── data/         # 数据实体类 - 对应数据库表
└── mapper/       # Mapper接口层
    ├── ustar/    # 主业务数据库映射
    └── ustar_log/ # 日志数据库映射
```

#### 12.1.2 层次职责划分
- **Data类**：纯数据实体，对应数据库表结构
- **Mapper接口**：数据库操作接口，继承BaseMapper
- **DAO类**：业务数据访问层，封装复杂查询逻辑

### 12.2 数据实体类(Data)规范

#### 12.2.1 基础规范
```java
/**
 * 数据表注释说明
 * 
 * <AUTHOR>
 * @date 创建日期
 */
@TableName("t_table_name")
public class TableNameData {
    
    /**
     * 主键ID - 统一使用AUTO自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 字段注释 - 必须添加业务说明
     */
    private String fieldName;
    
    /**
     * 时间字段统一使用Integer类型存储时间戳
     */
    private Integer ctime;
    
    // getter和setter方法...
}
```

#### 12.2.2 字段命名规范
- **主键**：统一命名为`id`，类型为`Integer`，使用`@TableId(type = IdType.AUTO)`
- **时间字段**：创建时间`ctime`，修改时间`mtime`，使用`Integer`存储时间戳
- **用户ID**：统一命名为`uid`，类型为`String`
- **房间ID**：统一命名为`roomId`，类型为`String`
- **活动ID**：统一命名为`activityId`，类型为`Integer`
- **状态字段**：使用`status`，类型为`Integer`，需注释状态值含义

#### 12.2.3 特殊字段处理
```java
public class ActivityData {
    
    /**
     * 状态字段需注释各状态值含义
     * 状态 0进行中 1自动结束 2手动结束
     */
    private Integer status;
    
    /**
     * 类型字段需注释各类型值含义  
     * 投票类型 0礼物投票 1问答投票
     */
    private Integer type;
    
    /**
     * JSON字段使用String存储
     * 选项内容 - JSON格式存储
     */
    private String optionContent;
    
    /**
     * 多语言字段分别定义
     * 英文标题
     */
    private String titleEn;
    
    /**
     * 阿语标题
     */
    private String titleAr;
}
```

### 12.3 Mapper接口规范

#### 12.3.1 基础Mapper
```java
/**
 * 表操作Mapper接口
 * 
 * <AUTHOR>
 * @date 创建日期
 */
public interface TableNameMapper extends BaseMapper<TableNameData> {
    
    // 继承BaseMapper即可获得基础CRUD操作
    // 特殊查询使用@Select、@Insert、@Update、@Delete注解
}
```

#### 12.3.2 复杂查询Mapper
```java
public interface RoomEventSubMapper {
    
    /**
     * 分表插入 - 使用动态表名
     */
    @Insert("insert into ${tableName} (`event_id`,`uid`,`is_robot`,`start_time`,`end_time`,`ctime`) " +
            "values (#{item.eventId},#{item.uid},#{item.isRobot},#{item.startTime},#{item.endTime},#{item.ctime})")
    void insert(@Param("tableName") String tableName, @Param("item") RoomEventSubData data);
    
    /**
     * 复杂查询 - 联合查询多个分表
     */
    @Select("SELECT event_id as eventId FROM ( " +
            "SELECT id,event_id,uid FROM t_room_event_sub_0 where uid=#{uid} UNION ALL " +
            "SELECT id,event_id,uid FROM t_room_event_sub_1 where uid=#{uid} " +
            ") as stat order by start_time limit #{offset},#{pageSize}")
    List<Integer> getMineSubList(@Param("uid") String uid, @Param("offset") int offset, @Param("pageSize") int pageSize);
}
```

### 12.4 DAO层开发规范

#### 12.4.1 基础DAO结构
```java
/**
 * 表数据访问对象
 * 
 * <AUTHOR>
 * @date 创建日期
 */
@Component
public class TableNameDao {
    
    private static final Logger logger = LoggerFactory.getLogger(TableNameDao.class);
    
    @Resource
    private TableNameMapper tableNameMapper;
    
    /**
     * 基础查询方法
     */
    public TableNameData selectOne(Integer id) {
        QueryWrapper<TableNameData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return tableNameMapper.selectOne(queryWrapper);
    }
    
    /**
     * 条件查询方法
     */
    public List<TableNameData> selectByCondition(String uid, Integer status) {
        QueryWrapper<TableNameData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        queryWrapper.orderByDesc("ctime");
        return tableNameMapper.selectList(queryWrapper);
    }
}
```

#### 12.4.2 缓存集成规范
```java
@Component
@Lazy
public class QuizResultDao {
    
    private static final Logger logger = LoggerFactory.getLogger(QuizResultDao.class);
    
    // 缓存配置
    private final CacheMap<Integer, List<QuizResultData>> cacheMap;
    private static final long CACHE_TIME_MILLIS = 5 * 60 * 1000L;
    
    @Resource
    private QuizResultMapper quizResultMapper;
    
    public QuizResultDao() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }
    
    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }
    
    /**
     * 带缓存的查询方法
     */
    public List<QuizResultData> selectTopTenRanking(Integer activityId) {
        // 1. 尝试从缓存获取
        List<QuizResultData> dataList = cacheMap.getData(activityId);
        if (!CollectionUtils.isEmpty(dataList)) {
            return new ArrayList<>(dataList);
        }
        
        // 2. 缓存未命中，查询数据库
        dataList = selectTopTenList(activityId);
        if (!CollectionUtils.isEmpty(dataList)) {
            cacheMap.cacheData(activityId, dataList);
        }
        
        return dataList;
    }
}
```

#### 12.4.3 分页查询规范
```java
/**
 * 分页查询方法
 */
public List<VoteData> getVoteRecordPage(int type, String roomId, int timestamp, int page, int pageSize) {
    IPage<VoteData> dataPage = new Page<>(page, pageSize);
    QueryWrapper<VoteData> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("type", type);
    queryWrapper.eq("room_id", roomId);
    queryWrapper.lt("end_time", DateHelper.getNowSeconds());
    queryWrapper.gt("ctime", timestamp);
    queryWrapper.orderByDesc("ctime");
    
    dataPage = voteMapper.selectPage(dataPage, queryWrapper);
    return dataPage.getRecords();
}
```

### 12.5 分表处理规范

#### 12.5.1 分表策略
```java
@Component
public class RoomEventSubDao {
    
    private static final String TABLE_PRE = "t_room_event_sub";
    
    /**
     * 根据业务ID计算分表名
     */
    private String getTableName(int eventId) {
        String strEventId = String.valueOf(eventId);
        int index = Integer.parseInt(strEventId.substring(strEventId.length() - 1));
        return TABLE_PRE + "_" + index;
    }
    
    /**
     * 分表插入
     */
    public void insert(RoomEventSubData data) {
        roomEventSubMapper.insert(getTableName(data.getEventId()), data);
    }
    
    /**
     * 分表查询
     */
    public RoomEventSubData selectOne(int eventId, String uid) {
        return roomEventSubMapper.selectOne(getTableName(eventId), eventId, uid);
    }
}
```

### 12.6 查询构建规范

#### 12.6.1 QueryWrapper使用规范
```java
/**
 * 复杂条件查询构建
 */
private QueryWrapper<RoomEventData> buildQueryWrapper(RoomEventDTO dto) {
    QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
    
    // 基础条件
    queryWrapper.eq("room_id", dto.getRoomId());
    
    // 可选条件判断
    if (dto.getStatus() != null) {
        queryWrapper.eq("status", dto.getStatus());
    }
    
    // 时间范围查询
    if (dto.getStartTime() != null) {
        queryWrapper.ge("start_time", dto.getStartTime());
    }
    
    if (dto.getEndTime() != null) {
        queryWrapper.le("end_time", dto.getEndTime());
    }
    
    // 复杂条件 - OR查询
    queryWrapper.and(wrapper -> wrapper
        .lt("start_time", startTime).gt("end_time", startTime)
        .or()
        .lt("start_time", endTime).gt("end_time", endTime)
    );
    
    // 排序
    queryWrapper.orderByDesc("ctime");
    
    // 限制查询数量
    queryWrapper.last("limit 10");
    
    return queryWrapper;
}
```

#### 12.6.2 动态查询规范
```java
public List<QuestionData> selectListPage(String gid, Integer page, Integer pageSize) {
    QueryWrapper<QuestionData> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("gid", gid);
    queryWrapper.orderByAsc("id");
    
    // 使用MyBatis-Plus分页
    IPage<QuestionData> recordPage = new Page<>(page, pageSize);
    IPage<QuestionData> iPage = questionMapper.selectPage(recordPage, queryWrapper);
    
    return iPage.getRecords();
}
```

### 12.7 数据更新规范

#### 12.7.1 单条更新
```java
/**
 * 根据ID更新实体
 */
public void update(VoteData voteData) {
    voteMapper.updateById(voteData);
}

/**
 * 条件更新
 */
public void updateByCondition(RoomEventData data) {
    UpdateWrapper<RoomEventData> updateWrapper = new UpdateWrapper<>();
    updateWrapper.eq("id", data.getId());
    updateWrapper.set("sub_num", data.getSubNum());
    roomEventMapper.update(data, updateWrapper);
}
```

#### 12.7.2 批量操作
```java
/**
 * 批量删除
 */
public void batchDeleteByEventId(Integer eventId) {
    roomEventSubMapper.batchDeleteByEventId(getTableName(eventId), eventId);
}

/**
 * 批量查询
 */
public List<RoomEventData> selectList(List<Integer> eventIds) {
    QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
    queryWrapper.in("id", eventIds);
    queryWrapper.orderByAsc("start_time");
    return roomEventMapper.selectList(queryWrapper);
}
```

### 12.8 异常处理规范

#### 12.8.1 数据库操作异常处理
```java
/**
 * 插入操作异常处理
 */
public void insert(QuizResultData data) {
    try {
        quizResultMapper.insert(data);
        // 更新相关缓存
        if (isBetterThanTenth(data)) {
            updateCacheMap(data.getActivityId());
        }
    } catch (Exception e) {
        logger.error("insert quiz result data error: activityId={}, uid={}, error={}", 
                    data.getActivityId(), data.getUid(), e.getMessage(), e);
        // 根据业务需要决定是否重抛异常
        throw new DatabaseOperationException("数据插入失败", e);
    }
}
```

### 12.9 性能优化规范

#### 12.9.1 查询优化
```java
/**
 * 避免N+1查询 - 使用批量查询
 */
public Map<Integer, RoomEventData> getRoomEventMap(List<Integer> eventIds) {
    if (CollectionUtils.isEmpty(eventIds)) {
        return Collections.emptyMap();
    }
    
    List<RoomEventData> eventList = selectList(eventIds);
    return eventList.stream()
        .collect(Collectors.toMap(RoomEventData::getId, Function.identity()));
}

/**
 * 大数据量查询使用流式处理
 */
public void processLargeDataSet(String condition, Consumer<RoomEventData> processor) {
    QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("status", condition);
    
    // 分批处理大量数据
    int pageSize = 1000;
    int currentPage = 1;
    
    while (true) {
        IPage<RoomEventData> page = new Page<>(currentPage, pageSize);
        page = roomEventMapper.selectPage(page, queryWrapper);
        
        if (CollectionUtils.isEmpty(page.getRecords())) {
            break;
        }
        
        page.getRecords().forEach(processor);
        currentPage++;
    }
}
```

#### 12.9.2 索引使用建议
```java
/**
 * 查询方法需要考虑数据库索引
 * 建议索引：
 * - 主键id (自动创建)
 * - uid (用户相关查询)
 * - room_id (房间相关查询) 
 * - activity_id (活动相关查询)
 * - ctime (时间排序查询)
 * - 复合索引: (room_id, start_time) 用于房间活动时间查询
 */

// 良好的索引使用示例
public RoomEventData getOngoingRoomEvent(String roomId, int nowTime) {
    QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("room_id", roomId);        // 使用room_id索引
    queryWrapper.le("start_time", nowTime);    // 结合时间条件，可用复合索引
    queryWrapper.ge("end_time", nowTime);
    queryWrapper.last("limit 1");
    return roomEventMapper.selectOne(queryWrapper);
}
```

### 12.10 国际化数据处理

#### 12.10.1 多语言字段设计
```java
@TableName("t_question")
public class QuestionData {
    
    /**
     * 问题内容 - 支持多语言的JSON格式
     * 或者分别定义英文和阿语字段
     */
    private String content;
    
    /**
     * 英文标题
     */
    private String titleEn;
    
    /**
     * 阿语标题  
     */
    private String titleAr;
    
    /**
     * 根据语言类型获取标题
     */
    public String getTitle(int slang) {
        return slang == SLangType.ARABIC ? titleAr : titleEn;
    }
}
```

#### 12.10.2 多语言查询处理
```java
/**
 * 根据用户语言查询数据
 */
public List<QuestionData> getQuestionsByLanguage(String gid, int slang) {
    QueryWrapper<QuestionData> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("gid", gid);
    
    // 可以根据语言类型选择不同的题库
    if (slang == SLangType.ARABIC) {
        queryWrapper.eq("lang", 1);
    } else {
        queryWrapper.eq("lang", 0);
    }
    
    queryWrapper.orderByAsc("ctime");
    return questionMapper.selectList(queryWrapper);
}
```



这个MySQL开发规范基于`com.quhong.mysql`包的实际代码结构制定，涵盖了数据实体类设计、Mapper接口规范、DAO层开发、分表处理、查询优化、异常处理和国际化支持等完整的数据层开发标准。